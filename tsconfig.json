{"compilerOptions": {"target": "es5", "module": "esnext", "jsx": "react-jsx", "noEmit": false, "moduleResolution": "node", "sourceMap": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "removeComments": false, "outDir": "dist", "allowUnusedLabels": false, "allowSyntheticDefaultImports": true, "noImplicitReturns": true, "noUnusedParameters": true, "noUnusedLocals": true, "lib": ["es7", "DOM"], "pretty": true, "typeRoots": ["node_modules/@types"], "baseUrl": ".", "paths": {"@api/*": ["src/api/*"], "@assets/*": ["src/assets/*"], "@shared/*": ["src/shared/*"], "@common/*": ["src/common/*"], "@components/*": ["src/components/*"], "@hooks/*": ["src/hooks/*"], "types/*": ["src/types/*"], "@store/*": ["src/store/*"], "@styles/*": ["src/styles/*"]}}, "ts-node": {"compilerOptions": {"module": "CommonJS", "target": "es5", "esModuleInterop": true}}, "include": ["src"], "exclude": ["node_modules"], "compileOnSave": false, "buildOnSave": false}