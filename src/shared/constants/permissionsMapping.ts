export const Permissions = {
    UserList: 'User.getList',
    UserGet: 'User.get',
    UserUpdate: 'User.update',
    UserCreate: 'User.create',
    UserRestore: 'User.restore',
    UserDelete: 'User.delete',
    UserCurrent: 'User.current',
    UserBan: 'User.ban',
    UserUnban: 'User.unban',
    UserChangeRole: 'User.changeRole',

    PermissionCategoryList: 'PermissionCategory.getList',
    PermissionCategoryGet: 'PermissionCategory.get',
    PermissionCategoryUpdate: 'PermissionCategory.update',
    PermissionCategoryCreate: 'PermissionCategory.create',
    PermissionCategoryRestore: 'PermissionCategory.restore',
    PermissionCategoryDelete: 'PermissionCategory.delete',

    PermissionList: 'Permission.getList',
    PermissionGet: 'Permission.get',
    PermissionUpdate: 'Permission.update',
    PermissionCreate: 'Permission.create',
    PermissionRestore: 'Permission.restore',
    PermissionDelete: 'Permission.delete',

    RoleList: 'Role.getList',
    RoleGet: 'Role.get',
    RoleUpdate: 'Role.update',
    RoleCreate: 'Role.create',
    RoleSyncPermissions: 'Role.syncPermission',

    FilterList: 'Filter.getList',
    FilterGet: 'Filter.get',
    FilterCreate: 'Filter.create',
    FilterUpdate: 'Filter.update',
    FilterDelete: 'Filter.delete',
    FilterRestore: 'Filter.restore',
    FilterBulkAdd: 'Filter.bulkAdd',
    FilterBulkResult: 'Filter.getBulkResult',

    InvitationList: 'Invitation.getList',
    InvitationGet: 'Invitation.get',
    InvitationUpdate: 'Invitation.update',
    InvitationCreate: 'Invitation.create',
    InvitationRestore: 'Invitation.restore',
    InvitationDelete: 'Invitation.delete',
    InvitationResendEmails: 'Invitation.resendEmails',
    InvitationBulkAdd: 'Invitation.bulkAdd',
    InvitationBulkResult: 'Invitation.getBulkResult',

    NotificationList: 'Notification.getList',
    NotificationGet: 'Notification.get',

    ChatList: 'Chat.getList',
    ChatGet: 'Chat.get',
    ChatUpdate: 'Chat.update',
    ChatCreate: 'Chat.create',
    ChatRestore: 'Chat.restore',
    ChatDelete: 'Chat.delete',
    ChatRegisterSocket: 'Chat.regeisterSocket',
    ChatAuth: 'Chat.authChat',
    ChatWithManager: 'Chat.chatWithManager',

    UserChatList: 'UserChat.getList',
    UserChatBan: 'UserChat.ban',
    UserChatUnban: 'UserChat.unban',
    UserChatMakeAdmin: 'UserChat.makeAdmin',
    UserChatUnmakeAdmin: 'UserChat.unmakeAdmin',
    UserChatCreate: 'UserChat.create',

    ChatMessageList: 'ChatMessage.getList',
    ChatMessageGet: 'ChatMessage.get',
    ChatMessageSend: 'ChatMessage.sendMessage',

    SimulationList: 'Simulation.getList',
    SimulationGet: 'Simulation.get',
    SimulationUpdate: 'Simulation.update',
    SimulationCreate: 'Simulation.create',
    SimulationRestore: 'Simulation.restore',
    SimulationDelete: 'Simulation.delete',
    SimulationTest: 'Simulation.test',
    SimulationBulkAdd: 'Simulation.bulkAdd',
    SimulationBulkResult: 'Simulation.getBulkResult',

    SimulationTaskList: 'SimulationTask.getList',
    SimulationTaskGet: 'SimulationTask.get',
    SimulationTaskUpdate: 'SimulationTask.update',
    SimulationTaskCreate: 'SimulationTask.create',
    SimulationTaskRestore: 'SimulationTask.restore',
    SimulationTaskDelete: 'SimulationTask.delete',
    SimulationTaskBulkAdd: 'SimulationTask.bulkAdd',
    SimulationTaskBulkResult: 'SimulationTask.getBulkResult',

    SimulationWorkerList: 'SimulationWorker.getList',
    SimulationWorkerGet: 'SimulationWorker.get',
    SimulationWorkerUpdate: 'SimulationWorker.update',
    SimulationWorkerCreate: 'SimulationWorker.create',
    SimulationWorkerRestore: 'SimulationWorker.restore',
    SimulationWorkerDelete: 'SimulationWorker.delete',
    SimulationWorkerBulkAdd: 'SimulationWorker.bulkAdd',
    SimulationWorkerBulkResult: 'SimulationWorker.getBulkResult',

    SimulationWorkerScheduleEventList: 'SimulationWorkerScheduleEvent.getList',
    SimulationWorkerScheduleEventGet: 'SimulationWorkerScheduleEvent.get',
    SimulationWorkerScheduleEventUpdate: 'SimulationWorkerScheduleEvent.update',
    SimulationWorkerScheduleEventCreate: 'SimulationWorkerScheduleEvent.create',
    SimulationWorkerScheduleEventRestore: 'SimulationWorkerScheduleEvent.restore',
    SimulationWorkerScheduleEventDelete: 'SimulationWorkerScheduleEvent.delete',
    SimulationWorkerScheduleEventBulkAdd: 'SimulationWorkerScheduleEvent.bulkAdd',
    SimulationWorkerScheduleEventBulkResult: 'SimulationWorkerScheduleEvent.getBulkResult',

    SimulationScheduleEventList: 'SimulationScheduleEvent.getList',
    SimulationScheduleEventGet: 'SimulationScheduleEvent.get',
    SimulationScheduleEventUpdate: 'SimulationScheduleEvent.update',
    SimulationScheduleEventCreate: 'SimulationScheduleEvent.create',
    SimulationScheduleEventDelete: 'SimulationScheduleEvent.delete',
    SimulationScheduleEventBulkAdd: 'SimulationScheduleEvent.bulkAdd',
    SimulationScheduleEventBulkResult: 'SimulationScheduleEvent.getBulkResult',

    SimulationScheduleEventTypeList: 'SimulationScheduleEventType.getList',
    SimulationScheduleEventTypeGet: 'SimulationScheduleEventType.get',
    SimulationScheduleEventTypeCreate: 'SimulationScheduleEventType.create',
    SimulationScheduleEventTypeUpdate: 'SimulationScheduleEventType.update',
    SimulationScheduleEventTypeDelete: 'SimulationScheduleEventType.delete',
    SimulationScheduleEventTypeRestore: 'SimulationScheduleEventType.restore',

    SimulationEventList: 'SimulationEvent.getList',
    SimulationEventGet: 'SimulationEvent.get',
    SimulationEventUpdate: 'SimulationEvent.update',
    SimulationEventCreate: 'SimulationEvent.create',
    SimulationEventRestore: 'SimulationEvent.restore',
    SimulationEventDelete: 'SimulationEvent.delete',

    SessionList: 'Session.getList',
    SessionGet: 'Session.get',
    SessionUpdate: 'Session.update',
    SessionCreate: 'Session.create',
    SessionDelete: 'Session.delete',

    SessionAssignmentList: 'SessionAssignment.getList',
    SessionAssignmentGet: 'SessionAssignment.get',
    SessionAssignmentCreate: 'SessionAssignment.create',
    SessionAssignmentDelete: 'SessionAssignment.delete',
    SessionAssignmentStart: 'SessionAssignment.start',
    SessionAssignmentPause: 'SessionAssignment.pause',
    SessionAssignmentResume: 'SessionAssignment.resume',
    SessionAssignmentStop: 'SessionAssignment.stop',
    SessionAssignmentFinish: 'SessionAssignment.finish',
    SessionAssignmentBulkAdd: 'SessionAssignment.bulkAdd',
    SessionAssignmentBulkResult: 'SessionAssignment.getBulkResult',

    SessionAssignmentInfo: 'SessionAssignmentInfo.info',
    SessionAssignmentInfoCWT: 'SessionAssignmentInfo.currentWorkerTasks',
    SessionAssignmentInfoCTW: 'SessionAssignmentInfo.currentTaskWorkers',
    SessionAssignmentInfoWorkers: 'SessionAssignmentInfo.workerExtendedList',
    SessionAssignmentInfoTasks: 'SessionAssignmentInfo.taskExtendedList',
    SessionAssignmentInfoSimulation: 'SessionAssignmentInfo.simulationInfo',
    SessionAssignmentInfoTaskProgress: 'SessionAssignmentInfo.taskProgressesList',

    SessionWorkerAssignmentList: 'SessionWorkerAssignment.getList',
    SessionWorkerAssignmentGet: 'SessionWorkerAssignment.get',
    SessionWorkerAssignmentAssign: 'SessionWorkerAssignment.assign',
    SessionWorkerAssignmentCancel: 'SessionWorkerAssignment.cancel',
    SessionWorkerAssignmentPrioritize: 'SessionWorkerAssignment.prioritize',
    SessionWorkerAssignmentBulkAdd: 'SessionWorkerAssignment.bulkAdd',
    SessionWorkerAssignmentBulkResult: 'SessionWorkerAssignment.getBulkResult',
};
