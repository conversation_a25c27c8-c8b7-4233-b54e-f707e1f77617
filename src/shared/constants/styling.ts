import { COLORS } from '@shared/config/colors';

export const LIST_GRID_SETTINGS = {
    screenXS: 480,
    screenXSMax: 480 * 2 + 16 - 1,
    screenXSMin: 480,
    screenSM: 480 * 2 + 16,
    screenSMMax: 480 * 3 + 16 * 2 - 1,
    screenSMMin: 480 * 2 + 16,
    screenMD: 480 * 3 + 16 * 2,
    screenMDMax: 480 * 4 + 16 * 3 - 1,
    screenMDMin: 480 * 3 + 16 * 2,
    screenLG: 480 * 4 + 16 * 3,
    screenLGMax: 480 * 5 + 16 * 4 - 1,
    screenLGMin: 480 * 4 + 16 * 3,
    screenXL: 480 * 5 + 16 * 4,
    screenXLMax: 480 * 6 + 16 * 5 - 1,
    screenXLMin: 480 * 5 + 16 * 4,
    screenXXL: 480 * 6 + 16 * 5,
    screenXXLMin: 480 * 6 + 16 * 5,
};

export const LIST_GRID_COLS = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
    xxl: 6,
};

export const TABLE_STATUSES = {
    accepted: {
        color: COLORS.ACCENT.COLD[800],
        text: 'Принято',
    },
    ban: {
        color: COLORS.ERROR.WARM[300],
        text: 'Бан',
    },
    deleted: {
        color: COLORS.ERROR.COLD[300],
        text: 'Удален(а)',
    },
    finished: {
        color: COLORS.ACCENT.COLD[800],
        text: 'Завершено',
    },
    new: {
        color: COLORS.SUCCESS.WARM[300],
        text: 'Новое',
    },
    'no-user': {
        color: COLORS.ERROR.COLD[300],
        text: 'Не выбран пользователь',
    },
    paused: {
        color: COLORS.ACCENT.COLD[500],
        text: 'Пауза',
    },
    prestarted: {
        color: COLORS.ACCENT.COLD[300],
        text: 'Предстарт',
    },
    restored: {
        color: COLORS.SUCCESS.WARM[300],
        text: 'Восстановлен(а)',
    },
    resumed: {
        color: COLORS.ACCENT.COLD[600],
        text: 'Продолжено',
    },
    'role-change': {
        color: COLORS.WARNING.WARM[300],
        text: 'Смена роли',
    },
    sent: {
        color: COLORS.ACCENT.COLD[400],
        text: 'Отправлено',
    },
    started: {
        color: COLORS.ACCENT.COLD[400],
        text: 'Идёт',
    },
    stopped: {
        color: COLORS.ACCENT.COLD[700],
        text: 'Стоп',
    },
    unban: {
        color: COLORS.SUCCESS.COLD[300],
        text: 'Разбан',
    },
    unfilled: {
        color: COLORS.ERROR.COLD[300],
        text: 'Не заполнен(а)',
    },
    updated: {
        color: COLORS.ACCENT.WARM[500],
        text: 'Изменен(а)',
    },
};
