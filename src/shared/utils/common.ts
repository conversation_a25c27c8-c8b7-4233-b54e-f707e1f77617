import { message } from 'antd';
import dayjs from 'dayjs';
import { TUser } from 'types/user/user';
import { TInvitation } from 'types/invitations/invitation';
import { getConnectionCredentials } from '@shared/auth/sessions';
import { CONFIG } from '@shared/config/config';

export function isNullOrUndefined(value?: unknown) {
    return value === null || value === undefined;
}

export function isNullOrEmptyString(value?: unknown) {
    return value === null || value === '';
}

export async function clipboardCopy(textToCopy: string, textToShow?: string) {
    try {
        await navigator.clipboard.writeText(textToCopy);
        if (!isNullOrEmptyString(textToShow)) {
            message.info(textToShow);
        }
    } catch (error) {
        message.error('Не удалось скопировать');
        console.error(error);
    }
}

export function isUserConnected() {
    return getConnectionCredentials()?.accessToken != null;
}

export function weekdayNumToShortString(day: number) {
    if (day < 0 || day > 6) return 'out-of-bounds[0;6]';

    const weekdays = [
        'Вс',
        'Пн',
        'Вт',
        'Ср',
        'Чт',
        'Пт',
        'Сб',
    ];

    return weekdays[day];
}

export function formatDateString(
    ds: string | null,
    mode: 'hms-dmy' | 'dmy' | 'dm' | 'hms' | 'hm' = 'hms-dmy',
) {
    if (!ds) return '-';

    const formatMap = {
        'hms-dmy': 'HH:mm:ss DD.MM.YYYY',
        dmy: 'DD.MM.YYYY',
        dm: 'DD.MM',
        hms: 'HH:mm:ss',
        hm: 'HH:mm',
    };

    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return dayjs.utc(ds).tz(timezone).format(formatMap[mode]);
}

export function makeRoleName(role: TUser['role']) {
    const roleMap = {
        'Roles.Admin': 'Администратор',
        'Roles.Architect': 'Архитектор',
        'Roles.Manager': 'Менеджер',
        'Roles.Client': 'Клиент',
    };
    return roleMap[role] || role;
}

export function dateNowString() {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return dayjs.utc().tz(timezone).format(CONFIG.DATE_TIME_FORMAT);
}

export function shouldTextBeDark(hex: string): boolean {
    const value: string = hex;
    const r = Number.parseInt(value.slice(1, 2), 16);
    const g = Number.parseInt(value.slice(3, 4), 16);
    const b = Number.parseInt(value.slice(5, 6), 16);

    // https://stackoverflow.com/questions/3942878/how-to-decide-font-color-in-white-or-black-depending-on-background-color
    let tr = r / 255.0;
    tr = tr <= 0.04045 ? tr / 12.92 : ((tr + 0.055) / 1.055) ^ 2.4;
    let tg = g / 255.0;
    tg = tg <= 0.04045 ? tg / 12.92 : ((tg + 0.055) / 1.055) ^ 2.4;
    let tb = b / 255.0;
    tb = tb <= 0.04045 ? tb / 12.92 : ((tb + 0.055) / 1.055) ^ 2.4;
    const luminosity = 0.2126 * tr + 0.7152 * tg + 0.0722 * tb;
    const useDark = luminosity > 0.179;

    return useDark;
}

export function makeInviteLink(inviteId: TInvitation['id']): string {
    const baseUrl = `http://${CONFIG.SOCKET_URL}:81`;
    return `${baseUrl}/invite/${inviteId}`;
}

export function debounce<T extends (...args: unknown[]) => void>(fn: T, ms: number) {
    let timer: NodeJS.Timeout | null = null;

    return (...args: Parameters<T>): void => {
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            fn(...args);
        }, ms);
    };
}
