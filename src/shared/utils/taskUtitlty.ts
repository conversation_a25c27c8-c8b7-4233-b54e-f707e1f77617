import { GanttUseCases, TSimTaskGanttExtend } from '@components/gantt/gantt';
import { rootStore } from 'src/store/instanse';
import _ from 'lodash';
import { IngameTimeSettings } from 'types/ingame';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { TSessionTaskProgress } from 'types/session/sessionTaskProgress';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { TSimulation } from 'types/simulation/simulation';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSimWorker } from 'types/simulation/simulationWorker';

/** Промежуточный тип для задач-узлов Ганта */
export type TaskGantt = {
    /** Столбец, дробная позиция старта день+тик (от 0) */
    column: number;
    /** Текущая, дробная продолжительность в днях */
    curDuration: TSimTaskGanttExtend['curDuration'];
    /** Заложенный архитектором бюджет */
    estBudget: TSimTask['est_budget'];
    /** Столбец планового старта */
    estColumn: number;
    /** Заложенная архитектором продолжительность */
    estDuration: TSimTask['est_duration'];
    /** Сколько по архитектору оптимальное кол-во ПШЕ */
    estWorkers: TSimTask['est_workers'];
    /** Следующие за текущей задачи */
    following: TSimTask['following'];
    /** ID задачи */
    id: TSimTask['id'];
    /** Крит. путь до задачи, текущий */
    maxLengthPath: TSimTask['id'][];
    /** Длина крит. пути до задачи, текущая */
    maxLengthValue: number;
    /** Маркер критической задачи (по крит. пути) */
    milestone: TSimTask['milestone'];
    /** Название задачи */
    name: TSimTask['name'];
    /** Предыдущие текущей задачи */
    previous: TSimTask['previous'];
    /** Прогресс задачи в трудочасах */
    progress: number;
    /** Строка задачи, она же порядок */
    row: number;
    /** Запас - slack до след. крит (прямо или косвенно, часов, от 0) */
    slack?: number;
    /** Требования характеристик задачи */
    statsReq: TSimTask['stats_req'];
    /** UID задачи */
    uid: TSimTask['uid'];
    /** ПШЕ задачи */
    workers: TSimWorker[] | TSessionWorkerExtended[];
};

/** Параметры для подготовки данных Ганта */
export type TaskToGanttParams = {
    /** Часов в рабочем дне */
    hoursInADay: IngameTimeSettings['workDayHours'];
    /** Ведущая симуляция */
    simulation: TSimulation;
    /** Задачи симуляции */
    tasks: TSimTaskGanttExtend[] | TSessionTaskExtended[];
    /** Кейс использования Ганта */
    useCase: GanttUseCases;
    /** ПШЕ симуляции */
    workers?: TSimWorker[] | TSessionWorkerExtended[];
};

/** Класс утилит для преобразования задач для визуализации */
export class TaskUtils {
    /**
     * Подготовка данных для диаграммы Ганта
     * @param param0 Параметры, необходимые для вычисления
     * @returns Набор преобразованных задач для визуализации
     */
    public static tasksToGantt({
        hoursInADay,
        simulation,
        tasks,
        useCase,
        workers = [],
    }: TaskToGanttParams): TaskGantt[] {
        let tempTasks: TaskGantt[] = [];
        // Составляем строки
        if (useCase == GanttUseCases.Constructor) {
            tempTasks = (tasks as TSimTaskGanttExtend[]).map((ti, i) => {
                return {
                    column: 0,
                    curDuration: ti.curDuration,
                    estBudget: ti.est_budget,
                    estColumn: 0,
                    estDuration: ti.est_duration,
                    estWorkers: ti.est_workers,
                    following: ti.following,
                    id: ti.id,
                    maxLengthPath: [],
                    maxLengthValue: 0,
                    milestone: ti.milestone,
                    name: ti.name,
                    previous: ti.previous,
                    progress: 0,
                    rank: 0,
                    row: i,
                    slack: 1000,
                    statsReq: ti.stats_req,
                    uid: ti.uid,
                    workers: ti.workers,
                };
            });
        } else {
            const ticksInDay =
                rootStore.ingameStore.getTimeSettings().workDayHours *
                rootStore.ingameStore.SAI?.config.ticks_in_hour;
            tempTasks = (tasks as TSessionTaskExtended[]).map((ti, i) => {
                // Высчитываем "вес" задачи в трудочасах
                let currentWeight = ti.current_weight;
                // Если вес не вычислен или работников нет - плановые значения
                if (currentWeight == 0 && ti.current_workers.length == 0) {
                    currentWeight = ti.est_duration * hoursInADay;
                }
                // Высчитываем продолжитльность задачи в дробном кол-ве дней
                let curDuration = currentWeight / hoursInADay;
                // Если задача начата, не завершена и есть прогноз - используем его
                if (ti.start_day != null && ti.end_day == null && ti.predicted_end_day != null) {
                    const endPredictedPure =
                        ti.predicted_end_day + ti.predicted_end_tick / ticksInDay;
                    const startPure = ti.start_day + ti.start_tick / ticksInDay;
                    curDuration = endPredictedPure - startPure;
                }
                // Если задача завершена - фактическая продолжительность
                if (ti.start_day != null && ti.end_day != null) {
                    const endPure = ti.end_day + ti.end_tick / ticksInDay;
                    const startPure = ti.start_day + ti.start_tick / ticksInDay;
                    curDuration = endPure - startPure;
                }
                return {
                    column: 0,
                    curDuration: curDuration,
                    estBudget: ti.est_budget,
                    estColumn: 0,
                    estDuration: ti.est_duration,
                    estWorkers: ti.est_workers,
                    following: ti.following,
                    id: ti.id,
                    maxLengthPath: [],
                    maxLengthValue: 0,
                    milestone: ti.milestone,
                    name: ti.name,
                    previous: ti.previous,
                    progress:
                        currentWeight < 1
                            ? 1
                            : ti.progress > currentWeight
                              ? 1
                              : ti.progress / currentWeight,
                    row: i,
                    slack: 1000,
                    statsReq: ti.stats_req,
                    uid: ti.task_uid,
                    workers: ti.current_workers.map((cw_uid) =>
                        (workers as TSessionWorkerExtended[]).find((wi) => wi.worker_uid == cw_uid),
                    ),
                };
            });
        }
        let lastResult = [];
        // Проставляем отступы слева
        while (!_.isEqual(tempTasks, lastResult)) {
            lastResult = tempTasks;
            // Временная копия в рамках хода цикла
            const opTasks = [...tempTasks];
            for (let i = 0; i < opTasks.length; i++) {
                const curTask = opTasks[i];
                if (curTask.id == simulation.first_task || curTask.previous.length == 0) continue;
                for (let j = 0; j < curTask.previous.length; j++) {
                    const curPrev = opTasks.find((oti) => oti.id == curTask.previous[j]);
                    // Фактическая позиция старта - по текущим значениям продолжительности
                    if (curTask.column < curPrev.column + curPrev.curDuration) {
                        curTask.column = curPrev.column + curPrev.curDuration;
                    }
                    // Путь максимальной длины к задаче по текущим значениям
                    if (curTask.maxLengthValue < curPrev.maxLengthValue + curPrev.curDuration) {
                        curTask.maxLengthValue = curPrev.maxLengthValue + curPrev.curDuration;
                        curTask.maxLengthPath = curPrev.maxLengthPath.concat(curPrev.id);
                    }
                    // Плановая позиция старта - по значениям конструктора
                    if (curTask.estColumn < curPrev.estColumn + curPrev.estDuration) {
                        curTask.estColumn = curPrev.estColumn + curPrev.estDuration;
                    }
                }
                opTasks[i] = curTask;
            }
            tempTasks = opTasks;
        }
        // Вычисление критического пути задач
        const last_task = tempTasks.find((ti) => ti.id == simulation.last_task);
        tempTasks = tempTasks.map((tti) => {
            return {
                ...tti,
                milestone:
                    last_task.maxLengthPath.includes(tti.id) || tti.id == simulation.last_task,
            };
        });
        // Вычисление резерва (slack) задач
        tempTasks = tempTasks.map((tti) => {
            let slack = tti.slack;
            for (let i = 0; i < tti.following.length; i++) {
                // Из следующих задач смотрим - какая начинается раньше,
                // сколько до неё отступ, если есть
                const curFollowing = tempTasks.find((ttf) => ttf.id == tti.following[i]);
                const daysToFollowing = curFollowing.column - tti.column - tti.curDuration;
                if (slack > daysToFollowing) {
                    slack = daysToFollowing;
                }
            }
            return {
                ...tti,
                slack: tti.id == simulation.last_task ? 0 : slack,
            };
        });
        return tempTasks;
    }

    /**
     * Вычисление общей длины дерева задач
     * @param param0 Параметры, необходимые для вычисления
     * @returns Длина дерева задач от первой до последней в кол-ве дней
     */
    public static totalLengthOfTaskTree({
        hoursInADay,
        simulation,
        tasks,
        useCase,
    }: TaskToGanttParams): number {
        const taskTree = TaskUtils.tasksToGantt({
            hoursInADay,
            simulation,
            tasks,
            useCase,
        });
        const lastTask = taskTree.find((tti) => tti.id == simulation.last_task);
        return lastTask.column + lastTask.curDuration;
    }

    /**
     * Подготовка узлов для таймлайна
     * @param daysInAWeek Дней в рабочей неделе
     * @param hoursInADay Часов в рабочем дне
     * @param simulation Ведущая симуляция
     * @param STP Данные о списаниях трудочасов
     * @param tasks Набор задач симуляции
     * @param workers Набор ПШЕ симуляции
     * @returns Преобразованные задачи, готовые к визуализации
     */
    public static tasksToTimeline(
        daysInAWeek: IngameTimeSettings['daysInAWeek'],
        hoursInADay: IngameTimeSettings['workDayHours'],
        simulation: TSimulation,
        STP: TSessionTaskProgress[],
        tasks: TSimTaskGanttExtend[] | TSessionTaskExtended[],
        workers: TSimWorker[] | TSessionWorkerExtended[],
    ) {
        // Маркировка задач критического пути
        const tg = this.tasksToGantt({
            hoursInADay,
            simulation,
            tasks,
            useCase: GanttUseCases.Ingame,
            workers,
        });
        // Текущие назначения ПШЕ имеют собственный порядок
        // А вывод предстоящих задач на таймлайн - хронологический
        // Поэтому сперва вычислим уровень вложенности всех задач
        const first_task = tasks.find(
            (ti) => ti.id == simulation.first_task,
        ) as TSessionTaskExtended;
        let taskLevel: { [key: TSessionTaskExtended['id']]: number } = {
            [first_task.task_uid]: 1,
        };
        let tempTaskLevel: { [key: TSessionTaskExtended['id']]: number } = {};
        while (!_.isEqual(tempTaskLevel, taskLevel)) {
            tempTaskLevel = taskLevel;
            const opTaskLevel = taskLevel;
            for (let i = 0; i < tasks.length; i++) {
                const task = tasks[i];
                let maxTaskLevel = 1;
                for (let j = 0; j < task.previous.length; j++) {
                    if (Object.keys(taskLevel).includes('' + task.previous[j])) {
                        if (taskLevel[task.previous[j]] > maxTaskLevel) {
                            maxTaskLevel = taskLevel[task.previous[j]];
                        }
                    }
                }
                opTaskLevel[task.id] = maxTaskLevel;
            }
            taskLevel = opTaskLevel;
        }
        let tt: Array<
            TaskGantt & {
                allowActions: boolean;
                onTaskClick: (task_uid: TSimTask['uid']) => void | null;
                type: 'task' | 'free';
            }
        > = [];
        const ticksInDay =
            rootStore.ingameStore.getTimeSettings().workDayHours *
            rootStore.ingameStore.SAI?.config.ticks_in_hour;
        for (let i = 0; i < workers.length; i++) {
            const curWorker = workers[i] as TSessionWorkerExtended;
            // Сначала собираем в пачки STP
            const curWorkerSTP = STP.filter((stpi) => stpi.worker_uid == curWorker.worker_uid);
            const tempSTPgroups = [];
            let curSTPtask = null;
            let lastSTPday = null;
            let lastSTPtick = null;
            for (let j = 0; j < curWorkerSTP.length; j++) {
                const curSTP = curWorkerSTP[j];
                const curSTPposition = curSTP.day + curSTP.tick / ticksInDay;
                const lastSTPposition =
                    lastSTPday == null || lastSTPtick == null
                        ? null
                        : lastSTPday + lastSTPtick / ticksInDay;

                // Если до первой записи есть пробел
                if (j == 0 && lastSTPposition == null && curSTPposition != 1) {
                    tempSTPgroups.push({ type: 'free', duration: curSTPposition - 1 });
                }
                // Если есть разрыв между записями
                if (
                    j != 0 &&
                    lastSTPposition != null &&
                    curSTPposition - lastSTPposition - 1 / ticksInDay >
                        (curSTP.tick == 10 ? 0.112 : 0.01)
                ) {
                    //console.log("drop", i, {...curSTP}, curSTPposition, lastSTPposition, curSTPposition - lastSTPposition - (1 / ticksInDay), curSTP.tick, lastSTPtick)
                    tempSTPgroups.push({
                        type: 'free',
                        duration: curSTPposition - lastSTPposition,
                    });
                }

                // Если сменилась задача в списаниях
                if (curSTP.task_id != curSTPtask?.id) {
                    curSTPtask = tasks.find((ti) => ti.id == curSTP.task_id);
                    tempSTPgroups.push({
                        type: 'task',
                        duration: 1 / ticksInDay,
                        task_id: curSTP.task_id,
                    });
                } else {
                    // Та же задача
                    tempSTPgroups[tempSTPgroups.length - 1].duration += 1 / ticksInDay;
                }

                lastSTPday = curSTP.day;
                lastSTPtick = curSTP.tick;
            }
            // Если последнее списание по текущей задаче - добавляем длительность по прогнозу (либо плану)
            if (
                tempSTPgroups.length > 0 &&
                tempSTPgroups[tempSTPgroups.length - 1].type == 'task'
            ) {
                const lastTask = tasks.find(
                    (ti) => ti.id == tempSTPgroups[tempSTPgroups.length - 1].task_id,
                ) as TSessionTaskExtended;
                if (lastTask.predicted_end_day != null) {
                    if (lastTask.end_day == null) {
                        const predictedEnd =
                            lastTask.predicted_end_day + lastTask.predicted_end_tick / ticksInDay;
                        const factStart =
                            tempSTPgroups.reduce((acc, cv) => acc + cv.duration, 0) -
                            tempSTPgroups[tempSTPgroups.length - 1].duration +
                            1;
                        tempSTPgroups[tempSTPgroups.length - 1].duration = predictedEnd - factStart;
                    } else {
                        const lastTaskEnd = lastTask.end_day + lastTask.end_tick / ticksInDay;
                        const factStart =
                            tempSTPgroups.reduce((acc, cv) => acc + cv.duration, 0) -
                            tempSTPgroups[tempSTPgroups.length - 1].duration +
                            1;
                        tempSTPgroups[tempSTPgroups.length - 1].duration = lastTaskEnd - factStart;
                    }
                }
            }
            // Затем добавляем будущие задачи (в порядке открытия, плановая длительность)
            const workerCurTasksByRank = curWorker.current_tasks.sort(
                (a, b) => taskLevel[a] - taskLevel[b],
            );
            for (let j = 0; j < workerCurTasksByRank.length; j++) {
                const tempTask = tasks.find(
                    (ti) => ti.id == workerCurTasksByRank[j],
                ) as TSessionTaskExtended;
                // Пропускаем задачи, которые уже записаны перебором STP
                if (curWorkerSTP.find((stpi) => stpi.task_id == tempTask.id) != undefined) {
                    continue;
                }
                // Пропускаем задачи, которые назначены, но уже кончились
                if (tempTask.end_day != null) {
                    continue;
                }
                // Если задача не начата и до неё есть обязательный отступ
                if (tempTask.start_day == null) {
                    const tempTG = tg.find((tgi) => tgi.id == tempTask.id);
                    const groupTotalDuration = tempSTPgroups.reduce(
                        (acc, cv) => acc + cv.duration,
                        0,
                    );
                    if (tempTG.maxLengthValue != 0 && groupTotalDuration < tempTG.maxLengthValue) {
                        tempSTPgroups.push({
                            type: 'free',
                            duration: tempTG.maxLengthValue - groupTotalDuration,
                        });
                    }
                }
                // Если задача назначена, ещё в ней не работал, есть прогноз
                if (tempTask.predicted_end_day != null && tempTask.end_day == null) {
                    const tempTaskStart = tempTask.start_day + tempTask.start_tick / ticksInDay;
                    const tempTaskPredictedEnd =
                        tempTask.predicted_end_day + tempTask.predicted_end_tick / ticksInDay;
                    const groupTotalDuration = tempSTPgroups.reduce(
                        (acc, cv) => acc + cv.duration,
                        0,
                    );
                    if (groupTotalDuration + 1 < tempTaskStart) {
                        tempSTPgroups.push({
                            type: 'free',
                            duration: tempTaskStart - groupTotalDuration,
                        });
                    }
                    tempSTPgroups.push({
                        type: 'task',
                        duration: tempTaskPredictedEnd - tempTaskStart,
                        task_id: tempTask.id,
                    });
                } else if (tempTask.predicted_end_day == null && tempTask.end_day == null) {
                    // Если по задаче ещё нет прогноза, начата она или нет - плановая длина
                    tempSTPgroups.push({
                        type: 'task',
                        duration: tempTask.est_duration,
                        task_id: tempTask.id,
                    });
                }
            }
            // Если остаётся пробел до планового конца
            const plannedEnd = simulation.weeks * daysInAWeek;
            const groupTotalDuration = tempSTPgroups.reduce((acc, cv) => acc + cv.duration, 0);
            if (groupTotalDuration < plannedEnd) {
                tempSTPgroups.push({ type: 'free', duration: plannedEnd - groupTotalDuration });
            }
            //console.log(i, tempSTPgroups);
            // Переводим сформированные группы в общий набор
            let curColumn = 0;
            for (let j = 0; j < tempSTPgroups.length; j++) {
                const curGroup = tempSTPgroups[j];
                if (curGroup.type == 'free') {
                    tt.push({
                        allowActions: false,
                        column: curColumn,
                        curDuration: curGroup.duration,
                        estBudget: 0,
                        estColumn: 0,
                        estDuration: 0,
                        estWorkers: 0,
                        following: [],
                        id: 0,
                        maxLengthPath: [],
                        maxLengthValue: 0,
                        milestone: false,
                        name: '',
                        onTaskClick: null,
                        previous: [],
                        progress: 0,
                        row: i,
                        statsReq: [],
                        uid: '',
                        workers: [],
                        type: 'free',
                    });
                    curColumn += curGroup.duration;
                } else {
                    const curTask = tasks.find(
                        (ti) => ti.id == curGroup.task_id,
                    ) as TSessionTaskExtended;
                    tt.push({
                        allowActions: false,
                        column: curColumn,
                        curDuration: curGroup.duration,
                        estBudget: curTask.est_budget,
                        estColumn: 0,
                        estDuration: curTask.est_duration,
                        estWorkers: curTask.est_workers,
                        following: curTask.following,
                        id: curTask.id,
                        maxLengthPath: [],
                        maxLengthValue: 0,
                        milestone: false,
                        name: '' + curTask.id,
                        onTaskClick: null,
                        previous: curTask.previous,
                        progress:
                            curTask.current_weight < 1
                                ? 1
                                : curTask.progress / curTask.current_weight,
                        row: i,
                        statsReq: curTask.stats_req,
                        uid: curTask.task_uid,
                        workers: curTask.current_workers.map((cw_uid) =>
                            (workers as TSessionWorkerExtended[]).find(
                                (wi) => wi.worker_uid == cw_uid,
                            ),
                        ),
                        type: 'task',
                    });
                    curColumn += curGroup.duration;
                }
            }
        }

        tt = tt.map((tti) => {
            if (tti.type == 'task') {
                const ttiTG = tg.find((tgi) => tgi.id == tti.id);
                return {
                    ...tti,
                    milestone: ttiTG.milestone,
                };
            } else {
                return tti;
            }
        });

        return tt;
    }
}
