export const CONFIG = {
    /** ID div для закрепления SPA */
    MAIN_ROOT: 'simbios-root',
    /** URL бэкенда */
    BASE_URL: import.meta.env.VITE_API_URL || 'https://localhost:3000/api',
    /** Адрес для socket-подключений */
    SOCKET_URL: import.meta.env.VITE_SOCKET_URL || 'https://localhost:7090',
    /** Стандартный таймаут запросов */
    REQUEST_TIMEOUT: 30000,
    /** Таймаут bulk-запросов */
    BULK_REQUEST_TIMEOUT: 30000,
    /** Хранит данные сессии, TCredentials */
    CONNECTION_CREDENTIALS_PROPERTY: 'Simbios.Properties.ConnectionCredentials',
    /** Основной формат datetime */
    DATE_TIME_FORMAT: 'YYYY-MM-DDTHH:mm:ss',
};
