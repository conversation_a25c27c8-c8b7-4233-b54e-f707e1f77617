import { isNullOrUndefined } from '@shared/utils/common';
import { getConnectionCredentials } from './sessions';
import { rootStore } from '@store/instanse';
import { TPermissionCategory } from 'types/user/permissions';
import { TUser } from 'types/user/user';

export function checkPermission(permission: string): boolean {
    const creds = getConnectionCredentials();
    if (!creds?.accessToken) return false;

    const permissionSet = rootStore.currentUserStore.getUser?.permissions;
    if (isNullOrUndefined(permissionSet)) return false;

    const permValues: TPermissionCategory[] = Object.values(permissionSet);
    return permValues.some((item) =>
        item?.entity_permissions?.some((perm) => perm?.name === permission),
    );
}

export function checkUserPermission(user: TUser, permission: string): boolean {
    if (!user?.permissions) return false;

    const permissionSet = user.permissions;
    const permValues: TPermissionCategory[] = Object.values(permissionSet);
    return permValues.some((category) =>
        category?.entity_permissions?.some((perm) => perm?.name === permission),
    );
}
