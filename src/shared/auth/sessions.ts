import { TUser } from 'types/user/user';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import { getCookie, setCookie } from '@shared/utils/cookieManager';
import { CONFIG } from '@shared/config/config';

export type TCredentials = {
    accessToken?: string;
    refreshToken?: string;
    sessionAssignmentId?: TSessionAssignment['id'];
    user_id?: TUser['id'];
};

/**
 * Чтение данных сессии
 * @returns Данные сессии
 */
export function getConnectionCredentials(): TCredentials {
    const cookie = getCookie(CONFIG.CONNECTION_CREDENTIALS_PROPERTY);
    return cookie ? JSON.parse(cookie) : null;
}

/**
 * Запись данных сессии
 * @param credentials Данные сессии
 */
export function setConnectionCredentials(credentials: TCredentials) {
    const expires = new Date(Date.now() + 86400e3); // 24 часа

    setCookie(CONFIG.CONNECTION_CREDENTIALS_PROPERTY, JSON.stringify(credentials), {
        expires,
    });
}

/**
 * Обновляет данные сессии
 * @param credentials Данные сессии
 */
export function updateConnectionCredentials(credentials: Partial<TCredentials>): void {
    const oldCreds = getConnectionCredentials() ?? {};

    if (!oldCreds) {
        setConnectionCredentials(credentials);
        return;
    }

    setConnectionCredentials({ ...oldCreds, ...credentials });
}

/**
 * Очистка данных сессии
 */
export function clearConnectionCredentials(): void {
    setConnectionCredentials({
        accessToken: null,
        refreshToken: null,
        sessionAssignmentId: null,
        user_id: null,
    });
}
