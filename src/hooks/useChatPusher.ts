import { usePusherContext } from '@common/contexts/Pusher';
import { rootStore } from 'src/store/instanse';
import { message } from 'antd';
import { useEffect } from 'react';
import { TChat, TChatMessage } from 'types/chats/chats';

interface UseChatPusherProps {
    chatId?: string;
    chatType?: TChat['type'];
}

export const useChatPusher = ({ chatId, chatType }: UseChatPusherProps) => {
    const { subscribeToChannel } = usePusherContext();

    useEffect((): any => {
        if (!chatId || !chatType) return;
        const chatChannel = subscribeToChannel(
            `${chatType == 'private' ? 'private' : 'presence'}-chat-${chatId}`,
        );

        if (!chatChannel) {
            message.error('UseChatPusher: Не инициализирован канал');
            return;
        }

        const handleChatMessage = (data: TChatMessage) => {
            if (rootStore.socketStore.verbose) {
                message.info('UseChatPusher: Новое сообщение в консоли');
                console.log(data);
            }
            rootStore.socketStore.handleChatMessage(data);
        };

        chatChannel.bind('pusher:subscription_succeeded', () => {
            if (rootStore.socketStore.verbose) {
                message.info('UseChatPusher: Канал чата подключён');
            }
            rootStore.socketStore.fetchChatMessages(chatId, null);
        });
        chatChannel.bind('send-message', handleChatMessage);
        chatChannel.bind('pusher:subscription_error', (error) => {
            message.error('UseChatPusher: Ошибка при подключении к каналу чата');
            console.log(error);
        });

        return () => {
            if (rootStore.socketStore.verbose) {
                message.info('UseChatPusher: Канал чата отключён');
            }
            chatChannel.unbind_all();
            chatChannel.unsubscribe();
        };
    }, [
        chatId,
        chatType,
        subscribeToChannel,
    ]);
};
