import { TFilter } from 'types/filter';
import {
    Button,
    Checkbox,
    Col,
    ColorPicker,
    ColorPickerProps,
    Divider,
    Dropdown,
    Form,
    GetRef,
    Input,
    InputNumber,
    InputRef,
    message,
    Popconfirm,
    Row,
    Space,
    Table,
    TableProps,
    Tooltip,
} from 'antd';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useReactive } from 'ahooks';
import { useNavigate } from 'react-router-dom';
import { Permissions } from 'src/shared/permissions';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { Common } from 'src/shared/common';
import { CRMAPIManager } from '@api/crmApiManager';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { TableRowSelection } from 'antd/es/table/interface';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { Loader } from '@components/ui/loader/loader';
import _ from 'lodash';
import { prepareGrid } from '@components/tests/colors';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { GlobalConstants } from 'src/shared/constants';
import { rootStore } from 'src/store/instanse';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';

import './filters.scss';

type FormInstance<T> = GetRef<typeof Form<T>>;

const EditableContext = React.createContext<FormInstance<any> | null>(null);

type FilterDataType = TFilter & {
    key: TFilter['id'];
    deleted: boolean;
    fdtStatus: { color: string; text: string }[];
};

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form
            form={form}
            component={false}
        >
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    dataIndex: keyof FilterDataType;
    record: FilterDataType;
    handleSave: (record: FilterDataType) => void;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}) => {
    const [editing, setEditing] = useState(false);
    const inputRef = useRef<InputRef>(null);
    const form = useContext(EditableContext)!;

    useEffect(() => {
        if (editing) {
            inputRef?.current?.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({ [dataIndex]: record[dataIndex] });
    };

    const save = async () => {
        try {
            const values = await form.validateFields();

            toggleEdit();
            handleSave({ ...record, ...values });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };

    const customPanelRender: ColorPickerProps['panelRender'] = (
        _,
        { components: { Picker, Presets } },
    ) => (
        <Row
            justify="space-between"
            wrap={false}
        >
            <Col span={12}>
                <Presets />
            </Col>
            <Divider
                type="vertical"
                style={{ height: 'auto' }}
            />
            <Col flex="auto">
                <Picker />
            </Col>
        </Row>
    );

    let childNode = children;

    if (editable && !(record.deleted || record.deleted_at != null)) {
        childNode = editing ? (
            <Form.Item
                style={{ margin: 0 }}
                name={dataIndex}
                rules={[
                    {
                        required: true,
                        message: `${title} обязательное поле.`,
                    },
                ]}
                valuePropName={dataIndex == 'is_protected' ? 'checked' : 'value'}
            >
                {dataIndex == 'is_protected' && <Checkbox onChange={save} />}
                {dataIndex == 'name' && (
                    <Input
                        ref={inputRef}
                        maxLength={16}
                        onPressEnter={save}
                        onBlur={save}
                        placeholder="Введите название"
                        required
                    />
                )}
                {dataIndex == 'colorHEX' && (
                    <ColorPicker
                        disabledAlpha
                        format="hex"
                        onOpenChange={(o) => {
                            if (!o) save();
                        }}
                        panelRender={customPanelRender}
                        presets={prepareGrid().map((g) => {
                            return {
                                colors: g.colors.map((gc) => gc.hex),
                                defaultOpen: false,
                                key: g.group,
                                label: g.group,
                            };
                        })}
                        showText
                        styles={{ popupOverlayInner: { width: 480 } }}
                    />
                )}
                {dataIndex == 'target' && (
                    <Space.Compact block>
                        <Input
                            ref={inputRef}
                            maxLength={16}
                            onBlur={(e) => {
                                if (
                                    !(
                                        document.activeElement?.classList?.contains('no-blur') ||
                                        e.relatedTarget?.classList?.contains('no-blur')
                                    )
                                ) {
                                    save();
                                }
                            }}
                            onPressEnter={save}
                            placeholder="Введите группу сущностей"
                            required
                        />
                        <Dropdown
                            className="no-blur"
                            menu={{
                                className: 'no-blur',
                                items: [
                                    { label: 'Пользователи', key: 'users' },
                                    { label: 'Симуляции', key: 'simulations' },
                                    { label: 'Назначения', key: 'assignments' },
                                ],
                                onClick: (info) => {
                                    form.setFieldValue(dataIndex, info.key);
                                    save();
                                },
                            }}
                            trigger={['click']}
                        >
                            <Button
                                className="no-blur target-block-btn"
                                icon={<div className="three-dots-icon" />}
                            />
                        </Dropdown>
                    </Space.Compact>
                )}
            </Form.Item>
        ) : (
            <div
                className="editable-cell-value-wrap"
                style={{ paddingInlineEnd: 4 }}
                onClick={toggleEdit}
            >
                {children}
            </div>
        );
    }

    return <td {...restProps}>{childNode}</td>;
};

type ColumnTypes = Exclude<TableProps<FilterDataType>['columns'], undefined>;

type TFilterChange = TFilter & {
    change_type: 'add' | 'update' | 'remove' | 'restore';
};

type TState = {
    addAmount: number;
    addWithTarget: TFilter['target'];
    changes: { [key: TFilter['id']]: TFilterChange };
    filters: TFilter[];
    isLoading: boolean;
    lastFilterId: number;
    onlyShowDeleted: boolean;
    selectedFilterRows: React.Key[];
};

const FiltersPage = (): JSX.Element => {
    const state = useReactive<TState>({
        addAmount: 1,
        addWithTarget: '',
        changes: {},
        filters: [],
        isLoading: false,
        lastFilterId: -1,
        onlyShowDeleted: false,
        selectedFilterRows: [],
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const editable =
        Permissions.checkPermission(Permissions.FilterBulkAdd) &&
        Permissions.checkPermission(Permissions.FilterBulkResult) &&
        Permissions.checkPermission(Permissions.FilterCreate) &&
        Permissions.checkPermission(Permissions.FilterDelete) &&
        Permissions.checkPermission(Permissions.FilterGet) &&
        Permissions.checkPermission(Permissions.FilterList) &&
        Permissions.checkPermission(Permissions.FilterRestore) &&
        Permissions.checkPermission(Permissions.FilterUpdate);
    const anyChanges =
        Object.keys(state.changes).filter(
            (sckey) =>
                !(state.changes[sckey].id < 0 && state.changes[sckey].change_type == 'delete'),
        ).length > 0;
    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };
    const tableStatuses = {
        deleted: GlobalConstants.TableStatuses['deleted'],
        new: GlobalConstants.TableStatuses['new'],
        restored: GlobalConstants.TableStatuses['restored'],
        unfilled: GlobalConstants.TableStatuses['unfilled'],
        updated: GlobalConstants.TableStatuses['updated'],
    };
    const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
        {
            title: 'ID фильтра',
            dataIndex: 'id',
            render: (value, record) => (
                <span className="table-id p3">
                    {value < 0 ? `new${value}` : value}
                    {value >= 0 && (
                        <CopyButton
                            size={20}
                            textToCopy={`Фильтр #${value}, \'${Common.isNullOrEmptyString(record?.name) ? 'Без названия' : record?.name}\'`}
                            textToShow={'ID фильтра скопирован'}
                        />
                    )}
                </span>
            ),
            sorter: (a, b) => a.id - b.id,
            width: 200,
        },
        {
            title: 'Защищён',
            dataIndex: 'is_protected',
            editable: rootStore.currentUserStore.getUser?.role == 'Roles.Admin',
            render: (value) => <span className="p3">{value ? 'Да' : 'Нет'}</span>,
            sorter: (a, b) => +a.is_protected - +b.is_protected,
            width: 150,
        },
        {
            title: 'Состояние',
            dataIndex: 'fdtStatus',
            filters: Object.keys(tableStatuses).map((tsk) => {
                const tsv = tableStatuses[tsk];
                return {
                    text: (
                        <FilterButton
                            key={tsk}
                            hex={tsv.color}
                            text={tsv.text}
                        />
                    ),
                    value: tsv.text,
                };
            }),
            onFilter: (value, record) => {
                return record.fdtStatus.find((si) => si.text.includes('' + value)) != undefined;
            },
            render: (_, rec) => (
                <Row className="table-status">
                    {rec.fdtStatus.map((si, i) => (
                        <FilterButton
                            key={`status-${i}`}
                            hex={si.color}
                            text={si.text}
                        />
                    ))}
                    {rec.fdtStatus.length == 0 && <span className="p3 gray">Нет статусов</span>}
                </Row>
            ),
            width: 200,
        },
        {
            title: 'Название',
            dataIndex: 'name',
            editable: true,
            render: (value) =>
                Common.isNullOrEmptyString(value) ? (
                    <span className="p3 no-filter-name">Введите название</span>
                ) : (
                    <span className="p3">{value}</span>
                ),
            sorter: (a, b) => a.name.localeCompare(b.name),
            width: 200,
        },
        {
            title: 'Цвет фона',
            dataIndex: 'colorHEX',
            editable: true,
            render: (value, record) => {
                if (Common.isNullOrEmptyString(value)) {
                    return <span className="p3 no-filter-name">Выберите цвет</span>;
                } else {
                    const pureHex = typeof value == 'string' ? value.slice(1) : value.toHex();
                    return (
                        <FilterButton
                            emptyText="Текст"
                            hex={`#${pureHex}`}
                            text={record.name}
                        />
                    );
                }
            },
            width: 200,
        },
        {
            title: 'Группа сущностей',
            dataIndex: 'target',
            editable: true,
            render: (value) => {
                if (Common.isNullOrEmptyString(value)) {
                    return <span className="p3 no-filter-name">Выберите группу сущностей</span>;
                } else {
                    const tryLocaleValue = () => {
                        const items = [
                            { label: 'Пользователи', key: 'users' },
                            { label: 'Симуляции', key: 'simulations' },
                            { label: 'Назначения', key: 'assignments' },
                        ];
                        const item = items.find((i) => i.key == value);
                        return item == undefined ? value : item.label;
                    };
                    return <span className="p3">{tryLocaleValue()}</span>;
                }
            },
            filters: [
                {
                    text: <span className="p3">Пользователи</span>,
                    value: 'users',
                },
                {
                    text: <span className="p3">Симуляции</span>,
                    value: 'simulations',
                },
                {
                    text: <span className="p3">Назначения</span>,
                    value: 'assignments',
                },
            ],
            onFilter: (value, record) => {
                return record.target == value;
            },
            sorter: (a, b) => a.target.localeCompare(b.target),
            width: 200,
        },
        {
            title: 'Создан',
            dataIndex: 'created_at',
            render: (value) => (
                <span className="p3 lighter-tone">{Common.formatDateString(value)}</span>
            ),
            sorter: (a, b) => {
                const aTime = new Date(a.created_at).getTime();
                const bTime = new Date(b.created_at).getTime();
                return aTime - bTime;
            },
            width: 200,
        },
        {
            title: 'Изменён',
            dataIndex: 'updated_at',
            render: (value) => (
                <span className="p3 lighter-tone">{Common.formatDateString(value)}</span>
            ),
            sorter: (a, b) => {
                const aTime = new Date(a.updated_at).getTime();
                const bTime = new Date(b.updated_at).getTime();
                return aTime - bTime;
            },
            width: 200,
        },
    ];

    function updateTableFilter(record: FilterDataType) {
        if (Object.keys(state.changes).includes('' + record.id)) {
            const originalFilter = state.filters.find((fil) => fil.id == record.id);
            if (
                originalFilter != undefined &&
                state.changes[record.id].change_type != 'remove' &&
                _.isEqual(record, {
                    ...originalFilter,
                    change_type: 'update',
                    key: record.key,
                    deleted: record.deleted,
                    fdtStatus: record.fdtStatus,
                })
            ) {
                delete state.changes[record.id];
            } else {
                state.changes[record.id] = {
                    ...state.changes[record.id],
                    ...record,
                };
            }
        } else {
            const originalFilter = state.filters.find((fil) => fil.id == record.id);
            if (
                !_.isEqual(record, {
                    ...originalFilter,
                    key: originalFilter.id,
                    deleted: false,
                    fdtStatus: [],
                })
            ) {
                state.changes[record.id] = {
                    ...record,
                    change_type: 'update',
                };
            }
        }
    }

    const columns = defaultColumns.map((col) => {
        if (!col.editable) {
            return col;
        }
        return {
            ...col,
            onCell: (record: FilterDataType) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: updateTableFilter,
            }),
        };
    });

    useEffect(() => {
        loadNecessary();
    }, []);

    function checkPermission() {
        return (
            Permissions.checkPermission(Permissions.FilterList) &&
            Permissions.checkPermission(Permissions.FilterGet)
        );
    }

    async function loadNecessary() {
        if (!checkPermission()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        state.changes = {};
        state.onlyShowDeleted = false;
        state.selectedFilterRows = [];
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.request<FilterListResp>(async (api) => {
                return await api.getFilterList({
                    query: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: null,
                    },
                });
            });
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data;
            state.lastFilterId = -1 * (filters.data.meta.total + 1);
        } catch (errors) {
            messageApi.error('Ошибка при получении списка фильтров');
        }
        state.isLoading = false;
    }

    function makeFilter(): TFilter {
        const newFilter: TFilter = {
            id: state.lastFilterId,
            is_protected: false,
            colorHEX: '',
            name: '',
            target: state.addWithTarget,
            created_at: Common.dateNowString(),
            updated_at: null,
            deleted_at: null,
        };
        state.lastFilterId -= 1;
        return newFilter;
    }

    function getStatusList(filter: TFilter) {
        const statuses: FilterDataType['fdtStatus'] = [];
        if (filter.id < 0) {
            statuses.push(tableStatuses['new']);
        }
        if (
            state.changes[filter.id] != undefined &&
            state.changes[filter.id].change_type == 'remove'
        ) {
            statuses.push(tableStatuses['deleted']);
        }
        if (
            state.changes[filter.id] != undefined &&
            state.changes[filter.id].change_type == 'restore'
        ) {
            statuses.push(tableStatuses['restored']);
        }
        if (
            state.changes[filter.id] != undefined &&
            state.changes[filter.id].change_type == 'update'
        ) {
            statuses.push(tableStatuses['updated']);
        }
        if (
            Common.isNullOrEmptyString(filter.name) ||
            Common.isNullOrEmptyString(filter.colorHEX) ||
            Common.isNullOrEmptyString(filter.target)
        ) {
            statuses.push(tableStatuses['unfilled']);
        }
        return statuses;
    }

    function makeTableData(): FilterDataType[] {
        const tableData = state.filters.map((fil) => {
            const change = state.changes[fil.id];
            const tempFil = change != undefined ? change : fil;
            const deleted =
                change == undefined ? fil.deleted_at != null : change.change_type == 'remove';
            return {
                ...tempFil,
                key: tempFil.id,
                deleted: deleted,
                fdtStatus: getStatusList(tempFil),
            };
        });
        return tableData.filter((tdi) => tdi.deleted == state.onlyShowDeleted);
    }

    const filterRowSelection: TableRowSelection<FilterDataType> = {
        columnWidth: 40,
        getCheckboxProps: (fil: FilterDataType) => ({
            disabled:
                !editable ||
                (rootStore.currentUserStore.getUser?.role != 'Roles.Admin' && fil.is_protected),
            name: '' + fil.id,
        }),
        onChange: (srk) => (state.selectedFilterRows = srk),
        selectedRowKeys: state.selectedFilterRows,
    };

    function addNewFilters() {
        const amount = state.addAmount;
        const newSAList = [];
        const tempChanges = { ...state.changes };
        const rowKeySelectList = [];
        for (let i = 0; i < amount; i++) {
            const newFil = makeFilter();
            newSAList.push(newFil);
            tempChanges[newFil.id] = {
                ...newFil,
                change_type: 'add',
            };
            rowKeySelectList.push(newFil.id);
        }
        state.changes = tempChanges;
        state.filters = [
            ...state.filters,
            ...newSAList,
        ];
        state.selectedFilterRows = rowKeySelectList;
    }

    function makeChangeStats() {
        const values = Object.values(state.changes);
        let chDelete = 0;
        let chEdit = 0;
        let chNew = 0;
        let chRestore = 0;
        for (let i = 0; i < values.length; i++) {
            if (values[i].id < 0) chNew += 1;
            switch (values[i].change_type) {
                case 'remove': {
                    chDelete += 1;
                    break;
                }
                case 'update': {
                    chEdit += 1;
                    break;
                }
                case 'restore': {
                    chRestore += 1;
                    break;
                }
            }
        }
        return `(новые: ${chNew}, изменены: ${chEdit}, удалены: ${chDelete}, восстановлены: ${chRestore})`;
    }

    function deleteSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedFilterRows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                if (tempChanges[change].change_type == 'restore') {
                    delete tempChanges[change];
                } else {
                    tempChanges[change].change_type = 'remove';
                }
            } else {
                const filter = state.filters.find((filter) => filter.id == tempSKR[i]);
                tempChanges['' + tempSKR[i]] = {
                    ...filter,
                    key: filter.id,
                    change_type: 'remove',
                    deleted: true,
                    fdtStatus: [],
                };
            }
        }
        state.changes = tempChanges;
        state.selectedFilterRows = [];
    }

    function restoreSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedFilterRows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                delete tempChanges[change];
            } else {
                const filter = state.filters.find((filter) => filter.id == tempSKR[i]);
                tempChanges['' + tempSKR[i]] = {
                    ...filter,
                    key: filter.id,
                    change_type: 'restore',
                    deleted: false,
                    fdtStatus: [],
                };
            }
        }
        state.changes = tempChanges;
        state.selectedFilterRows = [];
    }

    const countUnfinishedFilters = useCallback(() => {
        const keys = Object.keys(state.changes);
        let count = 0;
        for (let i = 0; i < keys.length; i++) {
            const value = state.changes[keys[i]];
            if (
                value.change_type != 'remove' &&
                (Common.isNullOrEmptyString(value.name) ||
                    Common.isNullOrEmptyString(value.colorHEX) ||
                    Common.isNullOrEmptyString(value.target))
            ) {
                count++;
            }
        }
        return count;
    }, [state.changes]);

    function prepareChanges(): TBulkUpdate<TFilter> {
        return Object.keys(state.changes)
            .filter((key) => {
                const change = state.changes[key];
                return !(change.id < 0 && change.change_type == 'remove');
            })
            .map((changeKey, i) => {
                const change = state.changes[changeKey];
                return {
                    action: change.change_type,
                    index: i + 1,
                    value: {
                        id: change.id < 0 ? -1 * change.id : change.id,
                        is_protected: change.is_protected,
                        name: change.name,
                        colorHEX:
                            typeof change.colorHEX == 'string'
                                ? change.colorHEX
                                : `#${change.colorHEX.toHex()}`,
                        target: change.target,
                        created_at: change.created_at,
                        updated_at: change.updated_at,
                        deleted_at: change.deleted_at,
                    },
                };
            });
    }

    async function saveChanges() {
        const counter = countUnfinishedFilters();
        if (counter > 0) {
            messageApi.warning(`Остались не заполненные фильтры: ${counter} шт.`);
            return false;
        }
        state.isLoading = true;
        let returnValue = false;
        const bulkBody = prepareChanges();
        try {
            const result = await CRMAPIManager.bulkRequest<TFilter>(
                bulkBody,
                async (api, _bulkBody) => {
                    return await api.bulkFilter(_bulkBody);
                },
                async (api, task_id) => {
                    return await api.bulkResultFilter(task_id);
                },
            );
            if (result.errorMessages) throw result.errorMessages;
            message.success('Изменения сохранены');
            await loadNecessary();
            returnValue = true;
        } catch (errors) {
            messageApi.error('Ошибка при сохранении, попробуйте ещё раз');
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    return (
        <UniLayout
            activeTab="filters"
            additionalClass="profile-min-width"
            tabSet="management"
        >
            <div className="filters-profile">
                {contextHolder}
                {state.isLoading && <Loader />}
                <PreventLeaving
                    anyChanges={anyChanges}
                    onSave={saveChanges}
                />
                <Row className="filters-card">
                    <Col>
                        <Row className="header-row">
                            <Col>
                                <h4>{`Фильтров: ${state.filters.length} ${makeChangeStats()}`}</h4>
                            </Col>
                        </Row>
                        <Row className="body-row">
                            <Col>
                                <Row className="table-actions">
                                    <Col>
                                        <span className="selected p3">
                                            {state.selectedFilterRows.length} выбрано
                                        </span>
                                    </Col>
                                    <Col>
                                        <Space.Compact
                                            block
                                            className="p3 add-group"
                                        >
                                            <Button
                                                disabled={!editable || state.onlyShowDeleted}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    state.addAmount = 1;
                                                }}
                                                type={state.addAmount == 1 ? 'primary' : 'default'}
                                            >
                                                1
                                            </Button>
                                            <Button
                                                disabled={!editable || state.onlyShowDeleted}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    state.addAmount = 5;
                                                }}
                                                type={state.addAmount == 5 ? 'primary' : 'default'}
                                            >
                                                5
                                            </Button>
                                            <Button
                                                disabled={!editable || state.onlyShowDeleted}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    state.addAmount = 10;
                                                }}
                                                type={state.addAmount == 10 ? 'primary' : 'default'}
                                            >
                                                10
                                            </Button>
                                            <InputNumber
                                                disabled={!editable || state.onlyShowDeleted}
                                                max={10}
                                                min={1}
                                                onChange={(value) => {
                                                    state.addAmount = value;
                                                }}
                                                value={state.addAmount}
                                            />
                                            <Button
                                                disabled={!editable || state.onlyShowDeleted}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    addNewFilters();
                                                }}
                                                type="primary"
                                            >
                                                +
                                            </Button>
                                        </Space.Compact>
                                    </Col>
                                    <Col>
                                        <Space.Compact block>
                                            <Input
                                                disabled={!editable || state.onlyShowDeleted}
                                                maxLength={16}
                                                onChange={(e) =>
                                                    (state.addWithTarget = e.target.value)
                                                }
                                                placeholder="Введите группу сущностей"
                                                value={state.addWithTarget}
                                            />
                                            <Dropdown
                                                disabled={!editable || state.onlyShowDeleted}
                                                menu={{
                                                    items: [
                                                        { label: 'Пользователи', key: 'users' },
                                                        { label: 'Симуляции', key: 'simulations' },
                                                        { label: 'Назначения', key: 'assignments' },
                                                    ],
                                                    onClick: (info) => {
                                                        state.addWithTarget = info.key;
                                                    },
                                                }}
                                                trigger={['click']}
                                            >
                                                <Button
                                                    className="target-block-btn"
                                                    icon={<div className="three-dots-icon" />}
                                                />
                                            </Dropdown>
                                        </Space.Compact>
                                    </Col>
                                    {state.selectedFilterRows.length > 0 &&
                                        !state.onlyShowDeleted && (
                                            <Col>
                                                <Button
                                                    danger
                                                    disabled={!editable}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        deleteSelected();
                                                    }}
                                                >
                                                    Удалить
                                                </Button>
                                            </Col>
                                        )}
                                    {state.selectedFilterRows.length > 0 &&
                                        state.onlyShowDeleted && (
                                            <Col>
                                                <Button
                                                    danger
                                                    disabled={!editable}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        restoreSelected();
                                                    }}
                                                >
                                                    Восстановить
                                                </Button>
                                            </Col>
                                        )}
                                    <Col>
                                        <Checkbox
                                            checked={state.onlyShowDeleted}
                                            onClick={() => {
                                                state.selectedFilterRows = [];
                                                state.onlyShowDeleted = !state.onlyShowDeleted;
                                            }}
                                        >
                                            Корзина
                                        </Checkbox>
                                    </Col>
                                </Row>
                                <Row className="table-container">
                                    <Table<FilterDataType>
                                        bordered
                                        columns={columns as ColumnTypes}
                                        components={components}
                                        dataSource={makeTableData()}
                                        locale={{
                                            emptyText: (
                                                <Col
                                                    className="empty-text p3"
                                                    flex={1}
                                                >
                                                    <Row>Таких приглашений нет :)</Row>
                                                </Col>
                                            ),
                                        }}
                                        pagination={false}
                                        rowClassName={() => 'editable-row'}
                                        rowSelection={filterRowSelection}
                                        scroll={{
                                            x: 'max-content',
                                            y: 77 * 7,
                                        }}
                                    />
                                </Row>
                            </Col>
                        </Row>
                        <Row className="p2 controls-row">
                            <Col>
                                <Row>
                                    {anyChanges && (
                                        <Tooltip
                                            placement="topLeft"
                                            title={
                                                countUnfinishedFilters() > 0
                                                    ? `Не заполненных фильтров: ${countUnfinishedFilters()}`
                                                    : null
                                            }
                                        >
                                            <Button
                                                onClick={() => {
                                                    if (
                                                        anyChanges &&
                                                        countUnfinishedFilters() == 0
                                                    ) {
                                                        saveChanges();
                                                    }
                                                }}
                                            >
                                                Сохранить
                                            </Button>
                                        </Tooltip>
                                    )}
                                    {anyChanges && (
                                        <Popconfirm
                                            cancelText="Отмена"
                                            disabled={!anyChanges}
                                            okText="ОК"
                                            onConfirm={() => {
                                                loadNecessary();
                                            }}
                                            title="Не сохранённые изменения будут потеряны"
                                        >
                                            <Button>Отмена</Button>
                                        </Popconfirm>
                                    )}
                                </Row>
                            </Col>
                        </Row>
                    </Col>
                </Row>
            </div>
        </UniLayout>
    );
};

export default FiltersPage;
