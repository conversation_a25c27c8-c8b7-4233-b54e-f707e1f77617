import { Common } from 'src/shared/common';
import { SettingsManager } from 'src/shared/settingsManager';
import { Loader } from '@components/ui/loader/loader';
import {
    faCheckDouble,
    faCheck,
    faFolderOpen,
    faUserShield,
    faUserPen,
    faUserXmark,
    faUserCheck,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { rootStore } from 'src/store/instanse';
import { TChatExtended } from 'src/store/socketStore';
import { useReactive } from 'ahooks';
import {
    message,
    Col,
    Row,
    Button,
    Tooltip,
    List,
    Popover,
    Input,
    Dropdown,
    Popconfirm,
} from 'antd';
import dayjs from 'dayjs';
import EmojiPicker, { EmojiStyle } from 'emoji-picker-react';
import { debounce } from 'lodash';
import { useRef, useEffect, useCallback } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useNavigate, useParams } from 'react-router-dom';
import { TChat, TChatMessage, TChatUser } from 'types/chats/chats';
import ChatChannelHolder from './chatChannelHolder';
import { NewChatDialog } from './newChatDialog';
import { Permissions } from 'src/shared/permissions';
import { observer } from 'mobx-react';
import { CRMAPIManager } from '@api/crmApiManager';
import { ChatResp } from '@api/responseModels/chat/chatResponse';
import { TUser } from 'types/user/user';
import { UserListResp } from '@api/responseModels/users/userListResp';
import UserDrawer, { UserDrawerUseCase } from '@components/drawers/userDrawer';

const { Search } = Input;
const { TextArea } = Input;

import './chatsFullpage.scss';

export enum ChatPageUseCase {
    Default = 'default',
    Ingame = 'ingame',
}

type TProps = {
    useCase: ChatPageUseCase;
};

type TState = {
    isLoading: boolean;
    chatListEditing: boolean;
    chatListMinimized: boolean;
    chatListSearchValue: string;
    chatList: TChatExtended[];
    chatMode: 'default' | 'info' | 'search';
    infScrollHeight: number;
    initialized: boolean;
    messageInputText: string;
    messageListTrueSearchValue: string;
    messageListVisibleSearchValue: string;
    newChatDialogOpen: boolean;
    onlyShowDeletedChats: boolean;
    tempChatName: string;
    userList: TUser[];
    userPickerOpen: boolean;
    userPickerTempSelect: TUser['id'][];
};

const ChatsFullpage = observer(({ useCase }: TProps): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        chatListEditing: false,
        chatListMinimized: false,
        chatListSearchValue: '',
        chatList: [],
        chatMode: 'default',
        infScrollHeight: 500,
        initialized: false,
        messageInputText: '',
        messageListTrueSearchValue: '',
        messageListVisibleSearchValue: '',
        newChatDialogOpen: false,
        onlyShowDeletedChats: false,
        tempChatName: '',
        userList: [],
        userPickerOpen: false,
        userPickerTempSelect: [],
    });
    const navigate = useNavigate();
    const { chatId } = useParams();
    const [messageApi, contextHolder] = message.useMessage();
    const chatMessagesScrollRef = useRef<HTMLDivElement>(null);
    const chatListColRef = useRef<HTMLDivElement>(null);
    const charLimit = 900;
    const creds = SettingsManager.getConnectionCredentials();

    /// Основные функции
    // При изменении chatId в URL
    useEffect(() => {
        state.chatMode = 'default';
        if (!state.initialized) {
            initialize(chatId);
        } else {
            setSelectedChat(chatId);
        }
    }, [chatId]);

    function setAppropriatePerPage() {
        const messageMinHeight = 84;
        const messageGap = 8;
        const headerHeight = 80;
        const footerMinHeight = 82;
        const containerHeight = chatListColRef?.current?.clientHeight;
        if (containerHeight == null) {
            rootStore.socketStore.per_page = 20;
        } else {
            const availableHeight = containerHeight - headerHeight - footerMinHeight;
            const fittingSlots =
                Math.ceil(
                    ((availableHeight + messageGap) / (messageMinHeight + messageGap)) * 1.2,
                ) + 3;
            rootStore.socketStore.per_page = fittingSlots;
        }
    }

    /**
     * Функция инициализации списка чатов в хранилище
     * @param chatId ID чата из URL
     * @returns Обрыв функции
     */
    async function initialize(chatId: TChat['id'] | undefined) {
        if (!Permissions.checkPermission(Permissions.ChatList)) {
            if (useCase == ChatPageUseCase.Default) {
                navigate('/lk');
            } else {
                navigate('/session/dashboard');
            }
            message.error('Недостаточно прав для просмотра списка чатов');
            return;
        }
        state.isLoading = true;
        // Установка подходящего кол-ва сущностей "на страницу"
        setAppropriatePerPage();
        // Подгрузка списка чатов, если этого ещё не было сделано;
        const result = await rootStore.socketStore.getChatList(chatId, false, false);
        // Проверка на существование чата, если указан
        if (result != null) {
            state.isLoading = false;
            messageApi.error(result.text);
            if (result.redir) {
                if (useCase == ChatPageUseCase.Default) {
                    navigate('/chats');
                } else {
                    navigate('/session/chats');
                }
            }
            return;
        }
        state.initialized = true;
        state.onlyShowDeletedChats = false;
        await setSelectedChat(chatId);
    }

    /**
     * Установка текущего чата в хранилище
     * @param chatId ID чата из URL
     */
    async function setSelectedChat(chatId: TChat['id'] | undefined) {
        if (!Permissions.checkPermission(Permissions.ChatList)) {
            if (useCase == ChatPageUseCase.Default) {
                navigate('/lk');
            } else {
                navigate('/session/dashboard');
            }
            message.error('Недостаточно прав для просмотра списка чатов');
            return;
        }
        if (!state.isLoading) {
            state.isLoading = true;
        }
        // Установка текущего чата, если указан (иначе очистка поля)
        const result = await rootStore.socketStore.setCurrentChat(chatId);
        state.isLoading = false;
        // Проверка на существование, если указан
        if (result != null) {
            messageApi.error(result.text);
            if (result.redir) {
                if (useCase == ChatPageUseCase.Default) {
                    navigate('/chats');
                } else {
                    navigate('/session/chats');
                }
                return;
            }
        }
        if (rootStore.socketStore.currentChat?.deleted_at != null) {
            state.onlyShowDeletedChats = true;
        }
    }

    async function loadMoreChatMessages() {
        const result = await rootStore.socketStore.loadMoreCurrentChatMessages(
            state.messageListTrueSearchValue,
        );
        if (result != null) {
            if (result.redir) {
                if (useCase == ChatPageUseCase.Default) {
                    navigate('/chats');
                } else {
                    navigate('/session/chats');
                }
                return;
            }
            if (result.text) messageApi.error(result.text);
        }
    }

    function currentChatHasMore() {
        return rootStore.socketStore.currentChat.hasMore;
    }

    async function sendMessage() {
        if (!rootStore.socketStore.currentChat?.users.find((ui) => ui.id == creds?.user_id)) {
            messageApi.info('Штирлиц, нельзя писать в чужие чаты');
            return;
        }
        if (state.messageInputText.trim().length == 0) {
            messageApi.info('Введите текст сообщения');
            return;
        }
        const anyErrors = await rootStore.socketStore.sendMessage(
            rootStore.socketStore.currentChat?.id,
            state.messageInputText,
        );
        if (anyErrors) {
            messageApi.error('Не удалось отправить сообщение');
            return;
        }
        state.messageInputText = '';
    }

    /// Функции реакции на изменения store
    // Обновление групп при изменении списка чатов
    useEffect(() => {
        state.chatList = rootStore.socketStore.chatList.filter((chat) => {
            if (state.onlyShowDeletedChats && chat.deleted_at == null) return false;
            if (!state.onlyShowDeletedChats && chat.deleted_at != null) return false;
            if (state.chatListSearchValue == '') return true;
            if (chat.type == 'group') {
                return chat.chat_name
                    .toLocaleLowerCase()
                    .includes(state.chatListSearchValue.toLocaleLowerCase());
            } else {
                const otherSender = chat.users.find((u) => u.id != creds?.user_id);
                if (otherSender != undefined) {
                    return otherSender.name
                        .toLocaleLowerCase()
                        .includes(state.chatListSearchValue.toLocaleLowerCase());
                } else {
                    return false;
                }
            }
        });
    }, [
        rootStore.socketStore.chatList,
        state.chatListSearchValue,
        state.onlyShowDeletedChats,
    ]);

    /// Интерфейсные функции
    /**
     * Выбор чата из списка
     * @param selectedChatId ID чата
     */
    function onChatSelected(selectedChatId: TChat['id']) {
        if (useCase == ChatPageUseCase.Default) {
            navigate(`/chats/${selectedChatId}`);
        } else {
            navigate(`/session/chats/${selectedChatId}`);
        }
    }

    /**
     * Форматирование времени отправки последнего сообщения
     * @param msg Объект сообщения
     * @returns HH:mm, день недели или дата
     */
    function formatMessageDatetime(msg: TChatMessage) {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const dt = dayjs.utc(msg.created_at).tz(timezone);
        if (dt.isSame(dayjs.utc().tz(timezone), 'day')) {
            return dt.format('HH:mm');
        }
        if (dt.diff(dayjs.utc().tz(timezone), 'days') < 7) {
            return Common.weekdayNumToShortString(+dt.format('d'));
        } else {
            return dt.format('DD.MM');
        }
    }

    /**
     * Получить соответствующее типу название чата
     * @param chat Объект чата
     * @returns Название чата
     */
    function getChatName(chat: TChatExtended) {
        if (chat == null) return '';
        if (chat.type == 'group') {
            return chat.chat_name;
        } else {
            const otherSender = chat.users.find((cui) => cui.id != creds?.user_id);
            if (otherSender == undefined || otherSender?.name == null) return 'Неизвестный';
            else return otherSender.name;
        }
    }

    /**
     * Получить отправителя последнего сообщения
     * @param chat Объект чата
     * @returns Имя отправителя
     */
    function getMessageSenderName(
        chat: TChatExtended,
        sender_id: TChatMessage['sender_id'] = null,
    ) {
        if (chat.messages.length == 0) return null;
        const msgSenderId = sender_id == null ? chat.messages[0].sender_id : sender_id;
        if (msgSenderId == creds?.user_id) return 'Вы';
        else {
            const sender = chat.users.find((cui) => cui.id == msgSenderId);
            if (sender == undefined || sender?.name == null) return 'Неизвестный';
            else return sender.name;
        }
    }

    async function openNewChatDialog() {
        if (!Permissions.checkPermission(Permissions.ChatCreate)) {
            messageApi.error('Нет прав на создание чата');
            return;
        }
        state.newChatDialogOpen = true;
    }

    // Функция вычисления высоты InfiniteScroll при изменениях высоты контейнера
    const calcInfScrollHeight = debounce(() => {
        if (chatMessagesScrollRef?.current?.clientHeight == null) return;
        state.infScrollHeight = chatMessagesScrollRef?.current?.clientHeight;
    }, 300);

    async function changeChatListMode(key: string) {
        state.chatListSearchValue = '';
        if (key == 'incoming') {
            state.onlyShowDeletedChats = false;
        } else {
            state.chatListEditing = false;
            state.onlyShowDeletedChats = true;
        }
        if (chatId != undefined) {
            if (useCase == ChatPageUseCase.Default) {
                navigate('/chats');
            } else {
                navigate('/session/chats');
            }
        }
        state.isLoading = true;
        const result = await rootStore.socketStore.getChatList(
            undefined,
            true,
            state.onlyShowDeletedChats,
        );
        if (result != null) {
            state.isLoading = false;
            messageApi.error(result.text);
            if (result.redir) {
                if (useCase == ChatPageUseCase.Default) {
                    navigate('/chats');
                } else {
                    navigate('/session/chats');
                }
            }
            return;
        }
        state.isLoading = false;
    }

    useEffect(() => {
        calcInfScrollHeight();
    }, [state.chatMode]);

    async function deleteChat(chatItem: TChatExtended) {
        state.isLoading = true;
        const tempChatId = chatId;
        if (chatItem.id == tempChatId) {
            if (useCase == ChatPageUseCase.Default) {
                navigate('/chats');
            } else {
                navigate('/session/chats');
            }
        }
        try {
            const deleteResult = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.deleteChat(chatItem.id);
            });
            if (deleteResult.errorMessages) throw deleteResult.errorMessages;
            const result = await rootStore.socketStore.getChatList(
                undefined,
                true,
                state.onlyShowDeletedChats,
            );
            if (result != null) {
                state.isLoading = false;
                messageApi.error(result.text);
                if (result.redir) {
                    if (useCase == ChatPageUseCase.Default) {
                        navigate('/chats');
                    } else {
                        navigate('/session/chats');
                    }
                }
                return;
            }
            if (tempChatId != undefined) {
                await changeChatListMode('deleted');
                if (useCase == ChatPageUseCase.Default) {
                    navigate(`/chats/${tempChatId}`);
                } else {
                    navigate(`/session/chats/${tempChatId}`);
                }
            }
        } catch (errors) {
            messageApi.error('Ошибка при удалении чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function restoreChat(chatItem: TChatExtended) {
        state.isLoading = true;
        const tempChatId = chatId;
        if (chatItem.id == tempChatId) {
            if (useCase == ChatPageUseCase.Default) {
                navigate('/chats');
            } else {
                navigate('/session/chats');
            }
        }
        try {
            const restoreResult = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.restoreChat(chatItem.id);
            });
            if (restoreResult.errorMessages) throw restoreResult.errorMessages;
            const result = await rootStore.socketStore.getChatList(
                undefined,
                true,
                state.onlyShowDeletedChats,
            );
            if (result != null) {
                state.isLoading = false;
                messageApi.error(result.text);
                if (result.redir) {
                    if (useCase == ChatPageUseCase.Default) {
                        navigate('/chats');
                    } else {
                        navigate('/session/chats');
                    }
                }
                return;
            }
            if (tempChatId != undefined) {
                await changeChatListMode('incoming');
                if (useCase == ChatPageUseCase.Default) {
                    navigate(`/chats/${tempChatId}`);
                } else {
                    navigate(`/session/chats/${tempChatId}`);
                }
            }
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function searchMessageList() {
        if (
            state.messageListVisibleSearchValue.length < 3 ||
            state.messageListVisibleSearchValue.length > 50
        ) {
            messageApi.warning('Поиск от 3 до 50 символов');
            return;
        }
        state.messageListTrueSearchValue = state.messageListVisibleSearchValue;
        state.isLoading = true;
        await rootStore.socketStore.fetchChatMessages(chatId, state.messageListTrueSearchValue);
        state.isLoading = false;
    }

    async function updateChatName() {
        state.isLoading = true;
        state.onlyShowDeletedChats = false;
        try {
            const updChat = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.updateChat({
                    ...rootStore.socketStore.currentChat,
                    chat_name: state.tempChatName,
                    users: rootStore.socketStore.currentChat?.users.map((u) => u.id),
                });
            });
            if (updChat.errorMessages) throw updChat.errorMessages;
            await rootStore.socketStore.fetchChatList(true, state.onlyShowDeletedChats);
        } catch (errors) {
            messageApi.error('Ошибка при смене названия чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    const curUserInChat = useCallback(() => {
        return rootStore.socketStore.currentChat?.users.find((u) => u.id == creds?.user_id);
    }, [
        rootStore.socketStore.currentChat?.id,
        rootStore.socketStore.currentChat?.users,
    ]);

    const allowAddChatUser = useCallback(() => {
        return (
            Permissions.checkPermission(Permissions.UserChatCreate) &&
            rootStore.socketStore.currentChat?.type == 'group' &&
            (rootStore.socketStore.currentChat?.creator_id == creds?.user_id ||
                rootStore.currentUserStore.getUser?.role == 'Roles.Admin')
        );
    }, [
        rootStore.socketStore.currentChat?.id,
        rootStore.socketStore.currentChat?.users,
    ]);

    async function makeChatAdmin(chat_id: TChat['id'], user_id: TChatUser['id']) {
        state.isLoading = true;
        state.onlyShowDeletedChats = false;
        try {
            const updChat = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.makeUserChatAdmin(chat_id, user_id);
            });
            if (updChat.errorMessages) throw updChat.errorMessages;
            await rootStore.socketStore.fetchChatList(true, state.onlyShowDeletedChats);
        } catch (errors) {
            messageApi.error('Ошибка при повышении пользователя чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function unmakeChatAdmin(chat_id: TChat['id'], user_id: TChatUser['id']) {
        state.isLoading = true;
        state.onlyShowDeletedChats = false;
        try {
            const updChat = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.revokeUserChatAdmin(chat_id, user_id);
            });
            if (updChat.errorMessages) throw updChat.errorMessages;
            await rootStore.socketStore.fetchChatList(true, state.onlyShowDeletedChats);
        } catch (errors) {
            messageApi.error('Ошибка при понижении пользователя чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function banChatUser(chat_id: TChat['id'], user_id: TChatUser['id']) {
        state.isLoading = true;
        state.onlyShowDeletedChats = false;
        try {
            const updChat = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.banChatUser(chat_id, user_id);
            });
            if (updChat.errorMessages) throw updChat.errorMessages;
            await rootStore.socketStore.fetchChatList(true, state.onlyShowDeletedChats);
        } catch (errors) {
            messageApi.error('Ошибка при бане пользователя чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function unbanChatUser(chat_id: TChat['id'], user_id: TChatUser['id']) {
        state.isLoading = true;
        state.onlyShowDeletedChats = false;
        try {
            const updChat = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.unbanChatUser(chat_id, user_id);
            });
            if (updChat.errorMessages) throw updChat.errorMessages;
            await rootStore.socketStore.fetchChatList(true, state.onlyShowDeletedChats);
        } catch (errors) {
            messageApi.error('Ошибка при разбане пользователя чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function addChatUser() {
        state.isLoading = true;
        state.onlyShowDeletedChats = false;
        state.userPickerOpen = false;
        try {
            const updChat = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.addUserToChat(chatId, state.userPickerTempSelect[0]);
            });
            if (updChat.errorMessages) throw updChat.errorMessages;
            state.userPickerTempSelect = [];
            await rootStore.socketStore.fetchChatList(true, state.onlyShowDeletedChats);
        } catch (errors) {
            messageApi.error('Ошибка при добавлении пользователя чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function initAddingChatUser() {
        if (state.userList.length == 0) {
            state.isLoading = true;
            try {
                const userList = await CRMAPIManager.request<UserListResp>(async (api) => {
                    return await api.getUserList({
                        query: null,
                        role: null,
                        page: 1,
                        per_page: 100,
                    });
                });
                if (userList.errorMessages) throw userList.errorMessages;
                state.userList = userList.data.data;
            } catch (errors) {
                messageApi.error('Ошибка при загрузке списка пользователей');
                console.log(errors);
            }
            state.isLoading = false;
        }
        state.userPickerOpen = true;
    }

    return (
        <div className="chats-fullpage">
            {state.isLoading && <Loader />}
            {contextHolder}
            <NewChatDialog
                isLoading={state.isLoading}
                isOpen={state.newChatDialogOpen}
                messageApi={messageApi}
                setIsLoading={(value) => (state.isLoading = value)}
                setIsOpen={(value) => (state.newChatDialogOpen = value)}
                useCase={useCase}
            />
            {state.userPickerOpen && (
                <UserDrawer
                    isOpen={state.userPickerOpen}
                    multiple={false}
                    onConfirm={addChatUser}
                    onSelect={(users: TUser['id'][]) => {
                        state.userPickerTempSelect = users;
                    }}
                    selected={state.userPickerTempSelect}
                    setIsOpen={(value) => (state.userPickerOpen = value)}
                    title="Добавить пользователя"
                    useCase={UserDrawerUseCase.All}
                    userList={state.userList.filter((u) => {
                        return (
                            rootStore.socketStore.currentChat?.users.find((cu) => cu.id == u.id) ==
                            undefined
                        );
                    })}
                />
            )}
            <Col
                className={`chat-list ${state.chatListMinimized ? 'chat-list-minimized' : ''}`}
                ref={chatListColRef}
            >
                <Row className="chat-list-search">
                    {!state.chatListMinimized && (
                        <Search
                            className="chat-list-search-input"
                            enterButton={<div className="search-icon" />}
                            onChange={(e) => (state.chatListSearchValue = e.target.value)}
                            placeholder="Поиск чатов"
                            size="large"
                            value={state.chatListSearchValue}
                        />
                    )}
                    {state.chatListMinimized && (
                        <Button
                            className="chat-list-search-button"
                            icon={<div className="search-icon" />}
                            type="primary"
                        >
                            Поиск
                        </Button>
                    )}
                </Row>
                <Row className="chat-group-list">
                    <Col>
                        <Row className="chat-group-list-header">
                            <Col>
                                <Row className="chat-group-list-header-new">
                                    <Col className="new-messages-text">
                                        <Dropdown
                                            className="mode-select"
                                            menu={{
                                                items: [
                                                    {
                                                        key: 'incoming',
                                                        label: <span className="p2">Входящие</span>,
                                                    },
                                                    {
                                                        key: 'deleted',
                                                        label: <span className="p2">Корзина</span>,
                                                    },
                                                ],
                                                onClick: (info) => changeChatListMode(info.key),
                                                selectedKeys: state.onlyShowDeletedChats
                                                    ? ['deleted']
                                                    : ['incoming'],
                                            }}
                                            overlayClassName="p2"
                                        >
                                            <Row className="mode-select-label">
                                                <span className="p2">
                                                    {state.onlyShowDeletedChats
                                                        ? 'Корзина'
                                                        : 'Входящие'}
                                                </span>
                                                <div className="chevron-down-icon" />
                                            </Row>
                                        </Dropdown>
                                    </Col>
                                    {rootStore.socketStore.totalNewMessages > 0 && (
                                        <Col className="new-messages-badge">
                                            <Row>
                                                <Col className="new-messages-badge-dot" />
                                                <Col className="p3 new-messages-badge-text">
                                                    {rootStore.socketStore.totalNewMessages} new
                                                </Col>
                                            </Row>
                                        </Col>
                                    )}
                                </Row>
                            </Col>
                            <Col>
                                {!state.onlyShowDeletedChats && (
                                    <Row className="chat-group-list-header-edit">
                                        {Permissions.checkPermission(Permissions.ChatCreate) && (
                                            <Tooltip title="Новый чат">
                                                <Button
                                                    className="new-chat-btn"
                                                    icon={<div className="plus-icon" />}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        openNewChatDialog();
                                                    }}
                                                />
                                            </Tooltip>
                                        )}
                                        {Permissions.checkPermission(Permissions.ChatDelete) && (
                                            <Tooltip
                                                title={
                                                    state.chatListEditing
                                                        ? 'Отмена редактирования'
                                                        : 'Редактирование списка чатов'
                                                }
                                            >
                                                <Button
                                                    className={`edit-chats-btn${state.chatListEditing ? ' editing' : ''}`}
                                                    icon={
                                                        <div
                                                            className={
                                                                state.chatListEditing
                                                                    ? 'cross-icon'
                                                                    : 'edit-icon'
                                                            }
                                                        />
                                                    }
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.chatListEditing =
                                                            !state.chatListEditing;
                                                    }}
                                                />
                                            </Tooltip>
                                        )}
                                    </Row>
                                )}
                            </Col>
                        </Row>
                        <Row className="chat-group-list-body">
                            <List
                                className="group-card-chat-list"
                                dataSource={state.chatList}
                                itemLayout="vertical"
                                locale={{
                                    emptyText: (
                                        <Col className="empty-text p3">
                                            <Row>Таких чатов нет :)</Row>
                                        </Col>
                                    ),
                                }}
                                renderItem={(chatItem) => {
                                    const chatName = getChatName(chatItem);
                                    const lastMessageSender = getMessageSenderName(chatItem);

                                    return (
                                        <Row
                                            className={`chat-card${
                                                chatItem.id == chatId ? ' chat-card-selected' : ''
                                            }${
                                                chatItem.creator_id == creds?.user_id &&
                                                (state.onlyShowDeletedChats ||
                                                    (!state.onlyShowDeletedChats &&
                                                        state.chatListEditing))
                                                    ? ' action'
                                                    : ''
                                            }`}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                if (chatItem.id != chatId)
                                                    onChatSelected(chatItem.id);
                                            }}
                                        >
                                            <Col className="chat-avatar">
                                                <div className="p1-strong">{chatName[0]}</div>
                                            </Col>
                                            <Col className="chat-name-text">
                                                <Row className="p3 chat-name">{chatName}</Row>
                                                <Row className="desc-m chat-text">
                                                    {lastMessageSender != null && (
                                                        <Col className="chat-text-sender">
                                                            {lastMessageSender}
                                                        </Col>
                                                    )}
                                                    {lastMessageSender != null && (
                                                        <Col className="chat-text-semicolon">:</Col>
                                                    )}
                                                    <Col
                                                        className={
                                                            lastMessageSender == 'Вы'
                                                                ? 'chat-text-body my-message'
                                                                : lastMessageSender != null
                                                                  ? 'chat-text-body'
                                                                  : 'chat-text-body no-messages'
                                                        }
                                                    >
                                                        {lastMessageSender == null
                                                            ? 'Нет сообщений'
                                                            : chatItem.messages[0].text}
                                                    </Col>
                                                </Row>
                                            </Col>
                                            {lastMessageSender != null && (
                                                <Col className="chat-status">
                                                    <Row className="p3 chat-status-datetime">
                                                        {formatMessageDatetime(
                                                            chatItem.messages[0],
                                                        )}
                                                    </Row>
                                                    {chatItem.newMessages > 0 && (
                                                        <Row className="chat-status-badge">
                                                            <Col className="chat-status-badge-dot" />
                                                            <Col className="desc-l chat-status-badge-text">
                                                                {chatItem.newMessages > 99
                                                                    ? '99+'
                                                                    : chatItem.newMessages}
                                                            </Col>
                                                        </Row>
                                                    )}
                                                </Col>
                                            )}
                                            {(chatItem.creator_id == creds?.user_id ||
                                                rootStore.currentUserStore.getUser?.role ==
                                                    'Roles.Admin') &&
                                                Permissions.checkPermission(
                                                    Permissions.ChatRestore,
                                                ) &&
                                                state.onlyShowDeletedChats && (
                                                    <Tooltip title="Восстановить чат">
                                                        <Button
                                                            className="restore-btn"
                                                            icon={<div className="restore-icon" />}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                restoreChat(chatItem);
                                                            }}
                                                            type="text"
                                                        />
                                                    </Tooltip>
                                                )}
                                            {(chatItem.creator_id == creds?.user_id ||
                                                rootStore.currentUserStore.getUser?.role ==
                                                    'Roles.Admin') &&
                                                Permissions.checkPermission(
                                                    Permissions.ChatDelete,
                                                ) &&
                                                !state.onlyShowDeletedChats &&
                                                state.chatListEditing && (
                                                    <Tooltip title="Удалить чат">
                                                        <Popconfirm
                                                            cancelText="Отмена"
                                                            okText="ОК"
                                                            onCancel={(e) => {
                                                                e.stopPropagation();
                                                            }}
                                                            onConfirm={(e) => {
                                                                e.stopPropagation();
                                                                deleteChat(chatItem);
                                                            }}
                                                            title="Чат будет удалён для всех участников"
                                                        >
                                                            <Button
                                                                className="delete-btn"
                                                                icon={
                                                                    <div className="delete-icon" />
                                                                }
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                }}
                                                                type="text"
                                                            />
                                                        </Popconfirm>
                                                    </Tooltip>
                                                )}
                                        </Row>
                                    );
                                }}
                            />
                        </Row>
                    </Col>
                </Row>
            </Col>
            {chatId == undefined ? (
                <Col className="no-chat-selected">
                    <Row className="p2 no-chat-text">Пожалуйста, выберите чат :)</Row>
                </Col>
            ) : (
                <Col className="chat-inner">
                    {state.chatMode == 'default' && (
                        <ChatChannelHolder
                            chatId={rootStore.socketStore.currentChat?.id}
                            chatType={rootStore.socketStore.currentChat?.type}
                        />
                    )}
                    <Row
                        className="chat-header"
                        onClick={(e) => {
                            e.stopPropagation();
                            if (state.chatMode == 'search') return;
                            state.tempChatName = rootStore.socketStore.currentChat?.chat_name;
                            state.chatMode = 'info';
                        }}
                    >
                        {state.chatMode != 'search' && (
                            <Col className="chat-header-info">
                                <Row>
                                    {state.chatMode == 'info' && (
                                        <Col className="chat-info-back">
                                            <Tooltip title="Обратно">
                                                <Button
                                                    className="info-back-btn"
                                                    icon={<div className="back-icon" />}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.chatMode = 'default';
                                                    }}
                                                    type="text"
                                                />
                                            </Tooltip>
                                        </Col>
                                    )}
                                    <Col className="chat-avatar">
                                        <div className="p1-strong">
                                            {getChatName(rootStore.socketStore.currentChat)[0]}
                                        </div>
                                    </Col>
                                    <Col className="p3 chat-name">
                                        {getChatName(rootStore.socketStore.currentChat)}
                                    </Col>
                                </Row>
                            </Col>
                        )}
                        {state.chatMode == 'default' && (
                            <Col className="chat-header-actions">
                                <Row>
                                    <Tooltip title="Поиск сообщений">
                                        <Button
                                            className="chat-header-search"
                                            icon={<div className="search-icon" />}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                state.messageListTrueSearchValue = '';
                                                state.messageListVisibleSearchValue = '';
                                                state.chatMode = 'search';
                                            }}
                                            type="text"
                                        />
                                    </Tooltip>
                                    <Popover
                                        content={
                                            <Col className="desc-l chat-actions">
                                                <Button
                                                    className="chat-info"
                                                    icon={<div className="info-icon" />}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.tempChatName =
                                                            rootStore.socketStore.currentChat?.chat_name;
                                                        state.chatMode = 'info';
                                                    }}
                                                    type="text"
                                                >
                                                    О чате
                                                </Button>
                                                {rootStore.socketStore.currentChat?.deleted_at ==
                                                    null && (
                                                    <Popconfirm
                                                        cancelText="Отмена"
                                                        disabled={
                                                            !(
                                                                (rootStore.socketStore.currentChat
                                                                    ?.creator_id ==
                                                                    creds?.user_id ||
                                                                    rootStore.currentUserStore
                                                                        .getUser?.role ==
                                                                        'Roles.Admin') &&
                                                                Permissions.checkPermission(
                                                                    Permissions.ChatDelete,
                                                                )
                                                            )
                                                        }
                                                        okText="ОК"
                                                        onCancel={(e) => {
                                                            e.stopPropagation();
                                                        }}
                                                        onConfirm={(e) => {
                                                            e.stopPropagation();
                                                            deleteChat(
                                                                rootStore.socketStore.currentChat,
                                                            );
                                                        }}
                                                        title="Чат будет удалён для всех участников"
                                                    >
                                                        <Button
                                                            className="chat-delete"
                                                            disabled={
                                                                !(
                                                                    (rootStore.socketStore
                                                                        .currentChat?.creator_id ==
                                                                        creds?.user_id ||
                                                                        rootStore.currentUserStore
                                                                            .getUser?.role ==
                                                                            'Roles.Admin') &&
                                                                    Permissions.checkPermission(
                                                                        Permissions.ChatDelete,
                                                                    )
                                                                )
                                                            }
                                                            icon={<div className="delete-icon" />}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                            }}
                                                            type="text"
                                                        >
                                                            Удалить
                                                        </Button>
                                                    </Popconfirm>
                                                )}
                                                {rootStore.socketStore.currentChat?.deleted_at !=
                                                    null && (
                                                    <Tooltip title="Восстановить чат">
                                                        <Button
                                                            className="chat-restore"
                                                            disabled={
                                                                !(
                                                                    (rootStore.socketStore
                                                                        .currentChat?.creator_id ==
                                                                        creds?.user_id ||
                                                                        rootStore.currentUserStore
                                                                            .getUser?.role ==
                                                                            'Roles.Admin') &&
                                                                    Permissions.checkPermission(
                                                                        Permissions.ChatRestore,
                                                                    )
                                                                )
                                                            }
                                                            icon={<div className="restore-icon" />}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                restoreChat(
                                                                    rootStore.socketStore
                                                                        .currentChat,
                                                                );
                                                            }}
                                                            type="text"
                                                        >
                                                            Восстановить
                                                        </Button>
                                                    </Tooltip>
                                                )}
                                            </Col>
                                        }
                                        placement="bottomRight"
                                        title={null}
                                        trigger="click"
                                    >
                                        <Tooltip title="Опции">
                                            <Button
                                                className="chat-header-more"
                                                icon={<div className="more-icon" />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                }}
                                                type="text"
                                            />
                                        </Tooltip>
                                    </Popover>
                                </Row>
                            </Col>
                        )}
                        {state.chatMode == 'search' && (
                            <Col className="chat-header-info search">
                                <Row>
                                    <Col className="chat-search-back">
                                        <Tooltip title="Обратно">
                                            <Button
                                                className="info-back-btn"
                                                icon={<div className="back-icon" />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    state.chatMode = 'default';
                                                }}
                                                type="text"
                                            />
                                        </Tooltip>
                                    </Col>
                                    <Col className="chat-search">
                                        <Search
                                            className="chat-search-input"
                                            enterButton={
                                                <div
                                                    className="search-icon"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        searchMessageList();
                                                    }}
                                                />
                                            }
                                            maxLength={50}
                                            minLength={3}
                                            onChange={(e) =>
                                                (state.messageListVisibleSearchValue =
                                                    e.target.value)
                                            }
                                            onClick={(e) => {
                                                e.stopPropagation();
                                            }}
                                            onSearch={() => {
                                                searchMessageList();
                                            }}
                                            placeholder="Поиск сообщений"
                                            size="large"
                                            value={state.messageListVisibleSearchValue}
                                        />
                                    </Col>
                                </Row>
                            </Col>
                        )}
                    </Row>
                    {state.chatMode != 'info' && (
                        <Row
                            className={`chat-messages-scroll${state.chatMode == 'search' ? ' search' : ''}`}
                            ref={chatMessagesScrollRef}
                        >
                            {rootStore.socketStore.currentChat?.messages?.length > 0 ? (
                                <div className="messages-scroll-inner">
                                    <InfiniteScroll
                                        className="messages-scroll-inf"
                                        dataLength={
                                            rootStore.socketStore.currentChat.messages.length + 1
                                        }
                                        hasMore={currentChatHasMore()}
                                        height={state.infScrollHeight}
                                        inverse={true}
                                        loader={
                                            <div className="p3 messages-scroll-inf-loader">
                                                Загружаем сообщения...
                                            </div>
                                        }
                                        next={loadMoreChatMessages}
                                        scrollableTarget="messages-scroll-inner"
                                    >
                                        {rootStore.socketStore.currentChat.messages.map((msg) => {
                                            const msgSender = getMessageSenderName(
                                                rootStore.socketStore.currentChat,
                                                msg.sender_id,
                                            );
                                            return (
                                                <Row
                                                    key={msg.id}
                                                    className={`chat-message-item ${msg.sender_id == creds?.user_id ? 'mine' : 'not-mine'}`}
                                                >
                                                    <Col className="sender-avatar-col">
                                                        <div className="p1-strong sender-avatar">
                                                            {msgSender[0]}
                                                        </div>
                                                    </Col>
                                                    <Col className="message-sender-text">
                                                        <Row className="p3 message-sender">
                                                            {msgSender}
                                                        </Row>
                                                        <Popover
                                                            content={
                                                                <Col className="desc-l message-actions">
                                                                    <Button
                                                                        className="message-edit"
                                                                        disabled
                                                                        type="text"
                                                                    >
                                                                        Изменить
                                                                    </Button>
                                                                    <Button
                                                                        className="message-delete"
                                                                        disabled
                                                                        type="text"
                                                                    >
                                                                        Удалить
                                                                    </Button>
                                                                </Col>
                                                            }
                                                            placement={
                                                                msg.sender_id == creds?.user_id
                                                                    ? 'bottomRight'
                                                                    : 'bottomLeft'
                                                            }
                                                            title={null}
                                                            trigger="click"
                                                        >
                                                            <Row className="p3 message-text">
                                                                <Col>
                                                                    {msg.text}
                                                                    <div className="message-time-status">
                                                                        <Tooltip
                                                                            placement={
                                                                                msg.sender_id ==
                                                                                creds?.user_id
                                                                                    ? 'bottomRight'
                                                                                    : 'bottomLeft'
                                                                            }
                                                                            title={Common.formatDateString(
                                                                                msg.created_at,
                                                                            )}
                                                                        >
                                                                            <div className="message-time">
                                                                                {dayjs
                                                                                    .utc(
                                                                                        msg.created_at,
                                                                                    )
                                                                                    .tz(
                                                                                        Intl.DateTimeFormat().resolvedOptions()
                                                                                            .timeZone,
                                                                                    )
                                                                                    .format(
                                                                                        'HH:mm',
                                                                                    )}
                                                                            </div>
                                                                        </Tooltip>
                                                                        {msg.sender_id ==
                                                                            creds?.user_id && (
                                                                            <div className="message-status">
                                                                                <FontAwesomeIcon
                                                                                    icon={
                                                                                        msg.statuses?.find(
                                                                                            (sti) =>
                                                                                                sti.status ==
                                                                                                'read',
                                                                                        )
                                                                                            ? faCheckDouble
                                                                                            : faCheck
                                                                                    }
                                                                                />
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </Col>
                                                            </Row>
                                                        </Popover>
                                                    </Col>
                                                </Row>
                                            );
                                        })}
                                    </InfiniteScroll>
                                </div>
                            ) : (
                                <div className="p2 no-messages-in-chat">
                                    Сообщений пока нет. Напишите первым :)
                                </div>
                            )}
                        </Row>
                    )}
                    {state.chatMode == 'default' && (
                        <Row className="chat-inputs">
                            <Col className="emoji-input">
                                {rootStore.socketStore.currentChat?.deleted_at == null ? (
                                    rootStore.socketStore.currentChat?.users.find(
                                        (u) => u.id == creds?.user_id,
                                    ) != undefined ? (
                                        <Popover
                                            content={
                                                <EmojiPicker
                                                    emojiStyle={EmojiStyle.NATIVE}
                                                    lazyLoadEmojis
                                                    onEmojiClick={(emoji) => {
                                                        state.messageInputText += emoji.emoji;
                                                    }}
                                                    skinTonesDisabled={true}
                                                />
                                            }
                                            placement="topLeft"
                                            title={null}
                                        >
                                            <Button
                                                className="emoji-button"
                                                icon={<div className="emoji-icon" />}
                                                type="text"
                                            />
                                        </Popover>
                                    ) : (
                                        <Tooltip title="Вы не состоите в участниках чата, ввод запрещён">
                                            <Button
                                                className="emoji-button"
                                                disabled
                                                icon={<div className="emoji-icon" />}
                                                type="text"
                                            />
                                        </Tooltip>
                                    )
                                ) : (
                                    <Tooltip title="Чат удалён, ввод запрещён">
                                        <Button
                                            className="emoji-button"
                                            disabled
                                            icon={<div className="emoji-icon" />}
                                            type="text"
                                        />
                                    </Tooltip>
                                )}
                            </Col>
                            <Col className="main-input">
                                <TextArea
                                    autoSize={{ minRows: 1, maxRows: 12 }}
                                    className="main-input-textarea"
                                    disabled={
                                        rootStore.socketStore.currentChat?.deleted_at != null ||
                                        rootStore.socketStore.currentChat?.users.find(
                                            (u) => u.id == creds?.user_id,
                                        ) == undefined
                                    }
                                    onChange={(e) => {
                                        state.messageInputText = e.target.value;
                                    }}
                                    onPressEnter={(e) => {
                                        e.preventDefault();
                                        if (state.messageInputText.trim().length != 0) {
                                            sendMessage();
                                        }
                                    }}
                                    onResize={() => calcInfScrollHeight()}
                                    placeholder={
                                        rootStore.socketStore.currentChat?.deleted_at == null
                                            ? 'Ответить'
                                            : 'Чат удалён, ввод запрещён'
                                    }
                                    value={state.messageInputText}
                                />
                            </Col>
                            <Col className="attach-files-send">
                                <Row>
                                    <Col className="attach-files">
                                        <Tooltip
                                            title={
                                                rootStore.socketStore.currentChat?.deleted_at ==
                                                null
                                                    ? 'Выбрать изображение'
                                                    : 'Чат удалён, ввод запрещён'
                                            }
                                        >
                                            <Button
                                                className="attach-image"
                                                disabled={
                                                    rootStore.socketStore.currentChat?.deleted_at !=
                                                        null ||
                                                    rootStore.socketStore.currentChat?.users.find(
                                                        (u) => u.id == creds?.user_id,
                                                    ) == undefined
                                                }
                                                icon={<div className="image-icon" />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    messageApi.info('Тут будет выбор файлов');
                                                }}
                                                type="text"
                                            />
                                        </Tooltip>
                                    </Col>
                                    <Col className="send-btn">
                                        {state.messageInputText.length > charLimit && (
                                            <div className="p3 too-much-text">
                                                {`-${
                                                    state.messageInputText.length - charLimit > 999
                                                        ? 999
                                                        : state.messageInputText.length - charLimit
                                                }`}
                                            </div>
                                        )}
                                        <Button
                                            className="send-message"
                                            disabled={
                                                rootStore.socketStore.currentChat?.deleted_at !=
                                                    null ||
                                                state.messageInputText == '' ||
                                                state.messageInputText.length > charLimit ||
                                                rootStore.socketStore.currentChat?.users.find(
                                                    (u) => u.id == creds?.user_id,
                                                ) == undefined
                                            }
                                            icon={<div className="send-arrow-icon" />}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                sendMessage();
                                            }}
                                            type="primary"
                                        />
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    )}
                    {state.chatMode == 'info' && (
                        <Row className="chat-info">
                            <Col>
                                {rootStore.socketStore.currentChat.type == 'group' && (
                                    <Row className="chat-name-row">
                                        <Col flex={1}>
                                            <Input
                                                disabled={
                                                    !Permissions.checkPermission(
                                                        Permissions.ChatUpdate,
                                                    ) ||
                                                    rootStore.socketStore.currentChat?.deleted_at !=
                                                        null
                                                }
                                                maxLength={50}
                                                onChange={(e) =>
                                                    (state.tempChatName = e.target.value)
                                                }
                                                placeholder="Введите название чата..."
                                                showCount
                                                value={state.tempChatName}
                                            />
                                        </Col>
                                        <Col>
                                            <Tooltip title="Сохранить название чата">
                                                <Button
                                                    className="save-chat-name"
                                                    disabled={
                                                        !Permissions.checkPermission(
                                                            Permissions.ChatUpdate,
                                                        ) ||
                                                        state.tempChatName ==
                                                            rootStore.socketStore.currentChat
                                                                ?.chat_name ||
                                                        rootStore.socketStore.currentChat
                                                            ?.deleted_at != null
                                                    }
                                                    icon={<div className="confirm-icon" />}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        updateChatName();
                                                    }}
                                                />
                                            </Tooltip>
                                        </Col>
                                        <Col>
                                            <Tooltip title="Вернуть название чата">
                                                <Button
                                                    className="return-chat-name"
                                                    disabled={
                                                        !Permissions.checkPermission(
                                                            Permissions.ChatUpdate,
                                                        ) ||
                                                        state.tempChatName ==
                                                            rootStore.socketStore.currentChat
                                                                ?.chat_name ||
                                                        rootStore.socketStore.currentChat
                                                            ?.deleted_at != null
                                                    }
                                                    icon={<div className="cancel-icon" />}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.tempChatName =
                                                            rootStore.socketStore.currentChat?.chat_name;
                                                    }}
                                                />
                                            </Tooltip>
                                        </Col>
                                    </Row>
                                )}
                                <Row className="chat-users">
                                    <Col>
                                        <Row className="p2 chat-users-label">
                                            {`Участники ${rootStore.socketStore.currentChat?.type == 'group' ? 'группового чата' : 'диалога'}`}
                                        </Row>
                                        <Row className="chat-users-body">
                                            <List
                                                className="chat-user-list"
                                                dataSource={
                                                    rootStore.socketStore.currentChat?.users
                                                }
                                                itemLayout="vertical"
                                                renderItem={(chatUser) => {
                                                    const adminOrCreator =
                                                        rootStore.currentUserStore.getUser?.role ==
                                                            'Roles.Admin' ||
                                                        rootStore.socketStore.currentChat
                                                            ?.creator_id == creds?.user_id;
                                                    const allowBan =
                                                        chatUser.id != creds?.user_id &&
                                                        adminOrCreator &&
                                                        Permissions.checkPermission(
                                                            Permissions.UserChatBan,
                                                        );
                                                    const allowUnban =
                                                        chatUser.id != creds?.user_id &&
                                                        adminOrCreator &&
                                                        Permissions.checkPermission(
                                                            Permissions.UserChatUnban,
                                                        );
                                                    const allowMakeAdmin =
                                                        chatUser.id != creds?.user_id &&
                                                        adminOrCreator &&
                                                        Permissions.checkPermission(
                                                            Permissions.UserChatMakeAdmin,
                                                        );
                                                    const allowUnmakeAdmin =
                                                        chatUser.id != creds?.user_id &&
                                                        adminOrCreator &&
                                                        Permissions.checkPermission(
                                                            Permissions.UserChatUnmakeAdmin,
                                                        );
                                                    return (
                                                        <Row className="chat-user-card">
                                                            <Col>
                                                                <Row className="chat-user-avatar-name">
                                                                    <Col className="chat-user-avatar">
                                                                        <div className="p1-strong">
                                                                            {chatUser.name[0]}
                                                                        </div>
                                                                    </Col>
                                                                    <Col className="chat-user-name">
                                                                        <Row className="p3 user-name">
                                                                            {chatUser.name}
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                            </Col>
                                                            <Col>
                                                                <Row className="chat-user-actions">
                                                                    <Tooltip title="Открыть профиль">
                                                                        <Button
                                                                            className="open-btn"
                                                                            disabled={
                                                                                useCase ==
                                                                                ChatPageUseCase.Ingame
                                                                            }
                                                                            icon={
                                                                                <FontAwesomeIcon
                                                                                    icon={
                                                                                        faFolderOpen
                                                                                    }
                                                                                />
                                                                            }
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                navigate(
                                                                                    `/management/users/${chatUser.id}`,
                                                                                );
                                                                            }}
                                                                            type="text"
                                                                        />
                                                                    </Tooltip>
                                                                    <Tooltip title="Сделать админом чата">
                                                                        <Button
                                                                            className="admin-btn"
                                                                            disabled={
                                                                                !allowMakeAdmin
                                                                            }
                                                                            icon={
                                                                                <FontAwesomeIcon
                                                                                    icon={
                                                                                        faUserShield
                                                                                    }
                                                                                />
                                                                            }
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                makeChatAdmin(
                                                                                    chatId,
                                                                                    chatUser.id,
                                                                                );
                                                                            }}
                                                                            type="text"
                                                                        />
                                                                    </Tooltip>
                                                                    <Tooltip title="Отнять права админа чата">
                                                                        <Button
                                                                            className="unadmin-btn"
                                                                            disabled={
                                                                                !allowUnmakeAdmin
                                                                            }
                                                                            icon={
                                                                                <FontAwesomeIcon
                                                                                    icon={faUserPen}
                                                                                />
                                                                            }
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                unmakeChatAdmin(
                                                                                    chatId,
                                                                                    chatUser.id,
                                                                                );
                                                                            }}
                                                                            type="text"
                                                                        />
                                                                    </Tooltip>
                                                                    <Tooltip title="Забанить пользователя чата">
                                                                        <Button
                                                                            className="ban-btn"
                                                                            disabled={!allowBan}
                                                                            icon={
                                                                                <FontAwesomeIcon
                                                                                    icon={
                                                                                        faUserXmark
                                                                                    }
                                                                                />
                                                                            }
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                banChatUser(
                                                                                    chatId,
                                                                                    chatUser.id,
                                                                                );
                                                                            }}
                                                                            type="text"
                                                                        />
                                                                    </Tooltip>
                                                                    <Tooltip title="Разбанить пользователя чата">
                                                                        <Button
                                                                            className="unban-btn"
                                                                            disabled={!allowUnban}
                                                                            icon={
                                                                                <FontAwesomeIcon
                                                                                    icon={
                                                                                        faUserCheck
                                                                                    }
                                                                                />
                                                                            }
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                unbanChatUser(
                                                                                    chatId,
                                                                                    chatUser.id,
                                                                                );
                                                                            }}
                                                                            type="text"
                                                                        />
                                                                    </Tooltip>
                                                                </Row>
                                                            </Col>
                                                        </Row>
                                                    );
                                                }}
                                            />
                                        </Row>
                                        <Row className="chat-users-add">
                                            <Button
                                                className="p2 chat-user-add-btn"
                                                disabled={!allowAddChatUser()}
                                                icon={<div className="plus-icon" />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    initAddingChatUser();
                                                }}
                                            >
                                                Добавить пользователя
                                            </Button>
                                        </Row>
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    )}
                </Col>
            )}
        </div>
    );
});

export { ChatsFullpage };
