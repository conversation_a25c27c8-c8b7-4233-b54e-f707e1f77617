import { Permissions } from 'src/shared/permissions';
import { usePusherContext } from '@common/contexts/Pusher';
import { useChatPusher } from '@hooks/useChatPusher';
import { rootStore } from 'src/store/instanse';
import { observer } from 'mobx-react';
import { useEffect } from 'react';
import { TChat } from 'types/chats/chats';

interface ChatChannelHolderProps {
    chatId?: TChat['id'];
    chatType?: TChat['type'];
}

const ChatChannelConnector = ({ chatId, chatType }: ChatChannelHolderProps): JSX.Element => {
    useChatPusher({ chatId, chatType });

    return null;
};

const ChatChannelHolder = observer(({ chatId, chatType }: ChatChannelHolderProps): JSX.Element => {
    const { socketRegistered } = usePusherContext();

    useEffect(() => {
        if (rootStore.socketStore.verbose) {
            console.log(
                `ChatChannelHolder: chatId ${chatId}, chatType ${chatType}, socketRegistered ${socketRegistered}`,
            );
        }
    }, [
        chatId,
        chatType,
        socketRegistered,
    ]);

    return (
        <>
            {chatId != null &&
                chatType != null &&
                socketRegistered &&
                Permissions.checkPermission(Permissions.ChatList) &&
                Permissions.checkPermission(Permissions.ChatGet) && (
                    <ChatChannelConnector
                        chatId={chatId}
                        chatType={chatType}
                    />
                )}
        </>
    );
});

export default ChatChannelHolder;
