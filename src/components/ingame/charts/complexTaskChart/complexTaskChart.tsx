import Colors from 'src/shared/colors';
import { rootStore } from 'src/store/instanse';
import {
    Background,
    Edge,
    MarkerType,
    Node,
    ReactFlow,
    useEdgesState,
    useNodesState,
    useReactFlow,
} from '@xyflow/react';
import { useReactive } from 'ahooks';
import { observer } from 'mobx-react';
import { useEffect, useRef } from 'react';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import candleNode from './candleNode';
import lineNode from './lineNode';

import '@xyflow/react/dist/style.css';
import './complexTaskChart.scss';

type TProps = {
    useCase: 'Page' | 'Mini';
};

export type ChartNodeInner = TSessionTaskExtended & {
    column: number;
    rowHeight: number;
    type: 'candle' | 'line-current' | 'line-plan';
    useCase: TProps['useCase'];
};

const EDGE_PLAN_STYLE = {
    stroke: Colors.Accent.warm[400],
    strokeWidth: 3,
    transition: 'all 0.3s ease',
};
const EDGE_CURRENT_STYLE = {
    stroke: Colors.Accent.warm[800],
    strokeWidth: 3,
    transition: 'all 0.3s ease',
};

const EDGE_MARKER = {
    type: MarkerType.ArrowClosed,
    width: 0,
    height: 0,
    color: 'rgba(0, 0, 0, 0)',
    strokeWidth: 0,
};

const nodeTypes = {
    candleNode: candleNode,
    lineNode: lineNode,
};

type TState = {
    availableHeight: number;
    availableWidth: number;
    isLoading: boolean;
    maxBudget: number;
    maxWeight: number;
    tasks: TSessionTaskExtended[];
};

const ComplexTaskChart = observer(({ useCase }: TProps): JSX.Element => {
    const defaultHeight = 556;
    const defaultWidth = 440;
    const defaultRowGap = 8;
    const defaultLineRowRatio = 0.5;
    const state = useReactive<TState>({
        availableHeight: defaultHeight,
        availableWidth: defaultWidth,
        isLoading: false,
        maxBudget: 0,
        maxWeight: 0,
        tasks: [],
    });
    const [nodes, setNodes] = useNodesState<Node<ChartNodeInner>>([]);
    const [edges, setEdges] = useEdgesState([]);
    const chartContRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        initChart();
    }, []);

    async function initChart() {
        state.tasks = await rootStore.ingameStore.getTaskList();
        tasksToNodes();
        tasksToEdges();
    }

    function tasksToNodes() {
        const lineRowHeight = Math.floor(
            (state.availableHeight - defaultRowGap) * defaultLineRowRatio,
        );
        const candleRowHeight = Math.floor(
            (state.availableHeight - defaultRowGap) * (1 - defaultLineRowRatio),
        );
        const candleNodes: ChartNodeInner[] = [];
        for (let i = 0; i < state.tasks.length; i++) {
            const task = state.tasks[i];
            if (task.budget_current > state.maxBudget) state.maxBudget = task.budget_current;
            if (task.est_budget > state.maxBudget) state.maxBudget = task.est_budget;
        }
        const heightPerBudget = candleRowHeight / state.maxBudget;
        for (let i = 0; i < state.tasks.length; i++) {
            const task = state.tasks[i];
            candleNodes.push({
                ...task,
                column: i,
                rowHeight:
                    task.budget_current > task.est_budget
                        ? Math.floor(task.budget_current * heightPerBudget)
                        : Math.floor(task.est_budget * heightPerBudget),
                type: 'candle',
                useCase: useCase,
            });
        }
        const linePlanNodes: ChartNodeInner[] = [];
        const lineCurrentNodes: ChartNodeInner[] = [];
        const timeSettings = rootStore.ingameStore.getTimeSettings();
        const hoursInADay = timeSettings.workDayHours - +timeSettings.workDayLunchSkip;
        for (let i = 0; i < state.tasks.length; i++) {
            const task = state.tasks[i];
            const calcProgressDays =
                task.end_day != null
                    ? task.end_day - task.start_day
                    : task.start_day != null
                      ? rootStore.ingameStore.SAI.config.day - task.start_day
                      : 0;
            linePlanNodes.push({
                ...task,
                column: i,
                rowHeight: 0,
                type: 'line-plan',
                useCase: useCase,
            });
            lineCurrentNodes.push({
                ...task,
                column: i,
                rowHeight: 0,
                type: 'line-current',
                useCase: useCase,
            });
            if (task.progress > state.maxWeight) state.maxWeight = calcProgressDays * hoursInADay;
            if (task.est_duration * hoursInADay > state.maxWeight)
                state.maxWeight = task.est_duration * hoursInADay;
        }
        const tempNodes: Node<ChartNodeInner>[] = [];
        const widthPerTask = Math.floor(state.availableWidth / state.tasks.length);
        const heightPerWeight = lineRowHeight / state.maxWeight;
        const ticksInDay =
            rootStore.ingameStore.getTimeSettings().workDayHours *
            rootStore.ingameStore.SAI?.config.ticks_in_hour;
        for (let i = 0; i < lineCurrentNodes.length; i++) {
            const calcProgressDays = () => {
                if (lineCurrentNodes[i].start_day != null && lineCurrentNodes[i].end_day != null) {
                    const endPure =
                        lineCurrentNodes[i].end_day + lineCurrentNodes[i].end_tick / ticksInDay;
                    const startPure =
                        lineCurrentNodes[i].start_day + lineCurrentNodes[i].start_tick / ticksInDay;
                    return Math.ceil(endPure - startPure);
                }
                return lineCurrentNodes[i].end_day != null
                    ? lineCurrentNodes[i].end_day - lineCurrentNodes[i].start_day
                    : lineCurrentNodes[i].start_day != null
                      ? rootStore.ingameStore.SAI.config.day - lineCurrentNodes[i].start_day
                      : 0;
            };
            tempNodes.push({
                id: `n-current-${i}`,
                position: {
                    x: widthPerTask * (i + 0.5),
                    y: lineRowHeight - calcProgressDays() * hoursInADay * heightPerWeight,
                },
                style: {
                    height: '4px',
                    width: '4px',
                },
                data: lineCurrentNodes[i],
                type: 'lineNode',
            });
        }
        for (let i = 0; i < linePlanNodes.length; i++) {
            tempNodes.push({
                id: `n-plan-${i}`,
                position: {
                    x: widthPerTask * (i + 0.5),
                    y:
                        lineRowHeight -
                        linePlanNodes[i].est_duration * hoursInADay * heightPerWeight,
                },
                style: {
                    height: '4px',
                    width: '4px',
                },
                data: linePlanNodes[i],
                type: 'lineNode',
            });
        }
        for (let i = 0; i < candleNodes.length; i++) {
            tempNodes.push({
                id: `n-candle-${i}`,
                position: {
                    x: widthPerTask * i,
                    y: 0,
                },
                style: {
                    height: `${state.availableHeight}px`,
                    width: `${widthPerTask}px`,
                },
                data: candleNodes[i],
                type: 'candleNode',
            });
        }
        setNodes(tempNodes);
    }

    function tasksToEdges() {
        const links: Edge[] = [];
        if (state.tasks.length < 2) {
            setEdges([]);
            return;
        }
        for (let i = 0; i < state.tasks.length - 1; i++) {
            links.push({
                id: `e-plan-${i}-${i + 1}`,
                source: `n-plan-${i}`,
                target: `n-plan-${i + 1}`,
                type: 'straight',
                animated: false,
                className: 'line-edge plan-edge',
                style: EDGE_PLAN_STYLE,
                markerEnd: EDGE_MARKER,
            });
            links.push({
                id: `e-current-${i}-${i + 1}`,
                source: `n-current-${i}`,
                target: `n-current-${i + 1}`,
                type: 'straight',
                animated: false,
                className: 'line-edge current-edge',
                style: EDGE_CURRENT_STYLE,
                markerEnd: EDGE_MARKER,
            });
        }
        setEdges(links);
    }

    function repositionNodes() {
        const timeSettings = rootStore.ingameStore.getTimeSettings();
        const hoursInADay = timeSettings.workDayHours - +timeSettings.workDayLunchSkip;
        const lineRowHeight = Math.floor(
            (state.availableHeight - defaultRowGap) * defaultLineRowRatio,
        );
        const candleRowHeight = Math.floor(
            (state.availableHeight - defaultRowGap) * (1 - defaultLineRowRatio),
        );
        const widthPerTask = Math.floor(state.availableWidth / state.tasks.length);
        const heightPerWeight = lineRowHeight / state.maxWeight;
        const heightPerBudget = candleRowHeight / state.maxBudget;
        const ticksInDay =
            rootStore.ingameStore.getTimeSettings().workDayHours *
            rootStore.ingameStore.SAI?.config.ticks_in_hour;
        const tempNodes = [...nodes];
        setNodes(
            tempNodes.map((tn) => {
                const calcProgressDays = () => {
                    if (tn.data.start_day != null && tn.data.end_day != null) {
                        const endPure = tn.data.end_day + tn.data.end_tick / ticksInDay;
                        const startPure = tn.data.start_day + tn.data.start_tick / ticksInDay;
                        return Math.ceil(endPure - startPure);
                    }
                    return tn.data.end_day != null
                        ? tn.data.end_day - tn.data.start_day
                        : tn.data.start_day != null
                          ? rootStore.ingameStore.SAI.config.day - tn.data.start_day
                          : 0;
                };
                return {
                    ...tn,
                    data:
                        tn.data.type == 'candle'
                            ? {
                                  ...tn.data,
                                  rowHeight:
                                      tn.data.budget_current > tn.data.est_budget
                                          ? Math.floor(tn.data.budget_current * heightPerBudget)
                                          : Math.floor(tn.data.est_budget * heightPerBudget),
                              }
                            : tn.data,
                    position:
                        tn.data.type == 'candle'
                            ? { x: widthPerTask * tn.data.column, y: 0 }
                            : tn.data.type == 'line-current'
                              ? {
                                    x: widthPerTask * (tn.data.column + 0.5),
                                    y:
                                        lineRowHeight -
                                        calcProgressDays() * hoursInADay * heightPerWeight,
                                }
                              : {
                                    x: widthPerTask * (tn.data.column + 0.5),
                                    y:
                                        lineRowHeight -
                                        tn.data.est_duration * hoursInADay * heightPerWeight,
                                },
                    style:
                        tn.data.type == 'candle'
                            ? {
                                  height: `${state.availableHeight}px`,
                                  width: `${widthPerTask}px`,
                              }
                            : { height: '4px', width: '4px' },
                };
            }),
        );
    }

    useEffect(() => {
        if (nodes.length == 0) return;
        if (chartContRef?.current?.clientHeight == null) {
            state.availableHeight = defaultHeight;
        } else {
            state.availableHeight = Math.floor(chartContRef.current.clientHeight);
        }
        if (chartContRef?.current?.clientWidth == null) {
            state.availableWidth = defaultWidth;
        } else {
            state.availableWidth = Math.floor(chartContRef.current.clientWidth);
        }
        repositionNodes();
    }, [
        chartContRef?.current?.clientHeight,
        chartContRef?.current?.clientWidth,
    ]);

    return (
        <div
            className={`chart-container complex-task-chart${useCase == 'Mini' ? '-mini' : ''}`}
            ref={chartContRef}
        >
            <ReactFlow
                className="chart-flow complex-task-flow"
                nodes={nodes}
                edges={edges}
                nodeTypes={nodeTypes}
                proOptions={{ hideAttribution: true }}
                viewport={{
                    x: 0,
                    y: 0,
                    zoom: 1,
                }}
                // Запрет удаления
                deleteKeyCode={null}
                // Не фокусить связи
                edgesFocusable={false}
                // Отруб подсветки
                elevateEdgesOnSelect={false}
                // Отключение автозума
                fitView={false}
                // Ограничение зума
                maxZoom={1}
                minZoom={1}
                // Запрет мультивыбора
                multiSelectionKeyCode={null}
                // Не соединять узлы
                nodesConnectable={false}
                // Не перемещать узлы
                nodesDraggable={false}
                // Не фокусить узлы
                nodesFocusable={false}
                // Рендер только видимых элементов
                onlyRenderVisibleElements={true}
                // Перемещение скроллом
                panOnScroll={true}
                // Снятие запрета на скролл
                preventScrolling={false}
                // Запрет выбора
                selectionKeyCode={null}
                // Задание границ вью-порта
                translateExtent={[
                    [0, state.availableHeight],
                    [state.availableWidth, 0],
                ]}
                // Отключение зума двойным кликом
                zoomOnDoubleClick={false}
                // Отключение зума на тач-девайсе
                zoomOnPinch={false}
                // Отключение зума скроллом
                zoomOnScroll={false}
            >
                <Background
                    color={Colors.Accent.warm[0]}
                    variant={null}
                />
            </ReactFlow>
        </div>
    );
});

export { ComplexTaskChart };
