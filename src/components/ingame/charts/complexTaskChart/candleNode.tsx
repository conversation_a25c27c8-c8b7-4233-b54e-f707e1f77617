import { memo } from 'react';
import { ChartNodeInner } from './complexTaskChart';
import { Col, Row, Tooltip } from 'antd';
import Colors from 'src/shared/colors';
import { rootStore } from 'src/store/instanse';
import { useNavigate } from 'react-router-dom';

type CandleNodeProps = {
    data: ChartNodeInner;
};

export default memo(({ data }: CandleNodeProps) => {
    const navigate = useNavigate();
    const currentBiggerThanPlanned = data.budget_current > data.est_budget;
    const currentColor =
        data.start_day == null
            ? Colors.Neutral[500]
            : data.end_day == null
              ? Colors.Success.cold[200]
              : Colors.Accent.warm[500];
    const planColor =
        data.start_day == null
            ? Colors.Black[200]
            : data.end_day == null
              ? Colors.Success.cold[800]
              : Colors.Accent.warm[800];
    const currentHeight = currentBiggerThanPlanned
        ? data.rowHeight
        : Math.floor((data.budget_current / data.est_budget) * data.rowHeight);
    const planHeight = currentBiggerThanPlanned
        ? Math.floor((data.est_budget / data.budget_current) * data.rowHeight)
        : data.rowHeight;
    const calcProgressDays = () => {
        const ticksInDay =
            rootStore.ingameStore.getTimeSettings().workDayHours *
            rootStore.ingameStore.SAI?.config.ticks_in_hour;
        if (data.start_day != null && data.end_day != null) {
            const endPure = data.end_day + data.end_tick / ticksInDay;
            const startPure = data.start_day + data.start_tick / ticksInDay;
            return Math.ceil(endPure - startPure);
        }
        return data.end_day != null
            ? data.end_day - data.start_day
            : data.start_day != null
              ? rootStore.ingameStore.SAI.config.day - data.start_day
              : 0;
    };

    return (
        <Tooltip
            placement="left"
            title={
                <Col className="candle-node-tooltip">
                    <Row className="desc-l-strong">{`${data.column + 1}. ${data.name}`}</Row>
                    <Row className="desc-l">Длительность (план): {data.est_duration} д.</Row>
                    <Row className="desc-l">Длительность (факт): {calcProgressDays()} д.</Row>
                    <Row className="desc-l">
                        Бюджет (план): {Math.ceil(data.est_budget / 1000)} тыс.
                    </Row>
                    <Row className="desc-l">
                        Бюджет (факт): {Math.ceil(data.budget_current / 1000)} тыс.
                    </Row>
                </Col>
            }
        >
            <div
                className="desc-s candle-node-wrapper"
                onClick={(e) => {
                    e.stopPropagation();
                    if (data.useCase == 'Page') {
                        navigate(`/session/tasks/${data.task_uid}`);
                    } else {
                        navigate('/sessions/charts/complex-task-chart');
                    }
                }}
            >
                <Col
                    className="main-col"
                    style={{
                        backgroundImage: `linear-gradient(0deg, ${
                            currentBiggerThanPlanned ? planColor : currentColor
                        } 0px ${currentBiggerThanPlanned ? planHeight : currentHeight}px, ${
                            currentBiggerThanPlanned ? currentColor : planColor
                        } ${
                            currentBiggerThanPlanned ? planHeight : currentHeight
                        }px ${data.rowHeight}px, rgba(0, 0, 0, 0) ${data.rowHeight}px 100%)`,
                        backgroundPosition: 'center bottom',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: `100% 100%`,
                        color: Colors.Accent.warm[0],
                        height: `100%`,
                    }}
                >
                    &nbsp;
                </Col>
            </div>
        </Tooltip>
    );
});
