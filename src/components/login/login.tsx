import Logo from '@components/ui/logo/logo';
import { useReactive } from 'ahooks';
import { useNavigate } from 'react-router-dom';
import { Button, Col, Input, InputRef, Row, Tabs, Tooltip } from 'antd';
import useMessage from 'antd/es/message/useMessage';
import { useRef } from 'react';
import { SettingsManager } from 'src/shared/settingsManager';
import { CRMAPI } from '@api/crmApi';
import { rootStore } from 'src/store/instanse';

import './login.scss';

type TState = {
    isLoading: boolean;
    loginValue: string;
    passwordValue: string;
    emailValue: string;
};

const Login = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        loginValue: '',
        passwordValue: '',
        emailValue: '',
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = useMessage();
    const loginPasswordRef = useRef<InputRef>(null);
    const registerLoginRef = useRef<InputRef>(null);
    const registerPasswordRef = useRef<InputRef>(null);
    const recoveryLoginRef = useRef<InputRef>(null);
    // https://stackoverflow.com/questions/28555114/regexp-for-login
    const loginMask = /^(?=.*[A-Za-z0-9]$)[A-Za-z][A-Za-z\d]{2,15}$/;
    // https://stackoverflow.com/questions/46155/how-can-i-validate-an-email-address-in-javascript
    const emailMask = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    // https://stackoverflow.com/questions/12090077/javascript-regular-expression-password-validation-having-special-characters
    const passwordMask = /^(?=.*[0-9])[a-zA-Z0-9]{8,24}$/;

    function validate(password = false, email = false) {
        if (state.loginValue.trim() == '') {
            messageApi.warning('Пожалуйста, введите логин');
            return false;
        }
        if (password && state.passwordValue.trim() == '') {
            messageApi.warning('Пожалуйста, введите пароль');
            return false;
        }
        if (email && state.emailValue.trim() == '') {
            messageApi.warning('Пожалуйста, введите email');
            return false;
        }
        return true;
    }

    async function wrapper(func: () => Promise<void>) {
        messageApi.loading('Отправляем запрос...', 0);
        state.isLoading = true;
        try {
            await func();
        } catch (err) {
            state.isLoading = false;
            messageApi.destroy();
            messageApi.error(typeof err == 'object' ? err[0] : 'Произошла ошибка');
            console.log(err);
        }
    }

    async function loginSubmit() {
        if (!validate(true) || state.isLoading) {
            return;
        }
        wrapper(async () => {
            const api = new CRMAPI();
            const result = await api.login(state.loginValue, state.passwordValue);
            if (result.errorMessages) throw result.errorMessages;
            SettingsManager.updateConnectionCredentials({
                accessToken: result.data.data.access_token,
                refreshToken: result.data.data.refresh_token,
                user_id: result.data.data.user.id,
            });
            rootStore.currentUserStore.setUser(result.data.data.user);
            state.isLoading = false;
            messageApi.destroy();
            navigate('/lk');
        });
    }

    async function registerSubmit() {
        if (!validate(true, true) || state.isLoading) {
            return;
        }
        wrapper(async () => {
            const api = new CRMAPI();
            const result = await api.register(
                state.emailValue,
                state.loginValue,
                state.passwordValue,
            );
            if (result.errorMessages) throw result.errorMessages;
            state.isLoading = false;
            messageApi.destroy();
            messageApi.success('На ваш email отправлено письмо');
        });
    }

    async function recoverySubmit() {
        if (!validate(false, true) || state.isLoading) {
            return;
        }
        wrapper(async () => {
            const api = new CRMAPI();
            const result = await api.recovery(state.emailValue, state.loginValue);
            if (result.errorMessages) throw result.errorMessages;
            state.isLoading = false;
            messageApi.destroy();
            messageApi.success('На ваш email отправлено письмо');
        });
    }

    const tabs = [
        {
            label: 'Вход',
            key: 'login',
            children: (
                <Col className="login-tab-content p2">
                    <Row>
                        <Tooltip title="От 3 до 16 символов, латиница и цифры, первый - буква">
                            <Input
                                autoComplete="login username"
                                disabled={state.isLoading}
                                id="username"
                                maxLength={16}
                                name="username"
                                onChange={(e) => (state.loginValue = e.target.value)}
                                onPressEnter={() => {
                                    loginPasswordRef.current!.focus({ cursor: 'start' });
                                }}
                                placeholder="Введите логин"
                                size="large"
                                status={
                                    state.loginValue.length == 0 || loginMask.test(state.loginValue)
                                        ? null
                                        : 'error'
                                }
                                value={state.loginValue}
                            />
                        </Tooltip>
                    </Row>
                    <Row>
                        <Tooltip title="От 8 до 24 символов, латиница, минимум 1 цифра">
                            <Input.Password
                                autoComplete="current-password"
                                disabled={state.isLoading}
                                id="current-password"
                                maxLength={24}
                                name="current-password"
                                onChange={(e) => (state.passwordValue = e.target.value)}
                                onPressEnter={() => loginSubmit()}
                                placeholder="Введите пароль"
                                ref={loginPasswordRef}
                                size="large"
                                status={
                                    state.passwordValue.length == 0 ||
                                    passwordMask.test(state.passwordValue)
                                        ? null
                                        : 'error'
                                }
                                value={state.passwordValue}
                            />
                        </Tooltip>
                    </Row>
                    <Row>
                        <Button
                            className="login-tab-submit p3"
                            disabled={
                                !(
                                    loginMask.test(state.loginValue) &&
                                    passwordMask.test(state.passwordValue)
                                ) || state.isLoading
                            }
                            onClick={loginSubmit}
                            loading={state.isLoading}
                        >
                            Войти
                        </Button>
                    </Row>
                </Col>
            ),
        },
        {
            label: 'Регистрация',
            key: 'register',
            children: (
                <Col className="login-tab-content p2">
                    <Row>
                        <Tooltip title="От 5 до 40 символов, email-формат">
                            <Input
                                autoComplete="email"
                                disabled={state.isLoading}
                                id="email"
                                maxLength={40}
                                name="email"
                                onChange={(e) => (state.emailValue = e.target.value)}
                                onPressEnter={() => {
                                    registerLoginRef.current!.focus({ cursor: 'start' });
                                }}
                                placeholder="Введите email"
                                size="large"
                                status={
                                    state.emailValue.length == 0 ||
                                    (emailMask.test(state.emailValue) &&
                                        state.emailValue.length >= 5 &&
                                        state.emailValue.length <= 40)
                                        ? null
                                        : 'error'
                                }
                                value={state.emailValue}
                            />
                        </Tooltip>
                    </Row>
                    <Row>
                        <Tooltip title="От 3 до 16 символов, латиница и цифры, первый - буква">
                            <Input
                                autoComplete="username"
                                disabled={state.isLoading}
                                id="username"
                                maxLength={16}
                                name="username"
                                onChange={(e) => (state.loginValue = e.target.value)}
                                onPressEnter={() => {
                                    registerPasswordRef.current!.focus({ cursor: 'start' });
                                }}
                                placeholder="Введите логин"
                                ref={registerLoginRef}
                                size="large"
                                status={
                                    state.loginValue.length == 0 || loginMask.test(state.loginValue)
                                        ? null
                                        : 'error'
                                }
                                value={state.loginValue}
                            />
                        </Tooltip>
                    </Row>
                    <Row>
                        <Tooltip title="От 8 до 24 символов, латиница, минимум 1 цифра">
                            <Input.Password
                                autoComplete="new-password"
                                disabled={state.isLoading}
                                id="new-password"
                                maxLength={24}
                                name="new-password"
                                onChange={(e) => (state.passwordValue = e.target.value)}
                                onPressEnter={() => registerSubmit()}
                                placeholder="Введите пароль"
                                ref={registerPasswordRef}
                                size="large"
                                status={
                                    state.passwordValue.length == 0 ||
                                    passwordMask.test(state.passwordValue)
                                        ? null
                                        : 'error'
                                }
                                value={state.passwordValue}
                            />
                        </Tooltip>
                    </Row>
                    <Row>
                        <Button
                            className="login-tab-submit p3"
                            disabled={
                                !(
                                    emailMask.test(state.emailValue) &&
                                    state.emailValue.length >= 5 &&
                                    state.emailValue.length <= 40 &&
                                    loginMask.test(state.loginValue) &&
                                    passwordMask.test(state.passwordValue)
                                ) || state.isLoading
                            }
                            onClick={registerSubmit}
                            loading={state.isLoading}
                        >
                            Зарегистрироваться
                        </Button>
                    </Row>
                </Col>
            ),
        },
        {
            label: 'Восстановление доступа',
            key: 'recovery',
            children: (
                <Col className="login-tab-content p2">
                    <Row>
                        <Tooltip title="От 5 до 40 символов, email-формат">
                            <Input
                                autoComplete="email"
                                disabled={state.isLoading}
                                id="email"
                                maxLength={40}
                                name="email"
                                onChange={(e) => (state.emailValue = e.target.value)}
                                onPressEnter={() => {
                                    recoveryLoginRef.current!.focus({ cursor: 'start' });
                                }}
                                placeholder="Введите email"
                                size="large"
                                status={
                                    state.emailValue.length == 0 ||
                                    (emailMask.test(state.emailValue) &&
                                        state.emailValue.length >= 5 &&
                                        state.emailValue.length <= 40)
                                        ? null
                                        : 'error'
                                }
                                value={state.emailValue}
                            />
                        </Tooltip>
                    </Row>
                    <Row>
                        <Tooltip title="От 3 до 16 символов, латиница и цифры, первый - буква">
                            <Input
                                autoComplete="username"
                                disabled={state.isLoading}
                                id="username"
                                maxLength={16}
                                name="username"
                                onChange={(e) => (state.loginValue = e.target.value)}
                                onPressEnter={() => recoverySubmit()}
                                placeholder="Введите логин"
                                ref={recoveryLoginRef}
                                size="large"
                                status={
                                    state.loginValue.length == 0 || loginMask.test(state.loginValue)
                                        ? null
                                        : 'error'
                                }
                                value={state.loginValue}
                            />
                        </Tooltip>
                    </Row>
                    <Row>
                        <Button
                            className="login-tab-submit p3"
                            disabled={
                                !(
                                    emailMask.test(state.emailValue) &&
                                    state.emailValue.length >= 5 &&
                                    state.emailValue.length <= 40 &&
                                    loginMask.test(state.loginValue)
                                ) || state.isLoading
                            }
                            onClick={recoverySubmit}
                            loading={state.isLoading}
                        >
                            Восстановить доступ
                        </Button>
                    </Row>
                </Col>
            ),
        },
    ];

    return (
        <div className="login-container">
            {contextHolder}
            <Row className="login-main-row">
                <Col className="logo-column">
                    <Logo />
                </Col>
                <Col>
                    <Tabs
                        className="p3"
                        defaultActiveKey="login"
                        items={tabs}
                        type="card"
                    />
                </Col>
            </Row>
        </div>
    );
};

export default Login;
