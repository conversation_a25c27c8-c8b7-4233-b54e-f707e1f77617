import { CRMAPIManager } from '@api/crmApiManager';
import { SessionAssignmentResp } from '@api/responseModels/sessionAssignments/sessionAssignmentResponse';
import { SessionResp } from '@api/responseModels/sessions/sessionResponse';
import { Common } from 'src/shared/common';
import { Permissions } from 'src/shared/permissions';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { Loader } from '@components/ui/loader/loader';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { rootStore } from 'src/store/instanse';
import { useReactive } from 'ahooks';
import { Button, Col, message, Popconfirm, Row } from 'antd';
import { observer } from 'mobx-react';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSession } from 'types/session/session';
import { TSessionAssignment } from 'types/session/sessionAssignment';

import './sessionAssignmentProfile.scss';

type TState = {
    isLoading: boolean;
    session: TSession | null;
    sessionAssignment: TSessionAssignment | null;
};

const SessionAssignmentProfile = observer((): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        session: null,
        sessionAssignment: null,
    });
    const { sessionAssignmentId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const allowSessionGet = Permissions.checkPermission(Permissions.SessionGet);

    const allowDelete =
        Permissions.checkPermission(Permissions.SessionAssignmentDelete) &&
        state.sessionAssignment?.state == 'prestarted';
    const allowStart =
        Permissions.checkPermission(Permissions.SessionAssignmentStart) &&
        state.sessionAssignment?.state == 'prestarted';
    const allowPause =
        Permissions.checkPermission(Permissions.SessionAssignmentPause) &&
        (state.sessionAssignment?.state == 'started' ||
            state.sessionAssignment?.state == 'resumed');
    const allowResume =
        Permissions.checkPermission(Permissions.SessionAssignmentResume) &&
        (state.sessionAssignment?.state == 'paused' || state.sessionAssignment?.state == 'stopped');
    const allowStop =
        Permissions.checkPermission(Permissions.SessionAssignmentStop) &&
        (state.sessionAssignment?.state == 'started' ||
            state.sessionAssignment?.state == 'resumed');
    //|| state.sessionAssignment?.state == "paused"
    const allowFinish =
        Permissions.checkPermission(Permissions.SessionAssignmentFinish) &&
        (state.sessionAssignment?.state == 'started' ||
            state.sessionAssignment?.state == 'resumed');

    const allowEnter =
        Permissions.checkPermission(Permissions.SessionAssignmentInfo) &&
        Permissions.checkPermission(Permissions.SessionAssignmentInfoCWT) &&
        Permissions.checkPermission(Permissions.SessionAssignmentInfoCTW) &&
        Permissions.checkPermission(Permissions.SessionAssignmentInfoWorkers) &&
        Permissions.checkPermission(Permissions.SessionAssignmentInfoTasks);

    async function loadNecessary() {
        if (sessionAssignmentId == undefined) {
            navigate('/controls/assignments');
            message.error('Назначение не найдено');
            return;
        }
        if (!Permissions.checkPermission(Permissions.SessionAssignmentGet)) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        state.isLoading = true;
        try {
            const fetchSA = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.getSessionAssignment(sessionAssignmentId);
            });
            if (fetchSA.errorMessages) throw fetchSA.errorMessages;
            state.sessionAssignment = fetchSA.data.data;

            if (allowSessionGet) {
                const fetchSession = await CRMAPIManager.request<SessionResp>(async (api) => {
                    return await api.getSession(fetchSA.data.data.session_id);
                });
                if (fetchSession.errorMessages) throw fetchSession.errorMessages;
                state.session = fetchSession.data.data;
            }
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/controls/assignments');
            }
            messageApi.error('Ошибка при загрузке сущностей');
            console.log(errors);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        loadNecessary();
    }, [sessionAssignmentId]);

    function stateToLocale() {
        if (state.sessionAssignment?.state == null) {
            return '-';
        }
        switch (state.sessionAssignment?.state) {
            case 'prestarted':
                return 'Новое';
            case 'started':
                return 'Начато';
            case 'paused':
                return 'На паузе';
            case 'resumed':
                return 'Возобновлено';
            case 'stopped':
                return 'Остановлено';
            case 'finished':
                return 'Завершено';
            default:
                return state.sessionAssignment?.state;
        }
    }

    async function deleteSessionAssignment() {
        if (!allowDelete) {
            messageApi.error('Недостаточно прав для удаления или прохождение было начато');
            return;
        }
        state.isLoading = true;
        try {
            const assignment = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.deleteSessionAssignment(sessionAssignmentId);
            });
            if (assignment.errorMessages) throw assignment.errorMessages;
            navigate('/controls/assignments');
            message.success('Назначение было удалено');
        } catch (errors) {
            messageApi.error('Ошибка при удалении назначения');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function SAenter() {
        if (!allowEnter) {
            messageApi.error('Недостаточно прав для перехода в прохождение');
            return;
        }
        state.isLoading = true;
        await rootStore.ingameStore.initSA(sessionAssignmentId);
        if (rootStore.ingameStore.httpState == 'error') {
            state.isLoading = false;
            messageApi.error('Ошибка при инициализации, попробуйте ещё раз');
            return;
        } else {
            navigate('/session');
        }
        state.isLoading = false;
    }

    async function SAstart() {
        if (!allowStart) {
            messageApi.error('Недостаточно прав или прохождение уже запущено');
            return;
        }
        state.isLoading = true;
        try {
            const assignment = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SAstart(sessionAssignmentId);
            });
            if (assignment.errorMessages) throw assignment.errorMessages;
            state.sessionAssignment = assignment.data.data;
            messageApi.success('Прохождение запущено');
        } catch (errors) {
            messageApi.error('Ошибка при запуске прохождения');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function SApause() {
        if (!allowPause) {
            messageApi.error('Недостаточно прав или прохождение уже на паузе');
            return;
        }
        state.isLoading = true;
        try {
            const assignment = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SApause(sessionAssignmentId);
            });
            if (assignment.errorMessages) throw assignment.errorMessages;
            state.sessionAssignment = assignment.data.data;
            messageApi.success('Прохождение на паузе');
        } catch (errors) {
            messageApi.error('Ошибка при постановке прохождения на паузу');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function SAresume() {
        if (!allowResume) {
            messageApi.error('Недостаточно прав или прохождение не на паузе');
            return;
        }
        state.isLoading = true;
        try {
            const assignment = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SAresume(sessionAssignmentId);
            });
            if (assignment.errorMessages) throw assignment.errorMessages;
            state.sessionAssignment = assignment.data.data;
            messageApi.success('Прохождение снято с паузы');
        } catch (errors) {
            messageApi.error('Ошибка при снятии прохождения с паузы');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function SAstop() {
        if (!allowStop) {
            messageApi.error('Недостаточно прав или прохождение уже остановлено');
            return;
        }
        state.isLoading = true;
        try {
            const assignment = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SAstop(sessionAssignmentId);
            });
            if (assignment.errorMessages) throw assignment.errorMessages;
            state.sessionAssignment = assignment.data.data;
            messageApi.success('Прохождение остановлено');
        } catch (errors) {
            messageApi.error('Ошибка остановке прохождения');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function SAfinish() {
        if (!allowFinish) {
            messageApi.error('Недостаточно прав или прохождение не может быть завершено');
            return;
        }
        state.isLoading = true;
        try {
            const assignment = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SAfinish(sessionAssignmentId);
            });
            if (assignment.errorMessages) throw assignment.errorMessages;
            state.sessionAssignment = assignment.data.data;
            messageApi.success('Прохождение завершено');
        } catch (errors) {
            messageApi.error('Ошибка при завершении прохождения');
            console.log(errors);
        }
        state.isLoading = false;
    }

    return (
        <UniLayout
            activeTab="assignments"
            additionalClass="profile-min-width"
            tabSet="controls"
        >
            <div className="session-assignment-profile">
                {contextHolder}
                {state.isLoading && <Loader />}
                <div className="session-assignment-card">
                    {allowSessionGet && (
                        <Row className="session-info-row">
                            <Col>
                                <Row className="header-row">
                                    <Col>
                                        <Row>
                                            <h4>ID сессии: {state.session?.id}</h4>
                                            <CopyButton
                                                textToCopy={`ID сессии ${state.session?.id}`}
                                                textToShow="ID сессии скопирован"
                                                size={28}
                                            />
                                        </Row>
                                    </Col>
                                </Row>
                                <Row className="body-row">
                                    <Col>
                                        <Row>
                                            <Col className="labeled-input">
                                                <Row>
                                                    <span className="p3">Создатель:</span>
                                                </Row>
                                                <Row
                                                    className="creator-row"
                                                    onClick={() => {
                                                        navigate(
                                                            `/management/users/${state.session?.manager_id}`,
                                                        );
                                                    }}
                                                >
                                                    <Col className="creator-avatar">
                                                        <div className="p1-strong">
                                                            {state.session?.manager_name == null ||
                                                            state.session?.manager_name.length == 0
                                                                ? '-'
                                                                : state.session?.manager_name[0]}
                                                        </div>
                                                    </Col>
                                                    <Col className="p3 creator-name">
                                                        <Button
                                                            className="creator-btn"
                                                            type="link"
                                                        >
                                                            {state.session?.manager_name}
                                                        </Button>
                                                    </Col>
                                                </Row>
                                            </Col>
                                        </Row>
                                    </Col>
                                    <Col>
                                        <Row>
                                            <Col className="labeled-input">
                                                <Row>
                                                    <span className="p3">Была создана:</span>
                                                </Row>
                                                <Row>
                                                    <span className="p3 lighter-tone">
                                                        {Common.formatDateString(
                                                            state.session?.created_at,
                                                        )}
                                                    </span>
                                                </Row>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col className="labeled-input">
                                                <Row>
                                                    <span className="p3">Последнее изменение:</span>
                                                </Row>
                                                <Row>
                                                    <span className="p3 lighter-tone">
                                                        {Common.formatDateString(
                                                            state.session?.updated_at,
                                                        )}
                                                    </span>
                                                </Row>
                                            </Col>
                                        </Row>
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    )}
                    <Row className="simulation-info-row">
                        <Col>
                            {allowSessionGet && (
                                <Row className="header-row">
                                    <Col style={{ minWidth: '100px' }}>
                                        <Row>
                                            <h4>ID симуляции: {state.session?.simulation_id}</h4>
                                            <CopyButton
                                                textToCopy={`ID симуляции ${state.session?.simulation_id}`}
                                                textToShow="ID симуляции скопирован"
                                                size={28}
                                            />
                                        </Row>
                                    </Col>
                                </Row>
                            )}
                            <Row className="body-row">
                                <Col>
                                    <Row>
                                        <div className="p1">
                                            {state.sessionAssignment?.simulation_name}
                                        </div>
                                        <CopyButton
                                            textToCopy={state.sessionAssignment?.simulation_name}
                                            textToShow="Название скопировано"
                                            size={26}
                                        />
                                    </Row>
                                    <Row>
                                        <div className="description desc-l">
                                            {state.sessionAssignment?.simulation_description}
                                        </div>
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row className="assignment-info-row">
                        <Col>
                            <Row className="header-row">
                                <Col>
                                    <Row>
                                        <h4>ID назначения: {state.sessionAssignment?.id}</h4>
                                        <CopyButton
                                            textToCopy={`ID назначения ${state.sessionAssignment?.id}`}
                                            textToShow="ID назначения скопирован"
                                            size={28}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            <Row className="body-row">
                                <Col>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Менеджер:</span>
                                            </Row>
                                            <Row
                                                className="creator-row"
                                                onClick={() => {
                                                    navigate(
                                                        `/management/users/${state.sessionAssignment?.manager_id}`,
                                                    );
                                                }}
                                            >
                                                <Col className="creator-avatar">
                                                    <div className="p1-strong">
                                                        {state.sessionAssignment?.manager_name ==
                                                            null ||
                                                        state.sessionAssignment?.manager_name
                                                            .length == 0
                                                            ? '-'
                                                            : state.sessionAssignment
                                                                  ?.manager_name[0]}
                                                    </div>
                                                </Col>
                                                <Col className="p3 creator-name">
                                                    <Button
                                                        className="creator-btn"
                                                        type="link"
                                                    >
                                                        {state.sessionAssignment?.manager_name}
                                                    </Button>
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Пользователь:</span>
                                            </Row>
                                            <Row
                                                className="creator-row"
                                                onClick={() => {
                                                    navigate(
                                                        `/management/users/${state.sessionAssignment?.user_id}`,
                                                    );
                                                }}
                                            >
                                                <Col className="creator-avatar">
                                                    <div className="p1-strong">
                                                        {state.sessionAssignment?.user_name ==
                                                            null ||
                                                        state.sessionAssignment?.user_name.length ==
                                                            0
                                                            ? '-'
                                                            : state.sessionAssignment?.user_name[0]}
                                                    </div>
                                                </Col>
                                                <Col className="p3 creator-name">
                                                    <Button
                                                        className="creator-btn"
                                                        type="link"
                                                    >
                                                        {state.sessionAssignment?.user_name}
                                                    </Button>
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                </Col>
                                <Col>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Было создано:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {Common.formatDateString(
                                                        state.sessionAssignment?.created_at,
                                                    )}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Последнее изменение:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {Common.formatDateString(
                                                        state.sessionAssignment?.updated_at,
                                                    )}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Состояние:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {stateToLocale()}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Время:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {rootStore.ingameStore.dayTickToString(
                                                        state.sessionAssignment,
                                                    )}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row className="p2 controls-row">
                        <Col>
                            <Row>
                                {allowEnter && (
                                    <Button
                                        onClick={() => {
                                            SAenter();
                                        }}
                                    >
                                        В прохождение
                                    </Button>
                                )}
                                {allowStart && (
                                    <Button
                                        onClick={() => {
                                            SAstart();
                                        }}
                                    >
                                        Старт
                                    </Button>
                                )}
                                {allowPause && (
                                    <Button
                                        onClick={() => {
                                            SApause();
                                        }}
                                    >
                                        Пауза
                                    </Button>
                                )}
                                {allowResume && (
                                    <Button
                                        onClick={() => {
                                            SAresume();
                                        }}
                                    >
                                        Продолжить
                                    </Button>
                                )}
                                {allowFinish && (
                                    <Button
                                        onClick={() => {
                                            SAfinish();
                                        }}
                                    >
                                        Завершить
                                    </Button>
                                )}
                                {allowStop && (
                                    <Button
                                        onClick={() => {
                                            SAstop();
                                        }}
                                    >
                                        Остановить
                                    </Button>
                                )}
                            </Row>
                        </Col>
                        <Col>
                            <Row>
                                {allowSessionGet && (
                                    <Button
                                        onClick={() => {
                                            navigate(
                                                `/simulations/${state.session?.simulation_id}`,
                                            );
                                        }}
                                    >
                                        К симуляции
                                    </Button>
                                )}
                                {allowSessionGet && (
                                    <Button
                                        onClick={() => {
                                            navigate(`/controls/sessions/${state.session?.id}`);
                                        }}
                                    >
                                        К сессии
                                    </Button>
                                )}
                                {allowDelete && (
                                    <Popconfirm
                                        cancelText="Отмена"
                                        okText="Подтвердить"
                                        onConfirm={deleteSessionAssignment}
                                        title="Назначение не может быть восстановлено"
                                    >
                                        <Button>Удалить</Button>
                                    </Popconfirm>
                                )}
                            </Row>
                        </Col>
                    </Row>
                </div>
            </div>
        </UniLayout>
    );
});

export default SessionAssignmentProfile;
