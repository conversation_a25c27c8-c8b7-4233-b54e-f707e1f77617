import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import { Button, Col, ConfigProvider, List, message, Radio, Row, Tag } from 'antd';
import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import { Permissions } from 'src/shared/permissions';
import { CRMAPIManager } from '@api/crmApiManager';
import { SessionAssignmentListResp } from '@api/responseModels/sessionAssignments/sessionAssignmentListResponse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { rootStore } from 'src/store/instanse';
import { GlobalConstants } from 'src/shared/constants';
import { SettingsManager } from 'src/shared/settingsManager';

import './sessionAssignmentList.scss';

type TState = {
    isLoading: boolean;
    status: 'prestarted' | 'started' | 'paused' | 'resumed' | 'stopped' | 'finished' | 'all';
    assignments: TSessionAssignment[];
};

const SessionAssignmentList = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        status: 'all',
        assignments: [],
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const [searchParams] = useSearchParams();

    async function loadAssignments() {
        if (!Permissions.checkPermission(Permissions.SessionAssignmentList)) {
            navigate('/lk');
            message.error('Недостаточно прав для просмотра списка назначений');
            return;
        }
        state.isLoading = true;
        try {
            const session_id = searchParams.get('session_id');
            const user_id = searchParams.get('user_id');
            const result = await CRMAPIManager.request<SessionAssignmentListResp>(async (api) => {
                return await api.getSessionAssignmentList({
                    session_id: session_id,
                    user_id:
                        rootStore.currentUserStore.getUser?.role == 'Roles.Client'
                            ? SettingsManager.getConnectionCredentials()?.user_id
                            : user_id,
                    state: state.status == 'all' ? null : state.status,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        created_at: null,
                    },
                });
            });
            if (result.errorMessages) throw result.errorMessages;
            state.assignments = result.data.data;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка назначений');
            console.log(err);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        loadAssignments();
    }, [state.status]);

    function makeListItems() {
        const arr = [];
        arr.push(...state.assignments);
        return arr;
    }

    function stateToLocale(SA: TSessionAssignment) {
        switch (SA.state) {
            case 'prestarted':
                return 'Новое';
            case 'started':
                return 'Начато';
            case 'paused':
                return 'На паузе';
            case 'resumed':
                return 'Возобновлено';
            case 'stopped':
                return 'Остановлено';
            case 'finished':
                return 'Завершено';
            default:
                return SA.state;
        }
    }

    return (
        <UniLayout
            activeTab="assignments"
            additionalClass="list-min-width"
            tabSet="controls"
        >
            <div className="assignment-list-container">
                {contextHolder}
                <div
                    className={`radio-group${
                        searchParams.get('session_id') != null ||
                        searchParams.get('user_id') != null
                            ? ' and-tag'
                            : ''
                    }`}
                >
                    <Radio.Group
                        onChange={(e) => {
                            e.stopPropagation();
                            state.status = e.target.value;
                        }}
                        options={[
                            { label: 'Новые', value: 'prestarted' },
                            { label: 'Начатые', value: 'started' },
                            { label: 'На паузе', value: 'paused' },
                            { label: 'Возобновлённые', value: 'resumed' },
                            { label: 'Остановленные', value: 'stopped' },
                            { label: 'Завершённые', value: 'finished' },
                            { label: 'Все', value: 'all' },
                        ]}
                        optionType="button"
                        value={state.status}
                    />
                </div>
                <div className="assignment-list">
                    {(searchParams.get('session_id') != null ||
                        searchParams.get('user_id') != null) && (
                        <div className="p3 tag-section">
                            {searchParams.get('session_id') != null && (
                                <Tag
                                    closeIcon
                                    onClose={() => {
                                        searchParams.delete('session_id');
                                        navigate('/controls/assignments');
                                        loadAssignments();
                                    }}
                                >
                                    ID сессии {searchParams.get('session_id')}
                                </Tag>
                            )}
                            {searchParams.get('user_id') != null && (
                                <Tag
                                    closeIcon
                                    onClose={() => {
                                        searchParams.delete('user_id');
                                        navigate('/controls/assignments');
                                        loadAssignments();
                                    }}
                                >
                                    ID пользователя {searchParams.get('user_id')}
                                </Tag>
                            )}
                        </div>
                    )}
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={makeListItems()}
                            grid={GlobalConstants.ListGridCols}
                            loading={state.isLoading}
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких назначений нет :)</Row>
                                    </Col>
                                ),
                            }}
                            renderItem={(item: TSessionAssignment) => (
                                <div
                                    className={`assignment-list-card${
                                        Permissions.checkPermission(Permissions.SessionGet)
                                            ? ''
                                            : ' shorter'
                                    }`}
                                >
                                    <Col flex={1}>
                                        <Row className="header-row">
                                            <div className="p2-strong">ID {item.id}</div>
                                            <CopyButton
                                                textToCopy={`ID ${item.id}`}
                                                textToShow="ID скопирован"
                                                size={24}
                                            />
                                        </Row>
                                        <Row className="body-row">
                                            <Col flex={1}>
                                                {Permissions.checkPermission(
                                                    Permissions.SessionGet,
                                                ) && (
                                                    <Row className="p3">
                                                        <Col>
                                                            <span>Сессия (ID):</span>
                                                        </Col>
                                                        <Col>
                                                            <Row>
                                                                <Button
                                                                    disabled={
                                                                        !Permissions.checkPermission(
                                                                            Permissions.SessionGet,
                                                                        )
                                                                    }
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        navigate(
                                                                            `/controls/sessions/${item.session_id}`,
                                                                        );
                                                                    }}
                                                                    type="link"
                                                                >
                                                                    {item.session_id}
                                                                </Button>
                                                            </Row>
                                                        </Col>
                                                    </Row>
                                                )}
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Симуляция:</span>
                                                    </Col>
                                                    <Col>
                                                        <span>{item.simulation_name}</span>
                                                    </Col>
                                                </Row>
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Менеджер:</span>
                                                    </Col>
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/management/users/${item.manager_id}`,
                                                                    );
                                                                }}
                                                                type="link"
                                                            >
                                                                {item.manager_name}
                                                            </Button>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Пользователь:</span>
                                                    </Col>
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/management/users/${item.user_id}`,
                                                                    );
                                                                }}
                                                                type="link"
                                                            >
                                                                {item.user_name}
                                                            </Button>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                                <Row className="p3">
                                                    <span>Состояние:</span>
                                                    <span>{stateToLocale(item)}</span>
                                                </Row>
                                                <Row className="p3">
                                                    <span>Время:</span>
                                                    <span>
                                                        {rootStore.ingameStore.dayTickToString(
                                                            item,
                                                        )}
                                                    </span>
                                                </Row>
                                                <Row className="event-card-controls p3">
                                                    <Button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            navigate(
                                                                `/controls/assignments/${item.id}`,
                                                            );
                                                        }}
                                                    >
                                                        Открыть
                                                    </Button>
                                                </Row>
                                            </Col>
                                        </Row>
                                    </Col>
                                </div>
                            )}
                        />
                    </ConfigProvider>
                </div>
            </div>
        </UniLayout>
    );
};

export default SessionAssignmentList;
