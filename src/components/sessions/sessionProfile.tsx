import { TUser } from 'types/user/user';
import { TSession } from 'types/session/session';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import { TSimulation } from 'types/simulation/simulation';
import { observer } from 'mobx-react';
import { useReactive } from 'ahooks';
import { useNavigate, useParams } from 'react-router-dom';
import {
    Button,
    Checkbox,
    Col,
    InputNumber,
    message,
    Popconfirm,
    Row,
    Space,
    Table,
    TableColumnsType,
    Tooltip,
} from 'antd';
import React, { useCallback, useEffect } from 'react';
import { Permissions } from 'src/shared/permissions';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { CRMAPIManager } from '@api/crmApiManager';
import { SessionResp } from '@api/responseModels/sessions/sessionResponse';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { SettingsManager } from 'src/shared/settingsManager';
import { rootStore } from 'src/store/instanse';
import { UserResp } from '@api/responseModels/users/userResp';
import { SessionAssignmentListResp } from '@api/responseModels/sessionAssignments/sessionAssignmentListResponse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { Common } from 'src/shared/common';
import { TableRowSelection } from 'antd/es/table/interface';
import { UserListResp } from '@api/responseModels/users/userListResp';
import UserDrawer, { UserDrawerUseCase } from '@components/drawers/userDrawer';
import { Loader } from '@components/ui/loader/loader';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFolderOpen, faPause, faPlay, faStop } from '@fortawesome/free-solid-svg-icons';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { GlobalConstants } from 'src/shared/constants';
import { SessionAssignmentResp } from '@api/responseModels/sessionAssignments/sessionAssignmentResponse';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';

import './sessionProfile.scss';

type SADataType = TSessionAssignment & {
    key: TSessionAssignment['id'];
    deleted: boolean;
    status: { color: string; text: string }[];
    user_name: TUser['name'];
    user_picture: TUser['picture'];
};

type TSAChange = TSessionAssignment & {
    change_type: 'add' | 'update' | 'delete';
};

type TState = {
    addAmount: number;
    changes: { [key: TSessionAssignment['id']]: TSAChange };
    creator: TUser | null;
    isLoading: boolean;
    // Для создавемых id берётся из убывающего int,
    // чтобы избежать пересечений
    lastSAId: number;
    onlyShowDeleted: boolean;
    selectedSARows: React.Key[];
    selectedUsers: TUser['id'][];
    session: TSession | null;
    sessionAssignmentList: TSessionAssignment[];
    simulation: TSimulation | null;
    skipPreventLeaving: boolean;
    userList: TUser[];
    userPickerMode: 'single' | 'multiple';
    userPickerOpen: boolean;
};

const SessionProfile = observer((): JSX.Element => {
    const state = useReactive<TState>({
        addAmount: 1,
        changes: {},
        creator: null,
        isLoading: false,
        lastSAId: -1,
        onlyShowDeleted: false,
        selectedSARows: [],
        selectedUsers: [],
        session: null,
        sessionAssignmentList: [],
        simulation: null,
        skipPreventLeaving: false,
        userList: [],
        userPickerMode: 'single',
        userPickerOpen: false,
    });
    const { sessionId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const editable =
        state.creator?.deleted_at == null &&
        state.simulation?.deleted_at == null &&
        state.simulation?.archived == false;
    const anyChanges =
        Object.keys(state.changes).filter(
            (sckey) =>
                !(
                    state.changes[sckey].state == 'new' &&
                    state.changes[sckey].change_type == 'delete'
                ),
        ).length > 0;
    const tableStatuses = {
        deleted: GlobalConstants.TableStatuses['deleted'],
        finished: GlobalConstants.TableStatuses['finished'],
        new: GlobalConstants.TableStatuses['new'],
        'no-user': GlobalConstants.TableStatuses['no-user'],
        paused: GlobalConstants.TableStatuses['paused'],
        prestarted: GlobalConstants.TableStatuses['prestarted'],
        resumed: GlobalConstants.TableStatuses['resumed'],
        started: GlobalConstants.TableStatuses['started'],
        stopped: GlobalConstants.TableStatuses['stopped'],
        updated: GlobalConstants.TableStatuses['updated'],
    };
    const columns: TableColumnsType<SADataType> = [
        {
            title: 'ID назначения',
            dataIndex: 'id',
            render: (value) => (
                <span className="table-id p3">
                    {value}
                    {!value.includes('new-') && (
                        <Row>
                            <CopyButton
                                size={20}
                                textToCopy={value}
                                textToShow={'ID назначения скопирован'}
                            />
                            <Tooltip
                                title={anyChanges ? 'Сохраните изменения для перехода' : 'Перейти'}
                            >
                                <Button
                                    className="open-btn"
                                    disabled={anyChanges || state.onlyShowDeleted}
                                    icon={<FontAwesomeIcon icon={faFolderOpen} />}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        if (!(anyChanges || state.onlyShowDeleted)) {
                                            navigate(`/controls/assignments/${value}`);
                                        }
                                    }}
                                    type="text"
                                />
                            </Tooltip>
                        </Row>
                    )}
                </span>
            ),
            sorter: (a, b) => {
                if (a.id.includes('new-') && b.id.includes('new-')) {
                    return +a.id.replace('new-', '') - +b.id.replace('new-', '');
                } else if (a.id.includes('new-') && !b.id.includes('new-')) {
                    return 1;
                } else if (!a.id.includes('new-') && b.id.includes('new-')) {
                    return -1;
                } else {
                    return a.id.localeCompare(b.id);
                }
            },
            width: 200,
        },
        {
            title: 'Пользователь',
            dataIndex: 'user_id',
            render: (_, rec) => {
                const getUserName = () =>
                    rec.user_name == null || rec.user_name.length == 0
                        ? `- ${rec.user_id}`
                        : rec.user_name;
                return rec.user_id == null ? (
                    <Row className="table-user not-selected">
                        <Col className="p3 user-name">Не выбран</Col>
                    </Row>
                ) : (
                    <Row
                        className="table-user"
                        onClick={() => {
                            navigate(`/management/users/${rec.user_id}`);
                        }}
                    >
                        <Col className="user-avatar">
                            {rec.user_picture == null && (
                                <div className="p1-strong">{getUserName()[0]}</div>
                            )}
                        </Col>
                        <Col className="p3 user-name">
                            <Button
                                className="creator-btn"
                                type="link"
                            >
                                {getUserName()}
                            </Button>
                        </Col>
                    </Row>
                );
            },
            sorter: (a, b) => {
                if (a.user_id == null && b.user_id == null) {
                    return 0;
                } else if (a.user_id == null && b.user_id != null) {
                    return 1;
                } else if (a.user_id != null && b.user_id == null) {
                    return -1;
                } else {
                    return a.user_id.localeCompare(b.user_id);
                }
            },
            width: 200,
        },
        {
            title: 'Состояние',
            dataIndex: 'status',
            filters: Object.keys(tableStatuses).map((tsk) => {
                const tsv = tableStatuses[tsk];
                return {
                    text: (
                        <FilterButton
                            key={tsk}
                            hex={tsv.color}
                            text={tsv.text}
                        />
                    ),
                    value: tsv.text,
                };
            }),
            onFilter: (value, record) => {
                return record.status.find((si) => si.text.includes('' + value)) != undefined;
            },
            render: (_, rec) => (
                <Row className="table-status">
                    {rec.status.map((si, i) => (
                        <FilterButton
                            key={`status-${i}`}
                            hex={si.color}
                            text={si.text}
                        />
                    ))}
                    {rec.status.length == 0 && <span className="p3 gray">Нет статусов</span>}
                </Row>
            ),
            width: 200,
        },
        {
            title: 'Создано',
            dataIndex: 'created_at',
            render: (value) => (
                <span className="p3 lighter-tone">{Common.formatDateString(value)}</span>
            ),
            sorter: (a, b) => {
                const aTime = new Date(a.created_at).getTime();
                const bTime = new Date(b.created_at).getTime();
                return aTime - bTime;
            },
            width: 200,
        },
        {
            title: 'Изменено',
            dataIndex: 'updated_at',
            render: (value) => (
                <span className="p3 lighter-tone">{Common.formatDateString(value)}</span>
            ),
            sorter: (a, b) => {
                const aTime = new Date(a.updated_at).getTime();
                const bTime = new Date(b.updated_at).getTime();
                return aTime - bTime;
            },
            width: 200,
        },
    ];

    useEffect(() => {
        loadNecessary();
    }, [sessionId]);

    function checkPermission() {
        return (
            Permissions.checkPermission(Permissions.UserList) &&
            Permissions.checkPermission(Permissions.UserGet) &&
            Permissions.checkPermission(Permissions.SessionGet) &&
            Permissions.checkPermission(Permissions.SessionDelete) &&
            Permissions.checkPermission(Permissions.SessionAssignmentList) &&
            Permissions.checkPermission(Permissions.SessionAssignmentGet) &&
            Permissions.checkPermission(Permissions.SessionAssignmentCreate) &&
            Permissions.checkPermission(Permissions.SessionAssignmentDelete) &&
            Permissions.checkPermission(Permissions.SessionAssignmentBulkAdd) &&
            Permissions.checkPermission(Permissions.SessionAssignmentBulkResult) &&
            Permissions.checkPermission(Permissions.SimulationGet)
        );
    }

    async function loadNecessary() {
        if (sessionId == undefined) {
            navigate('/controls/sessions');
            message.error('Сессия не найдена');
            return;
        }
        if (!checkPermission()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        state.changes = {};
        state.lastSAId = -1;
        state.onlyShowDeleted = false;
        state.selectedSARows = [];
        state.selectedUsers = [];
        state.userPickerMode = 'single';
        state.userPickerOpen = false;
        state.isLoading = true;
        try {
            const session = await CRMAPIManager.request<SessionResp>(async (api) => {
                return await api.getSession(sessionId);
            });
            if (session.errorMessages) throw session.errorMessages;
            state.session = session.data.data;
            const simulation = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(session.data.data?.simulation_id);
            });
            if (simulation.errorMessages) throw simulation.errorMessages;
            state.simulation = simulation.data.data;
            if (
                session.data.data?.manager_id == SettingsManager.getConnectionCredentials()?.user_id
            ) {
                state.creator = rootStore.currentUserStore.getUser;
            } else {
                const creator = await CRMAPIManager.request<UserResp>(async (api) => {
                    return await api.getUser(session.data.data?.manager_id);
                });
                if (creator.errorMessages) throw creator.errorMessages;
                state.creator = creator.data.data;
            }
            const userList = await CRMAPIManager.request<UserListResp>(async (api) => {
                return await api.getUserList({
                    query: null,
                    role: null,
                    page: 1,
                    per_page: 100,
                });
            });
            if (userList.errorMessages) throw userList.errorMessages;
            state.userList = userList.data.data;
            const SAlist = await CRMAPIManager.request<SessionAssignmentListResp>(async (api) => {
                return await api.getSessionAssignmentList({
                    session_id: session.data.data?.id,
                    user_id: null,
                    state: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        created_at: null,
                    },
                });
            });
            if (SAlist.errorMessages) throw SAlist.errorMessages;
            state.sessionAssignmentList = SAlist.data.data;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/controls/sessions');
            }
            messageApi.error('Ошибка при загрузке сущностей');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function noEditExplanation() {
        if (state.creator?.deleted_at != null) {
            return 'Менеджер-владелец сессии удалён';
        }
        if (state.simulation?.deleted_at != null) {
            return 'Симуляция удалена';
        }
        return '';
    }

    async function deleteSession() {
        state.isLoading = true;
        try {
            state.skipPreventLeaving = true;
            const session = await CRMAPIManager.request<SessionResp>(async (api) => {
                return await api.deleteSession(sessionId);
            });
            if (session.errorMessages) throw session.errorMessages;
            navigate('/controls/sessions');
            message.success('Сессия была удалена');
        } catch (errors) {
            state.skipPreventLeaving = false;
            messageApi.error('Ошибка при удалении сессии');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function makeSessionAssignment(
        user_id: TSessionAssignment['user_id'] = null,
    ): TSessionAssignment {
        let user = null;
        if (user_id != null) {
            const find = state.userList.find((uli) => uli.id == user_id);
            if (find != undefined) user = find;
        }
        const newSA: TSessionAssignment = {
            id: `new${state.lastSAId}`,
            manager_id: state.session?.manager_id,
            manager_name: state.session?.manager_name,
            manager_picture: state.session?.manager_picture,
            session_id: state.session?.id,
            simulation_description: state.simulation?.description,
            simulation_name: state.session?.simulation_name,
            user_id: user?.id,
            user_name: user?.name,
            user_picture: user?.picture,
            state: 'new',
            day: 1,
            tick: 0,
            created_at: Common.dateNowString(),
            updated_at: null,
        };
        state.lastSAId -= 1;
        return newSA;
    }

    function getUser(user_id: TUser['id']) {
        const user = state.userList.find((uli) => uli.id == user_id);
        return user == undefined ? null : user;
    }

    function getStatusList(sai: TSessionAssignment) {
        const statuses: SADataType['status'] = [];
        if (sai.id.startsWith('new-')) {
            statuses.push(tableStatuses['new']);
        }
        if (state.changes[sai.id] != undefined && state.changes[sai.id].change_type == 'delete') {
            statuses.push(tableStatuses['deleted']);
        }
        if (state.changes[sai.id] != undefined && state.changes[sai.id].change_type == 'update') {
            statuses.push(tableStatuses['updated']);
        }
        switch (sai.state) {
            case 'prestarted': {
                statuses.push(tableStatuses['prestarted']);
                break;
            }
            case 'started': {
                statuses.push(tableStatuses['started']);
                break;
            }
            case 'paused': {
                statuses.push(tableStatuses['paused']);
                break;
            }
            case 'resumed': {
                statuses.push(tableStatuses['resumed']);
                break;
            }
            case 'stopped': {
                statuses.push(tableStatuses['stopped']);
                break;
            }
            case 'finished': {
                statuses.push(tableStatuses['finished']);
                break;
            }
        }
        if (sai.user_id == null) {
            statuses.push(tableStatuses['no-user']);
        }
        return statuses;
    }

    function makeTableData(): SADataType[] {
        const tableData = state.sessionAssignmentList.map((sai) => {
            const change = state.changes[sai.id];
            const tempSAi = change != undefined ? change : sai;
            const user = getUser(tempSAi.user_id);
            const deleted = change != undefined && change.change_type == 'delete';
            return {
                ...tempSAi,
                key: tempSAi.id,
                deleted: deleted,
                status: getStatusList(tempSAi),
                user_name: user?.name,
                user_picture: user?.picture,
            };
        });
        return tableData.filter((tdi) => tdi.deleted == state.onlyShowDeleted);
    }

    const SARowSelection: TableRowSelection<SADataType> = {
        columnWidth: 40,
        getCheckboxProps: (sai: SADataType) => ({
            disabled: !editable,
            name: sai.id,
        }),
        onChange: (srk) => (state.selectedSARows = srk),
        selectedRowKeys: state.selectedSARows,
    };

    function addNewSessionAssignments() {
        const amount = state.addAmount;
        const newSAList = [];
        const tempChanges = { ...state.changes };
        const rowKeySelectList = [];
        for (let i = 0; i < amount; i++) {
            const newSA = makeSessionAssignment();
            newSAList.push(newSA);
            tempChanges[newSA.id] = {
                ...newSA,
                change_type: 'add',
            };
            rowKeySelectList.push(newSA.id);
        }
        state.changes = tempChanges;
        state.sessionAssignmentList = [
            ...state.sessionAssignmentList,
            ...newSAList,
        ];
        state.selectedSARows = rowKeySelectList;
    }

    function makeChangeStats() {
        const values = Object.values(state.changes);
        let chNew = 0;
        let chDelete = 0;
        for (let i = 0; i < values.length; i++) {
            if (values[i].state == 'new') chNew += 1;
            switch (values[i].change_type) {
                case 'delete': {
                    chDelete += 1;
                    break;
                }
            }
        }
        return `(новые: ${chNew}, удалены: ${chDelete})`;
    }

    function assignSelectedSAToUser() {
        state.userPickerMode = 'single';
        state.userPickerOpen = true;
    }

    function assignSAToSelectedUsers() {
        state.userPickerMode = 'multiple';
        state.userPickerOpen = true;
    }

    function deleteSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedSARows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                tempChanges[change].change_type = 'delete';
            } else {
                tempChanges['' + tempSKR[i]] = {
                    ...state.sessionAssignmentList.find((sai) => sai.id == tempSKR[i]),
                    change_type: 'delete',
                };
            }
        }
        state.changes = tempChanges;
        state.selectedSARows = [];
    }

    function restoreSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedSARows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                if (tempChanges[change].id.startsWith('new-')) {
                    tempChanges[change].change_type = 'add';
                } else {
                    delete tempChanges[change];
                }
            }
        }
        state.changes = tempChanges;
        state.selectedSARows = [];
    }

    const countNoUserSA = useCallback(() => {
        const keys = Object.keys(state.changes);
        let count = 0;
        for (let i = 0; i < keys.length; i++) {
            const value = state.changes[keys[i]];
            if (value.user_id == null && value.change_type != 'delete') {
                count++;
            }
        }
        return count;
    }, [state.changes]);

    function handleSelectConfirm() {
        const tempSelRows = [...state.selectedSARows];
        if (state.userPickerMode == 'single' && tempSelRows.length == 0) {
            state.userPickerOpen = false;
            state.selectedUsers = [];
        }
        const tempSelUsers = [...state.selectedUsers];
        if (tempSelUsers.length == 0) {
            state.userPickerOpen = false;
            state.selectedUsers = [];
        }

        let tempSA = [...state.sessionAssignmentList];
        const tempChanges = { ...state.changes };
        const keys = Object.keys(tempChanges);

        if (state.userPickerMode == 'single') {
            for (let i = 0; i < tempSelRows.length; i++) {
                const changeKey = keys.find((k) => k == tempSelRows[i]);
                if (changeKey != undefined) {
                    tempChanges[changeKey].updated_at = Common.dateNowString();
                    tempChanges[changeKey].user_id = tempSelUsers[0];
                }
            }
            state.changes = tempChanges;
            state.selectedUsers = [];
            state.userPickerOpen = false;
        } else {
            const newSelList = [];
            for (let i = 0; i < tempSelUsers.length; i++) {
                const newSA = makeSessionAssignment(tempSelUsers[i]);
                tempSA = [...tempSA, newSA];
                tempChanges[newSA.id] = {
                    ...newSA,
                    change_type: 'add',
                };
                newSelList.push(newSA.id);
            }
            state.sessionAssignmentList = tempSA;
            state.changes = tempChanges;
            state.selectedUsers = [];
            state.selectedSARows = newSelList;
            state.userPickerOpen = false;
        }
    }

    function prepareChanges(): TBulkUpdate<
        | {
              session_id: TSessionAssignment['session_id'];
              user_id: TSessionAssignment['user_id'];
          }
        | {
              id: TSessionAssignment['id'];
          }
    > {
        return Object.keys(state.changes)
            .filter(
                (ch) =>
                    !(
                        state.changes[ch].state == 'new' &&
                        state.changes[ch].change_type == 'delete'
                    ),
            )
            .map((changeKey, i) => {
                const change = state.changes[changeKey];
                return {
                    action: change.change_type == 'delete' ? 'remove' : 'add',
                    index: i + 1,
                    value:
                        change.change_type == 'add'
                            ? {
                                  session_id: change.session_id,
                                  user_id: change.user_id,
                              }
                            : {
                                  id: change.id,
                              },
                };
            });
    }

    async function saveChanges() {
        const counter = countNoUserSA();
        if (counter > 0) {
            messageApi.warning(`Остались назначения без пользователя: ${counter} шт.`);
            return false;
        }
        state.isLoading = true;
        let returnValue = false;
        const bulkBody = prepareChanges();
        try {
            const result = await CRMAPIManager.bulkRequest<
                | {
                      session_id: TSessionAssignment['session_id'];
                      user_id: TSessionAssignment['user_id'];
                  }
                | {
                      id: TSessionAssignment['id'];
                  }
            >(
                bulkBody,
                async (api, _bulkBody) => {
                    return await api.bulkSessionAssignment(_bulkBody);
                },
                async (api, task_id) => {
                    return await api.bulkResultSessionAssignment(task_id);
                },
            );
            if (result.errorMessages) throw result.errorMessages;
            message.success('Изменения сохранены');
            await loadNecessary();
            returnValue = true;
        } catch (errors) {
            messageApi.error('Ошибка при сохранении, попробуйте ещё раз');
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    async function startOrResumeSelected() {
        const selRows = state.selectedSARows;
        let counter = 0;
        state.isLoading = true;
        try {
            for (let i = 0; i < selRows.length; i++) {
                const SA = state.sessionAssignmentList.find((sai) => sai.id == selRows[i]);
                if (SA.state == 'prestarted') {
                    const result = await CRMAPIManager.request<SessionAssignmentResp>(
                        async (api) => {
                            return await api.SAstart(SA.id);
                        },
                    );
                    if (result.errorMessages) throw result.errorMessages;
                    counter += 1;
                } else if (SA.state == 'paused' || SA.state == 'stopped') {
                    const result = await CRMAPIManager.request<SessionAssignmentResp>(
                        async (api) => {
                            return await api.SAresume(SA.id);
                        },
                    );
                    if (result.errorMessages) throw result.errorMessages;
                    counter += 1;
                }
            }
            await loadNecessary();
            messageApi.success(`Запущено/возобновлено ${counter} шт.`);
        } catch (error) {
            await loadNecessary();
            messageApi.warning(`Ошибка при пуске/возобновлении; удалось - ${counter} шт.`);
            console.log(error);
        }
        state.isLoading = false;
    }

    async function pauseSelected() {
        const selRows = state.selectedSARows;
        let counter = 0;
        state.isLoading = true;
        try {
            for (let i = 0; i < selRows.length; i++) {
                const SA = state.sessionAssignmentList.find((sai) => sai.id == selRows[i]);
                if (SA.state == 'started' || SA.state == 'resumed') {
                    const result = await CRMAPIManager.request<SessionAssignmentResp>(
                        async (api) => {
                            return await api.SApause(SA.id);
                        },
                    );
                    if (result.errorMessages) throw result.errorMessages;
                    counter += 1;
                }
            }
            await loadNecessary();
            messageApi.success(`Поставлено на паузу ${counter} шт.`);
        } catch (error) {
            await loadNecessary();
            messageApi.warning(`Ошибка при постановке на паузу; удалось - ${counter} шт.`);
            console.log(error);
        }
        state.isLoading = false;
    }

    async function stopSelected() {
        const selRows = state.selectedSARows;
        let counter = 0;
        state.isLoading = true;
        try {
            for (let i = 0; i < selRows.length; i++) {
                const SA = state.sessionAssignmentList.find((sai) => sai.id == selRows[i]);
                if (SA.state == 'started' || SA.state == 'resumed') {
                    const result = await CRMAPIManager.request<SessionAssignmentResp>(
                        async (api) => {
                            return await api.SAstop(SA.id);
                        },
                    );
                    if (result.errorMessages) throw result.errorMessages;
                    counter += 1;
                }
            }
            await loadNecessary();
            messageApi.success(`Остановлено ${counter} шт.`);
        } catch (error) {
            await loadNecessary();
            messageApi.warning(`Ошибка при остановке; удалось - ${counter} шт.`);
            console.log(error);
        }
        state.isLoading = false;
    }

    const actionsAvailable = useCallback(() => {
        const selRows = state.selectedSARows;
        let allowDelete = true;
        let allowAssignUser = true;
        for (let i = 0; i < selRows.length; i++) {
            const SA = state.sessionAssignmentList.find((sai) => sai.id == selRows[i]);
            if (!(SA?.state == 'new' || SA?.state == 'prestarted')) {
                allowDelete = false;
                allowAssignUser = false;
            }
        }
        return { allowDelete, allowAssignUser };
    }, [state.selectedSARows]);

    return (
        <UniLayout
            activeTab="sessions"
            additionalClass="session-profile-min-width"
            tabSet="controls"
        >
            <div className="session-profile">
                {contextHolder}
                {state.isLoading && <Loader />}
                <PreventLeaving
                    anyChanges={anyChanges && !state.skipPreventLeaving}
                    onSave={saveChanges}
                />
                {state.userPickerOpen && (
                    <UserDrawer
                        isOpen={state.userPickerOpen}
                        multiple={state.userPickerMode == 'multiple'}
                        onConfirm={handleSelectConfirm}
                        onSelect={(users: TUser['id'][]) => {
                            state.selectedUsers = users;
                        }}
                        selected={state.selectedUsers}
                        setIsOpen={(value) => (state.userPickerOpen = value)}
                        useCase={UserDrawerUseCase.All}
                    />
                )}
                <div className="session-card">
                    <Row className="session-info-row">
                        <Col>
                            <Row className="header-row">
                                <Col>
                                    <Row>
                                        <h4>ID {state.session?.id}</h4>
                                        <CopyButton
                                            textToCopy={`ID сессии ${state.session?.id}`}
                                            textToShow="ID скопирован"
                                            size={28}
                                        />
                                    </Row>
                                </Col>
                                {!editable && (
                                    <Col className="p2 readonly-message">
                                        {`ТОЛЬКО ЧТЕНИЕ: ${noEditExplanation()}`}
                                    </Col>
                                )}
                            </Row>
                            <Row className="body-row">
                                <Col>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Создатель:</span>
                                            </Row>
                                            <Row
                                                className="creator-row"
                                                onClick={() => {
                                                    if (!anyChanges) {
                                                        navigate(
                                                            `/management/users/${state.creator?.id}`,
                                                        );
                                                    }
                                                }}
                                            >
                                                <Col className="creator-avatar">
                                                    <div className="p1-strong">
                                                        {state.creator?.name == null ||
                                                        state.creator?.name.length == 0
                                                            ? '-'
                                                            : state.creator?.name[0]}
                                                    </div>
                                                </Col>
                                                <Col className="p3 creator-name">
                                                    <Button
                                                        className="creator-btn"
                                                        type="link"
                                                    >
                                                        {state.creator?.name}
                                                    </Button>
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                </Col>
                                <Col>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Была создана:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {Common.formatDateString(
                                                        state.session?.created_at,
                                                    )}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Последнее изменение:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {Common.formatDateString(
                                                        state.session?.updated_at,
                                                    )}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row className="simulation-info-row">
                        <Col>
                            <Row className="header-row">
                                <Col style={{ minWidth: '100px' }}>
                                    <Row>
                                        <h4>ID симуляции: {state.simulation?.id}</h4>
                                        <CopyButton
                                            textToCopy={`ID симуляции ${state.simulation?.id}`}
                                            textToShow="ID симуляции скопирован"
                                            size={28}
                                        />
                                    </Row>
                                </Col>
                                <Col
                                    className="filters desc-l-strong"
                                    style={{
                                        maxWidth: '768px',
                                    }}
                                >
                                    <Row style={{ gap: '8px 16px' }}>
                                        {state.simulation?.filters.map((f) => {
                                            return (
                                                <FilterButton
                                                    key={`sim-tag-${f.id}`}
                                                    hex={f.colorHEX}
                                                    text={f.name}
                                                />
                                            );
                                        })}
                                    </Row>
                                </Col>
                            </Row>
                            <Row className="body-row">
                                <Col>
                                    <Row>
                                        <div className="p1">{state.simulation?.name}</div>
                                        <CopyButton
                                            textToCopy={state.simulation?.name}
                                            textToShow="Название скопировано"
                                            size={26}
                                        />
                                    </Row>
                                    <Row>
                                        <div className="description desc-l">
                                            {state.simulation?.description}
                                        </div>
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row className="session-assignment-row">
                        <Col>
                            <Row className="header-row">
                                <Col>
                                    <h4>
                                        {`Назначений: ${
                                            state.sessionAssignmentList.length
                                        } ${makeChangeStats()}`}
                                    </h4>
                                </Col>
                            </Row>
                            <Row className="body-row">
                                <Col>
                                    <Row className="table-actions">
                                        <Col>
                                            <span className="selected p3">
                                                {state.selectedSARows.length} выбрано
                                            </span>
                                        </Col>
                                        <Col>
                                            <Space.Compact
                                                block
                                                className="p3 add-group"
                                            >
                                                <Button
                                                    disabled={!editable || state.onlyShowDeleted}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.addAmount = 1;
                                                    }}
                                                    type={
                                                        state.addAmount == 1 ? 'primary' : 'default'
                                                    }
                                                >
                                                    1
                                                </Button>
                                                <Button
                                                    disabled={!editable || state.onlyShowDeleted}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.addAmount = 5;
                                                    }}
                                                    type={
                                                        state.addAmount == 5 ? 'primary' : 'default'
                                                    }
                                                >
                                                    5
                                                </Button>
                                                <Button
                                                    disabled={!editable || state.onlyShowDeleted}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.addAmount = 10;
                                                    }}
                                                    type={
                                                        state.addAmount == 10
                                                            ? 'primary'
                                                            : 'default'
                                                    }
                                                >
                                                    10
                                                </Button>
                                                <InputNumber
                                                    disabled={!editable || state.onlyShowDeleted}
                                                    max={10}
                                                    min={1}
                                                    onChange={(value) => {
                                                        state.addAmount = value;
                                                    }}
                                                    value={state.addAmount}
                                                />
                                                <Button
                                                    disabled={!editable || state.onlyShowDeleted}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        addNewSessionAssignments();
                                                    }}
                                                    type="primary"
                                                >
                                                    +
                                                </Button>
                                            </Space.Compact>
                                        </Col>
                                        {!state.onlyShowDeleted && (
                                            <Col>
                                                <Button
                                                    disabled={!editable}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        assignSAToSelectedUsers();
                                                    }}
                                                >
                                                    Назначить выбранным
                                                </Button>
                                            </Col>
                                        )}
                                        {state.selectedSARows.length > 0 &&
                                            !state.onlyShowDeleted && (
                                                <Col>
                                                    <Button
                                                        disabled={
                                                            !editable ||
                                                            !actionsAvailable().allowAssignUser
                                                        }
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            assignSelectedSAToUser();
                                                        }}
                                                    >
                                                        Выбрать пользователя
                                                    </Button>
                                                </Col>
                                            )}
                                        {state.selectedSARows.length > 0 &&
                                            !state.onlyShowDeleted && (
                                                <Col className="state-actions">
                                                    <Row>
                                                        <Tooltip
                                                            placement="top"
                                                            title="Пуск/продолжить"
                                                        >
                                                            <Button
                                                                disabled={anyChanges}
                                                                icon={
                                                                    <FontAwesomeIcon
                                                                        icon={faPlay}
                                                                    />
                                                                }
                                                                onClick={startOrResumeSelected}
                                                            />
                                                        </Tooltip>
                                                        <Tooltip
                                                            placement="top"
                                                            title="Поставить на паузу"
                                                        >
                                                            <Button
                                                                disabled={anyChanges}
                                                                icon={
                                                                    <FontAwesomeIcon
                                                                        icon={faPause}
                                                                    />
                                                                }
                                                                onClick={pauseSelected}
                                                            />
                                                        </Tooltip>
                                                        <Tooltip
                                                            placement="top"
                                                            title="Остановить"
                                                        >
                                                            <Button
                                                                disabled={anyChanges}
                                                                icon={
                                                                    <FontAwesomeIcon
                                                                        icon={faStop}
                                                                    />
                                                                }
                                                                onClick={stopSelected}
                                                            />
                                                        </Tooltip>
                                                    </Row>
                                                </Col>
                                            )}
                                        {state.selectedSARows.length > 0 &&
                                            !state.onlyShowDeleted && (
                                                <Col>
                                                    <Button
                                                        danger
                                                        disabled={
                                                            !editable ||
                                                            !actionsAvailable().allowDelete
                                                        }
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            deleteSelected();
                                                        }}
                                                    >
                                                        Удалить
                                                    </Button>
                                                </Col>
                                            )}
                                        {state.selectedSARows.length > 0 &&
                                            state.onlyShowDeleted && (
                                                <Col>
                                                    <Button
                                                        danger
                                                        disabled={!editable}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            restoreSelected();
                                                        }}
                                                    >
                                                        Восстановить
                                                    </Button>
                                                </Col>
                                            )}
                                        <Col>
                                            <Checkbox
                                                checked={state.onlyShowDeleted}
                                                onChange={(e) => {
                                                    state.selectedSARows = [];
                                                    state.onlyShowDeleted = e.target.checked;
                                                }}
                                            >
                                                Корзина
                                            </Checkbox>
                                        </Col>
                                    </Row>
                                    <Row className="table-container">
                                        <Table<SADataType>
                                            bordered
                                            columns={columns}
                                            dataSource={makeTableData()}
                                            locale={{
                                                emptyText: (
                                                    <Col
                                                        className="empty-text p3"
                                                        flex={1}
                                                    >
                                                        <Row>Таких назначений нет :)</Row>
                                                    </Col>
                                                ),
                                            }}
                                            pagination={false}
                                            rowSelection={SARowSelection}
                                            scroll={{
                                                x: 'max-content',
                                                y: 77 * 7,
                                            }}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row className="p2 controls-row">
                        <Col>
                            <Row>
                                {anyChanges && (
                                    <Tooltip
                                        placement="topLeft"
                                        title={
                                            countNoUserSA() > 0
                                                ? `Назначений без пользователя: ${countNoUserSA()}`
                                                : null
                                        }
                                    >
                                        <Button
                                            onClick={() => {
                                                if (anyChanges && countNoUserSA() == 0) {
                                                    saveChanges();
                                                }
                                            }}
                                        >
                                            Сохранить
                                        </Button>
                                    </Tooltip>
                                )}
                            </Row>
                        </Col>
                        <Col>
                            <Row>
                                <Button
                                    onClick={() => {
                                        navigate(
                                            `/controls/assignments?session_id=${state.session?.id}`,
                                        );
                                    }}
                                >
                                    Назначения
                                </Button>
                                <Button
                                    onClick={() => {
                                        navigate(`/simulations/${state.simulation?.id}`);
                                    }}
                                >
                                    К симуляции
                                </Button>
                                <Popconfirm
                                    cancelText="Отмена"
                                    okText="Подтвердить"
                                    onConfirm={deleteSession}
                                    title="Сессия не может быть восстановлена"
                                >
                                    <Button>Удалить</Button>
                                </Popconfirm>
                            </Row>
                        </Col>
                    </Row>
                </div>
            </div>
        </UniLayout>
    );
});

export default SessionProfile;
