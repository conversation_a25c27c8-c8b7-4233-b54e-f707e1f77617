import { TSession } from 'types/session/session';
import { useReactive } from 'ahooks';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, Col, ConfigProvider, List, message, Row, Tag } from 'antd';
import { CRMAPIManager } from '@api/crmApiManager';
import { SessionListResp } from '@api/responseModels/sessions/sessionListResponse';
import { useEffect } from 'react';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { Loader } from '@components/ui/loader/loader';
import { Permissions } from 'src/shared/permissions';
import { GlobalConstants } from 'src/shared/constants';

import './sessionList.scss';

type TState = {
    isLoading: boolean;
    sessions: TSession[];
};

const SessionList = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        sessions: [],
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const [searchParams] = useSearchParams();

    async function loadSessions() {
        if (!Permissions.checkPermission(Permissions.SessionList)) {
            navigate('/lk');
            message.error('Недостаточно прав для просмотра списка сессий');
            return;
        }
        state.isLoading = true;
        try {
            const sim_id = searchParams.get('sim_id');
            const manager_id = searchParams.get('manager_id');
            const result = await CRMAPIManager.request<SessionListResp>(async (api) => {
                return await api.getSessionList({
                    manager_id: manager_id,
                    simulation_id: sim_id != null ? +sim_id : null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        created_at: null,
                    },
                });
            });
            if (result.errorMessages) throw result.errorMessages;
            state.sessions = result.data.data;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка сессий');
            console.log(err);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        loadSessions();
    }, []);

    function makeListItems() {
        const arr = [];
        arr.push(...state.sessions);
        return arr;
    }

    return (
        <UniLayout
            activeTab="sessions"
            additionalClass="list-min-width"
            tabSet="controls"
        >
            <div className="session-list-container">
                {state.isLoading && <Loader />}
                {contextHolder}
                <div className="session-list">
                    {(searchParams.get('sim_id') != null ||
                        searchParams.get('manager_id') != null) && (
                        <div className="p3 tag-section">
                            {searchParams.get('sim_id') != null && (
                                <Tag
                                    closeIcon
                                    onClose={() => {
                                        searchParams.delete('sim_id');
                                        navigate('/controls/sessions');
                                        loadSessions();
                                    }}
                                >
                                    ID симуляции {searchParams.get('sim_id')}
                                </Tag>
                            )}
                            {searchParams.get('manager_id') != null && (
                                <Tag
                                    closeIcon
                                    onClose={() => {
                                        searchParams.delete('manager_id');
                                        navigate('/controls/sessions');
                                        loadSessions();
                                    }}
                                >
                                    ID менеджера {searchParams.get('manager_id')}
                                </Tag>
                            )}
                        </div>
                    )}
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={makeListItems()}
                            grid={GlobalConstants.ListGridCols}
                            loading={state.isLoading}
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких сессий нет :)</Row>
                                    </Col>
                                ),
                            }}
                            renderItem={(item: TSession) => (
                                <div className="session-list-card">
                                    <Col flex={1}>
                                        <Row className="header-row">
                                            <div className="p2-strong">ID {item.id}</div>
                                            <CopyButton
                                                textToCopy={`ID ${item.id}`}
                                                textToShow="ID скопирован"
                                                size={24}
                                            />
                                        </Row>
                                        <Row className="body-row">
                                            <Col flex={1}>
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Симуляция:</span>
                                                    </Col>
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/simulations/${item.simulation_id}`,
                                                                    );
                                                                }}
                                                                type="link"
                                                            >
                                                                {item.simulation_name}
                                                            </Button>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Менеджер:</span>
                                                    </Col>
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/management/users/${item.manager_id}`,
                                                                    );
                                                                }}
                                                                type="link"
                                                            >
                                                                {item.manager_name}
                                                            </Button>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                            </Col>
                                        </Row>
                                        <Row className="event-card-controls p3">
                                            <Button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    navigate(`/controls/sessions/${item.id}`);
                                                }}
                                            >
                                                Открыть
                                            </Button>
                                            {Permissions.checkPermission(
                                                Permissions.SessionAssignmentList,
                                            ) && (
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        navigate(
                                                            `/controls/assignments?session_id=${item.id}`,
                                                        );
                                                    }}
                                                >
                                                    Назначения
                                                </Button>
                                            )}
                                        </Row>
                                    </Col>
                                </div>
                            )}
                        />
                    </ConfigProvider>
                </div>
            </div>
        </UniLayout>
    );
};

export default SessionList;
