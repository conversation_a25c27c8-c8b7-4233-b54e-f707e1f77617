import { CRMAPIManager } from '@api/crmApiManager';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimTask } from 'types/simulation/simulationTask';
import { Loader } from '@components/ui/loader/loader';
import { TSimulation } from 'types/simulation/simulation';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import Dagre from '@dagrejs/dagre';
import {
    addEdge,
    applyEdgeChanges,
    applyNodeChanges,
    Background,
    Connection,
    ConnectionLineType,
    Edge,
    EdgeRemoveChange,
    getOutgoers,
    MarkerType,
    Node,
    NodeRemoveChange,
    ReactFlow,
    ReactFlowProvider,
    reconnectEdge,
    useEdgesState,
    useNodesState,
    useReactFlow,
    useStoreApi,
} from '@xyflow/react';
import { Col, message, Modal, Row } from 'antd';
import taskNode from './taskNode';
import { GraphLowerMenu } from './graphLowerMenu';
import { NodeInfoDrawer } from './nodeInfoDrawer';
import { Item, NodeTableDrawer } from './nodeTableDrawer';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import _ from 'lodash';
import Colors from 'src/shared/colors';
import { Common } from 'src/shared/common';
import { RequestResult } from '@api/responseModels/requestResult';
import { Permissions } from 'src/shared/permissions';
import { TaskUtils } from '@shared/utils/taskUtitlty';
import { DefaultTimeSettings } from 'src/store/ingame/data';
import { GanttUseCases } from '@components/gantt/gantt';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';

import '@xyflow/react/dist/style.css';
import './constructorGraph.scss';

// Константы для стилей
const EDGE_STYLE = {
    stroke: '#4a9eff',
    strokeWidth: 1.5,
    transition: 'all 0.3s ease',
};

const EDGE_MARKER = {
    type: MarkerType.ArrowClosed,
    width: 12,
    height: 12,
    color: '#4a9eff',
    strokeWidth: 1.5,
};

const defaultEdgeOptions = {
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#00bfff', strokeWidth: 2 },
};

const nodeTypes = { taskNode: taskNode };

const getLayoutedElements = (nodes, edges, options) => {
    const g = new Dagre.graphlib.Graph().setDefaultEdgeLabel(() => ({}));
    g.setGraph({
        align: 'UL',
        edgesep: 0,
        nodesep: 30,
        rankdir: options.direction,
        ranksep: 30,
    });

    edges.forEach((edge) => g.setEdge(edge.source, edge.target));
    nodes.forEach((node) =>
        g.setNode(node.id, {
            ...node,
            width: node.measured?.width ?? 0,
            height: node.measured?.height ?? 0,
        }),
    );

    Dagre.layout(g);

    return {
        nodes: nodes.map((node) => {
            const position = g.node(node.id);
            // We are shifting the dagre node position (anchor=center center) to the top left
            // so it matches the React Flow node anchor point (top left).
            const x = position.x - (node.measured?.width ?? 0) / 2;
            const y = position.y - (node.measured?.height ?? 0) / 2;

            return { ...node, position: { x, y } };
        }),
        edges,
    };
};

export type TaskNode = Node<
    TSimTask & {
        allowLeft: boolean;
        allowRight: boolean;
        className: string;
    }
>;

type TState = {
    calculatedBudget: number;
    calculatedIngameDays: number;
    conflictTotalBudget: boolean;
    conflictWeeks: boolean;
    firstTaskId?: number;
    isLoading: boolean;
    lastId: number;
    lastTaskId?: number;
    nodesChanged: boolean;
    proposeTotalBudget: TSimulation['total_budget'];
    proposeWeeks: TSimulation['weeks'];
    simulation: TSimulation;
    tableIsOpen: boolean;
    tasks: TSimTask[];
};

const ConstructorGraphContent = (): JSX.Element => {
    const state = useReactive<TState>({
        calculatedBudget: 0,
        calculatedIngameDays: 0,
        conflictTotalBudget: false,
        conflictWeeks: false,
        firstTaskId: null,
        isLoading: false,
        lastId: 1,
        lastTaskId: null,
        nodesChanged: false,
        proposeTotalBudget: 0,
        proposeWeeks: 0,
        simulation: null,
        tableIsOpen: false,
        tasks: [],
    });
    const { simId } = useParams();
    const store = useStoreApi();
    const { addNodes, fitView, getEdges, getNodes, screenToFlowPosition, zoomIn, zoomOut } =
        useReactFlow();
    const [
        nodes,
        setNodes,
        onNodesChange,
    ] = useNodesState<TaskNode>([]);
    const [
        edges,
        setEdges,
        onEdgesChange,
    ] = useEdgesState([]);
    const [selectedNode, setSelectedNode] = useState<TaskNode | null>(null);
    const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const anyChanges =
        state.nodesChanged ||
        state.simulation?.first_task != state.firstTaskId ||
        state.simulation?.last_task != state.lastTaskId;
    const navigate = useNavigate();
    const disableEdit = () =>
        state.simulation?.archived || state.simulation?.published || state.simulation?.finished;

    function checkPermission() {
        return (
            Permissions.checkPermission(Permissions.SimulationGet) &&
            Permissions.checkPermission(Permissions.SimulationUpdate) &&
            Permissions.checkPermission(Permissions.SimulationTaskList) &&
            Permissions.checkPermission(Permissions.SimulationTaskGet) &&
            Permissions.checkPermission(Permissions.SimulationTaskCreate) &&
            Permissions.checkPermission(Permissions.SimulationTaskDelete) &&
            Permissions.checkPermission(Permissions.SimulationTaskRestore) &&
            Permissions.checkPermission(Permissions.SimulationTaskUpdate) &&
            Permissions.checkPermission(Permissions.SimulationTaskBulkAdd) &&
            Permissions.checkPermission(Permissions.SimulationTaskBulkResult)
        );
    }

    async function loadTaskList() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.request<SimTaskListResp>(async (api) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'all',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.tasks = tl.data.data
                .sort((a, b) => a.id - b.id)
                .filter((t) => t.deleted_at == null);
            state.lastId = tl.data.meta.total + 1;
            tasksToNodes();
            tasksToEdges();
            state.nodesChanged = false;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке узлов');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadParentSim() {
        if (!checkPermission()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }

        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
            state.firstTaskId = sim.data.data.first_task;
            state.lastTaskId = sim.data.data.last_task;
            state.proposeWeeks = state.simulation.weeks;
            state.proposeTotalBudget = state.simulation.total_budget;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при загрузке симуляции!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        setEdges((prev) =>
            prev.map((e) =>
                e.source == selectedNode?.id || e.target == selectedNode?.id
                    ? { ...e, selected: true }
                    : { ...e, selected: false },
            ),
        );
    }, [selectedNode]);

    const onSelectionChange = useCallback(({ nodes }) => {
        if (nodes.length == 0) {
            setSelectedNode(null);
            setIsSidebarOpen(false);
        }
    }, []);

    useEffect(() => {
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        loadParentSim().then(() => {
            if (state.simulation == null) return;
            loadTaskList();
        });
    }, []);

    const handleZoomIn = useCallback(() => {
        zoomIn();
    }, [zoomIn]);

    const handleZoomOut = useCallback(() => {
        zoomOut();
    }, [zoomOut]);

    const handleFitView = useCallback(() => {
        fitView({ padding: 0.2 });
    }, [fitView]);

    function tasksToNodes() {
        const pt = state.tasks.map((t) => {
            return {
                id: '' + t.id,
                position: {
                    x: t.grid_x,
                    y: t.grid_y,
                },
                data: {
                    ...t,
                    allowLeft: state.simulation?.first_task != t.id,
                    allowRight: state.simulation?.last_task != t.id,
                    className: disableEdit() ? 'nopan' : '',
                },
                type: 'taskNode',
            };
        });
        setNodes(pt);
    }

    function tasksToEdges() {
        const links = [];
        for (let i = 0; i < state.tasks.length; i++) {
            const t = state.tasks[i];
            for (let j = 0; j < t.previous.length; j++) {
                links.push({
                    id: `e${t.previous[j]}-${t.id}`,
                    source: `${t.previous[j]}`,
                    target: `${t.id}`,
                    type: 'smoothstep',
                    animated: true,
                    style: EDGE_STYLE,
                    markerEnd: EDGE_MARKER,
                });
            }
        }
        setEdges(links);
    }

    const deleteNode = useCallback(
        (id: number) => {
            const target = nodes.find((n) => n.data.id == id);
            if (target == undefined) return;
            if (state.firstTaskId == id) {
                state.firstTaskId = null;
            }
            if (state.lastTaskId == id) {
                state.lastTaskId = null;
            }
            if (selectedNode?.data.id == id) {
                setSelectedNode(null);
                setIsSidebarOpen(false);
            }
            setEdges((prev) => prev.filter((e) => +e.source != id && +e.target != id));
            setNodes((prev) =>
                prev.map((n) =>
                    (target as TaskNode).data.previous.includes(n.data.id)
                        ? {
                              ...n,
                              data: {
                                  ...n.data,
                                  following: n.data.following.filter((dfi) => dfi != id),
                              },
                          }
                        : (target as TaskNode).data.following.includes(n.data.id)
                          ? {
                                ...n,
                                data: {
                                    ...n.data,
                                    previous: n.data.previous.filter((dfi) => dfi != id),
                                },
                            }
                          : n,
                ),
            );
            const changes: NodeRemoveChange[] = [{ id: '' + id, type: 'remove' }];
            setNodes((nds) => applyNodeChanges(changes, nds));
            state.nodesChanged = true;
        },
        [
            nodes,
            edges,
            setNodes,
            setEdges,
            selectedNode,
        ],
    );

    const deleteEdge = useCallback(
        (edge: Edge) => {
            const target: Edge | undefined = edges.find((e) => e.id == edge.id);
            if (target == undefined) return;
            if (selectedEdge?.id == target.id) {
                setSelectedEdge(null);
            }
            setNodes((prev) =>
                prev.map((n) =>
                    n.id == target.source
                        ? {
                              ...n,
                              data: {
                                  ...n.data,
                                  following: n.data.following.filter(
                                      (ndfi) => ndfi != +target.target,
                                  ),
                              },
                          }
                        : n.id == target.target
                          ? {
                                ...n,
                                data: {
                                    ...n.data,
                                    previous: n.data.previous.filter(
                                        (ndpi) => ndpi != +target.source,
                                    ),
                                },
                            }
                          : n,
                ),
            );
            const changes: EdgeRemoveChange[] = [{ id: target.id, type: 'remove' }];
            setEdges((eds) => applyEdgeChanges(changes, eds));
            state.nodesChanged = true;
        },
        [
            nodes,
            edges,
            setNodes,
            setEdges,
            selectedEdge,
        ],
    );

    const isValidConnection = useCallback(
        (connection: Connection) => {
            // Предотвращение циклов
            const nodes = getNodes();
            const edges = getEdges();
            const target = nodes.find((node) => node.id === connection.target);
            const hasCycle = (node, visited = new Set()) => {
                if (visited.has(node.id)) return false;

                visited.add(node.id);

                for (const outgoer of getOutgoers(node, nodes, edges)) {
                    if (outgoer.id === connection.source) return true;
                    if (hasCycle(outgoer, visited)) return true;
                }
            };
            if (edges.find((e) => e.source == connection.source && e.target == connection.target))
                return false;
            if (target.id === connection.source) return false;
            if (hasCycle(target)) return false;
            // Отмена, если конец линии - первый узел
            if (+connection.target == state.firstTaskId) return false;
            // Отмена, если начало линии - последний узел
            if (+connection.source == state.lastTaskId) return false;
            return true;
        },
        [getNodes, getEdges],
    );

    const onConnect = useCallback(
        (params: Connection) => {
            const newEdges = addEdge(
                {
                    ...params,
                    type: 'smoothstep',
                    animated: true,
                    style: EDGE_STYLE,
                    markerEnd: EDGE_MARKER,
                },
                edges,
            );
            setEdges(newEdges);
            setNodes((prev) =>
                prev.map((n) =>
                    +n.id == +params.source
                        ? {
                              ...n,
                              data: {
                                  ...n.data,
                                  following: n.data.following
                                      .concat(+params.target)
                                      .sort((a, b) => a - b),
                              },
                          }
                        : +n.id == +params.target
                          ? {
                                ...n,
                                data: {
                                    ...n.data,
                                    previous: n.data.previous
                                        .concat(+params.source)
                                        .sort((a, b) => a - b),
                                },
                            }
                          : n,
                ),
            );
            state.nodesChanged = true;
        },
        [
            edges,
            nodes,
            setEdges,
        ],
    );

    const onReconnect = useCallback(
        (oldEdge: Edge, newConnection: Connection) => {
            setNodes((prev) =>
                prev.map((n) => {
                    if (oldEdge.target != newConnection.target) {
                        if (n.id == oldEdge.target) {
                            return {
                                ...n,
                                data: {
                                    ...n.data,
                                    previous: n.data.previous.filter(
                                        (ndi) => ndi != +oldEdge.source,
                                    ),
                                },
                            };
                        } else if (n.id == newConnection.target) {
                            return {
                                ...n,
                                data: {
                                    ...n.data,
                                    previous: n.data.previous
                                        .concat(+newConnection.source)
                                        .sort((a, b) => a - b),
                                },
                            };
                        } else if (n.id == oldEdge.source) {
                            return {
                                ...n,
                                data: {
                                    ...n.data,
                                    following: n.data.following
                                        .filter((ndi) => ndi != +oldEdge.target)
                                        .concat(+newConnection.target)
                                        .sort((a, b) => a - b),
                                },
                            };
                        }
                    } else if (oldEdge.source != newConnection.source) {
                        if (n.id == oldEdge.source) {
                            return {
                                ...n,
                                data: {
                                    ...n.data,
                                    following: n.data.following.filter(
                                        (ndi) => ndi != +oldEdge.target,
                                    ),
                                },
                            };
                        } else if (n.id == newConnection.source) {
                            return {
                                ...n,
                                data: {
                                    ...n.data,
                                    following: n.data.following
                                        .concat(+newConnection.target)
                                        .sort((a, b) => a - b),
                                },
                            };
                        } else if (n.id == oldEdge.target) {
                            return {
                                ...n,
                                data: {
                                    ...n.data,
                                    previous: n.data.previous
                                        .filter((ndi) => ndi != +oldEdge.source)
                                        .concat(+newConnection.source)
                                        .sort((a, b) => a - b),
                                },
                            };
                        }
                    }
                    return n;
                }),
            );
            setEdges((els) => reconnectEdge(oldEdge, newConnection, els));
            state.nodesChanged = true;
        },
        [nodes, setNodes],
    );

    const handleNodeClick = useCallback((event: React.MouseEvent, node: TaskNode) => {
        event.preventDefault();
        setSelectedNode(node);
    }, []);

    const handleNodeDoubleClick = useCallback((event: React.MouseEvent, node: TaskNode) => {
        event.preventDefault();
        setSelectedNode(node);
        setIsSidebarOpen(true);
    }, []);

    const handleEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
        event.preventDefault();
        setSelectedEdge(edge);
    }, []);

    const setSelectedNodeById = useCallback(
        (id: string) => {
            const tnode = nodes.find((n) => n.id == id);
            if (tnode != undefined) setSelectedNode(tnode);
        },
        [nodes, selectedNode],
    );

    const onLayout = useCallback(
        (direction) => {
            const layouted = getLayoutedElements(nodes, edges, { direction });

            setNodes([...layouted.nodes]);
            setEdges([...layouted.edges]);

            fitView();
            state.nodesChanged = true;
        },
        [nodes, edges],
    );

    function validateSave() {
        if (
            !anyChanges &&
            state.simulation?.first_task == state.firstTaskId &&
            state.simulation?.last_task == state.lastTaskId
        ) {
            messageApi.info('Нет изменений');
            return false;
        }
        if (state.firstTaskId == null) {
            messageApi.warning('Укажите первый узел');
            return false;
        }
        if (state.lastTaskId == null) {
            messageApi.warning('Укажите последний узел');
            return false;
        }
        const curNodes = getNodes();
        const badTreeText = 'Все узлы должны находиться между первым и последним';
        for (let i = 0; i < curNodes.length; i++) {
            const curNodeData = curNodes[i].data as TSimTask;
            if (curNodeData.previous.length == 0 && curNodeData.id != state.firstTaskId) {
                messageApi.warning(badTreeText);
                return false;
            }
            if (curNodeData.following.length == 0 && curNodeData.id != state.lastTaskId) {
                messageApi.warning(badTreeText);
                return false;
            }
        }
        state.conflictTotalBudget = false;
        state.proposeTotalBudget = 0;
        state.calculatedBudget =
            state.simulation.hardware_budget +
            state.simulation.other_budget +
            curNodes.reduce(
                (acc: number, node: Node) => acc + (node.data as TSimTask).est_budget,
                0,
            );
        if (state.calculatedBudget > state.simulation.total_budget) {
            messageApi.warning('Не сходится бюджет');
            // Т.к. шаг в поле ввода бюджета - тысяча - берём ближайшее большее значение, кратное ей
            state.proposeTotalBudget = Math.ceil(state.calculatedBudget / 1000) * 1000;
            state.conflictTotalBudget = true;
            return false;
        }
        state.conflictWeeks = false;
        state.proposeWeeks = 0;
        state.calculatedIngameDays = TaskUtils.totalLengthOfTaskTree({
            hoursInADay: DefaultTimeSettings.workDayHours - +DefaultTimeSettings.workDayLunchSkip,
            simulation: {
                ...state.simulation,
                first_task: state.firstTaskId,
                last_task: state.lastTaskId,
            },
            tasks: curNodes
                .sort((a: Node<TSimTask>, b: Node<TSimTask>) => {
                    return a.data.id - b.data.id;
                })
                .map((cni: Node<TSimTask>) => {
                    return {
                        ...cni.data,
                        curDuration: cni.data.est_duration,
                        workers: [],
                    };
                }),
            useCase: GanttUseCases.Constructor,
        });
        if (
            state.simulation.weeks * DefaultTimeSettings.daysInAWeek <
            state.calculatedIngameDays + 1
        ) {
            messageApi.warning('Мало календарных недель');
            state.proposeWeeks =
                Math.ceil(state.calculatedIngameDays / DefaultTimeSettings.daysInAWeek) + 1;
            state.conflictWeeks = true;
            return false;
        }
        return true;
    }

    async function saveChanges() {
        if (!validateSave()) return false;
        state.isLoading = true;
        let returnValue = false;
        try {
            let bulkResult: RequestResult<BulkResultResponse<TSimTask>> = null;
            let firstTaskIndex = null;
            let lastTaskIndex = null;
            if (state.nodesChanged) {
                const items: TBulkUpdate<TSimTask> = [];
                let bIndex = 1;
                const curNodes = getNodes();
                for (let i = 1; i <= state.lastId; i++) {
                    const task = state.tasks.find((t) => t.id == i);
                    const node = curNodes.find((n) => n.data.id == i);
                    // Если задачи нет, а узел есть (новая)
                    if (task == undefined && node != undefined) {
                        items.push({
                            action: 'add',
                            index: bIndex,
                            value: {
                                ...(node as TaskNode).data,
                                grid_x: Math.ceil(node.position.x),
                                grid_y: Math.ceil(node.position.y),
                            },
                        });
                        if ((node as TaskNode).data.id == state.firstTaskId) {
                            firstTaskIndex = bIndex;
                        }
                        if ((node as TaskNode).data.id == state.lastTaskId) {
                            lastTaskIndex = bIndex;
                        }
                        bIndex += 1;
                    }
                    // Если задача есть, а узла нет (удалена)
                    else if (task != undefined && node == undefined) {
                        items.push({
                            action: 'remove',
                            index: bIndex,
                            value: {
                                uid: task.uid,
                            },
                        });
                        bIndex += 1;
                    }
                    // Если задача и узел есть, ищем различия (изменена)
                    else if (task != undefined && node != undefined) {
                        if (
                            !_.isEqual(
                                {
                                    name: task.name,
                                    description: task.description,
                                    milestone: task.milestone,
                                    est_workers: task.est_workers,
                                    est_duration: task.est_duration,
                                    est_budget: task.est_budget,
                                    stats_req: task.stats_req,
                                    previous: task.previous,
                                    following: task.following,
                                    grid_x: task.grid_x,
                                    grid_y: task.grid_y,
                                },
                                {
                                    name: (node as TaskNode).data.name,
                                    description: (node as TaskNode).data.description,
                                    milestone: (node as TaskNode).data.milestone,
                                    est_workers: (node as TaskNode).data.est_workers,
                                    est_duration: (node as TaskNode).data.est_duration,
                                    est_budget: (node as TaskNode).data.est_budget,
                                    stats_req: (node as TaskNode).data.stats_req,
                                    previous: (node as TaskNode).data.previous,
                                    following: (node as TaskNode).data.following,
                                    grid_x: Math.round(node.position.x),
                                    grid_y: Math.round(node.position.y),
                                },
                            )
                        ) {
                            items.push({
                                action: 'update',
                                index: bIndex,
                                value: {
                                    ...(node as TaskNode).data,
                                    grid_x: Math.round(node.position.x),
                                    grid_y: Math.round(node.position.y),
                                },
                            });
                            bIndex += 1;
                        }
                    }
                }
                bulkResult = await CRMAPIManager.bulkRequest<TSimTask>(
                    items,
                    async (api, _bulkBody) => {
                        return await api.bulkSimTask(_bulkBody);
                    },
                    async (api, task_id) => {
                        return await api.bulkResultSimTask(task_id);
                    },
                );
                if (bulkResult.errorMessages) throw bulkResult.errorMessages;
            }
            if (
                state.simulation?.first_task != state.firstTaskId ||
                state.simulation?.last_task != state.lastTaskId ||
                state.proposeTotalBudget != 0 ||
                state.proposeWeeks != 0
            ) {
                const simUpdate = await CRMAPIManager.request<SimulationResp>(async (api) => {
                    return await api.updateSimulation({
                        ...state.simulation,
                        first_task:
                            firstTaskIndex == null
                                ? state.firstTaskId
                                : bulkResult.data.result.find((bri) => bri.index == firstTaskIndex)
                                      ?.value.id,
                        last_task:
                            lastTaskIndex == null
                                ? state.lastTaskId
                                : bulkResult.data.result.find((bri) => bri.index == lastTaskIndex)
                                      ?.value.id,
                        total_budget: state.simulation?.total_budget,
                        weeks: state.simulation?.weeks,
                    });
                });
                if (simUpdate.errorCode) throw simUpdate.errorMessages;
            }
            await loadParentSim();
            await loadTaskList();
            message.success('Изменения сохранены');
            returnValue = true;
        } catch (err) {
            messageApi.error('Ошибки сохранения, смотрите консоль');
            console.log(err);
        }
        state.isLoading = false;
        return returnValue;
    }

    function addNode() {
        const { domNode } = store.getState();
        const boundingRect = domNode?.getBoundingClientRect();

        if (boundingRect) {
            const center = screenToFlowPosition({
                x: boundingRect.x + boundingRect.width / 2,
                y: boundingRect.y + boundingRect.height / 2,
            });
            const newTask: TSimTask = {
                uid: '' + state.lastId,
                simulation_id: +simId,
                id: state.lastId,
                name: 'Новый узел #' + state.lastId,
                description: '',
                milestone: false,
                est_workers: 1,
                est_duration: 1,
                est_budget: 8 * 900,
                previous: [],
                following: [],
                stats_req: [
                    0,
                    0,
                    0,
                    0,
                    0,
                ],
                grid_x: center.x,
                grid_y: center.y,
                created_at: Common.dateNowString(),
                updated_at: null,
                deleted_at: null,
            };
            addNodes({
                id: '' + newTask.id,
                position: {
                    x: newTask.grid_x,
                    y: newTask.grid_y,
                },
                data: {
                    ...newTask,
                    allowLeft: true,
                    allowRight: true,
                    className: disableEdit() ? 'nopan' : '',
                },
                type: 'taskNode',
            });
            state.nodesChanged = true;
            state.lastId = state.lastId + 1;
        }
    }

    function getBudgetSum() {
        const taskSum = state.tasks?.reduce((acc, t) => acc + t.est_budget, 0);
        return (
            taskSum +
            state.simulation?.hardware_budget +
            state.simulation?.other_budget
        ).toLocaleString();
    }

    function makeTableItems() {
        return nodes.map((n) => {
            return { ...n.data, key: '' + n.data.id };
        });
    }

    function updateTableTask(record: Item) {
        setNodes((prev) =>
            prev.map((n) => {
                if (+n.id == record.id) {
                    const data = {
                        ...n.data,
                        name: record.name,
                        est_budget: record.est_budget,
                        est_duration: record.est_duration,
                        est_workers: record.est_workers,
                    };
                    if (selectedNode?.data.id == record.id) {
                        setSelectedNode({ ...n, data: data });
                    }
                    return { ...n, data: data };
                } else {
                    return n;
                }
            }),
        );
        state.nodesChanged = true;
    }

    function updateSelectedNode(data: TaskNode['data']) {
        setNodes((prev) =>
            prev.map((n) => {
                if (n.id == selectedNode.id) {
                    setSelectedNode({ ...n, data: data });
                    return { ...n, data: data };
                } else {
                    return n;
                }
            }),
        );
        state.nodesChanged = true;
    }

    function handleFirstTaskChange() {
        if (!selectedNode) return;
        const prevId = state.firstTaskId;
        if (prevId == selectedNode.data.id) {
            state.firstTaskId = null;
            setNodes((prev) =>
                prev.map((n) =>
                    n.data.id == prevId ? { ...n, data: { ...n.data, allowLeft: true } } : n,
                ),
            );
        } else {
            const prevSelNode = selectedNode;
            state.firstTaskId = selectedNode.data.id;
            setNodes((prev) =>
                prev.map((n) =>
                    n.data.id == prevId
                        ? { ...n, data: { ...n.data, allowLeft: true } }
                        : n.data.id == selectedNode.data.id
                          ? {
                                ...n,
                                data: {
                                    ...n.data,
                                    allowLeft: false,
                                    previous: [],
                                },
                            }
                          : prevSelNode.data.previous.includes(n.data.id)
                            ? {
                                  ...n,
                                  data: {
                                      ...n.data,
                                      following: n.data.following.filter(
                                          (dpi) => dpi != selectedNode.data.id,
                                      ),
                                  },
                              }
                            : n,
                ),
            );
            setEdges((prev) => prev.filter((e) => +e.target != selectedNode.data.id));
            setSelectedNode({ ...prevSelNode, data: { ...prevSelNode.data, previous: [] } });
        }
    }

    function handleLastTaskChange() {
        if (!selectedNode) return;
        const prevId = state.lastTaskId;
        if (prevId == selectedNode.data.id) {
            state.lastTaskId = null;
            setNodes((prev) =>
                prev.map((n) =>
                    n.data.id == prevId ? { ...n, data: { ...n.data, allowRight: true } } : n,
                ),
            );
        } else {
            const prevSelNode = selectedNode;
            state.lastTaskId = selectedNode.data.id;
            setNodes((prev) =>
                prev.map((n) =>
                    n.data.id == prevId
                        ? { ...n, data: { ...n.data, allowRight: true } }
                        : n.data.id == selectedNode.data.id
                          ? {
                                ...n,
                                data: {
                                    ...n.data,
                                    allowRight: false,
                                    following: [],
                                },
                            }
                          : prevSelNode.data.following.includes(n.data.id)
                            ? {
                                  ...n,
                                  data: {
                                      ...n.data,
                                      previous: n.data.previous.filter(
                                          (dpi) => dpi != selectedNode.data.id,
                                      ),
                                  },
                              }
                            : n,
                ),
            );
            setEdges((prev) => prev.filter((e) => +e.source != selectedNode.data.id));
            setSelectedNode({ ...prevSelNode, data: { ...prevSelNode.data, following: [] } });
        }
    }

    function handleNodesDelete(nodes: TaskNode[]) {
        for (let i = 0; i < nodes.length; i++) {
            deleteNode(nodes[i].data.id);
        }
    }

    function handleEdgesDelete(edges: Edge[]) {
        for (let i = 0; i < edges.length; i++) {
            deleteEdge(edges[i]);
        }
    }

    return (
        <UniLayout
            activeTab="graph"
            additionalClass="graph-min-width"
            tabSet="constructor"
            paddingNone={true}
        >
            <div className="graph-container">
                {contextHolder}
                {state.isLoading && <Loader />}
                <PreventLeaving
                    anyChanges={anyChanges}
                    onSave={saveChanges}
                />
                <ReactFlow
                    className="main-flow"
                    nodes={nodes}
                    edges={edges}
                    onNodesChange={onNodesChange}
                    onEdgesChange={onEdgesChange}
                    nodesDraggable={!disableEdit()}
                    nodesConnectable={!disableEdit()}
                    isValidConnection={isValidConnection}
                    onConnect={onConnect}
                    onReconnect={onReconnect}
                    onNodeClick={handleNodeClick}
                    onNodeDoubleClick={handleNodeDoubleClick}
                    onSelectionChange={onSelectionChange}
                    onEdgeClick={handleEdgeClick}
                    onNodesDelete={disableEdit() ? () => {} : handleNodesDelete}
                    onEdgesDelete={disableEdit() ? () => {} : handleEdgesDelete}
                    nodeTypes={nodeTypes}
                    defaultEdgeOptions={defaultEdgeOptions}
                    connectionLineType={ConnectionLineType.SmoothStep}
                    connectionLineStyle={defaultEdgeOptions.style}
                    deleteKeyCode={disableEdit() ? null : ['Backspace', 'Delete']}
                    selectionKeyCode={null}
                    multiSelectionKeyCode={null}
                    fitView={true}
                    defaultViewport={{ x: 0, y: 0, zoom: 0.7 }}
                    minZoom={0.1}
                    maxZoom={1.5}
                    proOptions={{ hideAttribution: true }}
                    onlyRenderVisibleElements={true}
                    elevateEdgesOnSelect={false}
                >
                    <Background
                        gap={16}
                        size={1}
                        color={Colors.Black[950]}
                    />
                </ReactFlow>
                <GraphLowerMenu
                    tableIsOpen={state.tableIsOpen}
                    flipTableOpen={() => (state.tableIsOpen = !state.tableIsOpen)}
                    saveChanges={disableEdit() || state.isLoading ? null : saveChanges}
                    addTableTask={disableEdit() || state.isLoading ? null : addNode}
                    shuffleHorizontal={
                        disableEdit() || state.isLoading ? null : () => onLayout('LR')
                    }
                    shuffleVertical={disableEdit() || state.isLoading ? null : () => onLayout('TB')}
                    fitView={handleFitView}
                    zoomIn={handleZoomIn}
                    zoomOut={handleZoomOut}
                />
                <NodeInfoDrawer
                    simulation={state.simulation}
                    firstTaskId={state.firstTaskId}
                    setFirstTaskId={handleFirstTaskChange}
                    lastTaskId={state.lastTaskId}
                    setLastTaskId={handleLastTaskChange}
                    selectedNode={selectedNode}
                    updateSelectedNodeData={updateSelectedNode}
                    deleteSelectedNode={() => deleteNode(selectedNode?.data.id)}
                    setSelectedNodeById={setSelectedNodeById}
                    isOpen={isSidebarOpen}
                    onClose={async () => {
                        setIsSidebarOpen(false);
                        setTimeout(() => {
                            setSelectedNode(null);
                        }, 400);
                    }}
                    nodes={nodes}
                    edges={edges}
                />
                <NodeTableDrawer
                    isOpen={state.tableIsOpen}
                    setIsOpen={(v) => (state.tableIsOpen = v)}
                    addTableTask={addNode}
                    deleteTask={deleteNode}
                    updateTableTask={updateTableTask}
                    dataSource={makeTableItems()}
                    simulation={state.simulation}
                    budgetSum={getBudgetSum()}
                />
                <Modal
                    cancelButtonProps={{
                        className: 'p2',
                    }}
                    cancelText="Отмена"
                    className="conflict-modal conflict-budget"
                    closable={false}
                    okButtonProps={{
                        className: 'p2',
                    }}
                    okText="Исправить"
                    onCancel={() => {
                        state.conflictTotalBudget = false;
                    }}
                    onOk={() => {
                        state.simulation.total_budget = state.proposeTotalBudget;
                        state.conflictTotalBudget = false;
                        saveChanges();
                    }}
                    open={state.conflictTotalBudget}
                    title={<h4>Конфликт бюджета</h4>}
                >
                    <Col>
                        <Row className="header-row">
                            <h6>Сумма бюджетов узлов, оборудования и прочего больше заданной</h6>
                        </Row>
                        <Row className="body-row">
                            <Col>
                                <Row>
                                    <Col className="p3-strong">Общий бюджет симуляции:</Col>
                                    <Col className="p3 conflict-value">
                                        {state.simulation?.total_budget / 1000} тыс.
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="p3-strong">
                                        Сумма бюджетов (после изменений):
                                    </Col>
                                    <Col className="p3">
                                        {(state.calculatedBudget / 1000).toFixed(1)} тыс.
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="p3-strong">Предлагаемое исправление:</Col>
                                    <Col className="p3">{state.proposeTotalBudget / 1000} тыс.</Col>
                                </Row>
                            </Col>
                        </Row>
                    </Col>
                </Modal>
                <Modal
                    cancelButtonProps={{
                        className: 'p2',
                    }}
                    cancelText="Отмена"
                    className="conflict-modal conflict-weeks"
                    closable={false}
                    okButtonProps={{
                        className: 'p2',
                    }}
                    okText="Исправить"
                    onCancel={() => {
                        state.conflictWeeks = false;
                    }}
                    onOk={() => {
                        state.simulation.weeks = state.proposeWeeks;
                        state.conflictWeeks = false;
                        saveChanges();
                    }}
                    open={state.conflictWeeks}
                    title={<h4>Конфликт длительности</h4>}
                >
                    <Col>
                        <Row className="header-row">
                            <h6>Конец последнего узла позже крайней недели</h6>
                        </Row>
                        <Row className="body-row">
                            <Col>
                                <Row>
                                    <Col className="p3-strong">Недель в симуляции:</Col>
                                    <Col className="p3 conflict-value">
                                        {state.simulation?.weeks} нед.
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="p3-strong">
                                        Конец последнего узла (после изменений):
                                    </Col>
                                    <Col className="p3">
                                        {`${Math.floor(state.calculatedIngameDays / 5) + 1} н ${(state.calculatedIngameDays % 5) + 1} д`}
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="p3-strong">Предлагаемое исправление:</Col>
                                    <Col className="p3">{state.proposeWeeks} нед.</Col>
                                </Row>
                            </Col>
                        </Row>
                    </Col>
                </Modal>
            </div>
        </UniLayout>
    );
};

const ConstructorGraph = (): JSX.Element => {
    return (
        <ReactFlowProvider>
            <ConstructorGraphContent />
        </ReactFlowProvider>
    );
};

export default ConstructorGraph;
