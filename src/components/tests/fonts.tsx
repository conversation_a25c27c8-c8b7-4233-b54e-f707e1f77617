import Colors from 'src/shared/colors';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { Col, Row } from 'antd';

const FontsTest = (): JSX.Element => {
    const txt = 'Сущность кайдзен очень проста: совершенствование.';

    return (
        <UniLayout
            activeTab="fonts"
            tabSet="dev"
        >
            <div
                style={{
                    background: Colors.Accent.warm[0],
                    border: `2px solid ${Colors.Neutral[100]}`,
                    borderRadius: '4px',
                    width: '100%',
                }}
            >
                <Row
                    style={{
                        columnGap: '24px',
                        padding: '40px 40px 40px 38px',
                        width: '100%',
                    }}
                >
                    <Col>
                        <h1>Heading 1</h1>
                        <h2>Heading 2</h2>
                        <h3>Heading 3</h3>
                        <h4>Heading 4</h4>
                        <h5>Heading 5</h5>
                        <h6>Heading 6</h6>
                    </Col>
                    <Col>
                        <p className="p1">P1: {txt}</p>
                        <p className="p1-strong">P1 Bold: {txt}</p>
                        <p className="p2">P2: {txt}</p>
                        <p className="p2-strong">P2 Bold: {txt}</p>
                        <p className="p3">P3: {txt}</p>
                        <p className="p3-strong">P3 Bold: {txt}</p>
                    </Col>
                    <Col>
                        <p className="desc-l">Description L</p>
                        <p className="desc-l-strong">Description L-Strong</p>
                        <p className="desc-m">Description M</p>
                        <p className="desc-m-strong">Description M-Strong</p>
                        <p className="desc-s">Description S</p>
                        <p className="desc-s-strong">Description S-Strong</p>
                    </Col>
                </Row>
            </div>
        </UniLayout>
    );
};

export default FontsTest;
