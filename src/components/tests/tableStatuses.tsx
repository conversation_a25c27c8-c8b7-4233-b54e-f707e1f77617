import { GlobalConstants } from 'src/shared/constants';
import { useReactive } from 'ahooks';
import dayjs from 'dayjs';
import { useContext, useEffect, useRef, useState } from 'react';
import {
    <PERSON>ton,
    Col,
    ColorPicker,
    ColorPickerProps,
    Divider,
    Form,
    GetRef,
    Input,
    InputRef,
    Row,
    Table,
    TableProps,
} from 'antd';
import { prepareGrid } from './colors';
import React from 'react';
import { Common } from 'src/shared/common';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import UniLayout from '@components/ui/uniLayout/uniLayout';

import './tests.scss';

type TempTableStatus = {
    key: string;
    defaultColor: string;
    defaultText: string;
    newColor: string;
    newText: string;
};

type FormInstance<T> = GetRef<typeof Form<T>>;

const EditableContext = React.createContext<FormInstance<any> | null>(null);

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form
            form={form}
            component={false}
        >
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    dataIndex: keyof TempTableStatus;
    record: TempTableStatus;
    handleSave: (record: TempTableStatus) => void;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}) => {
    const [editing, setEditing] = useState(false);
    const inputRef = useRef<InputRef>(null);
    const form = useContext(EditableContext)!;

    useEffect(() => {
        if (editing) {
            inputRef?.current?.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({ [dataIndex]: record[dataIndex] });
    };

    const save = async () => {
        try {
            const values = await form.validateFields();

            toggleEdit();
            handleSave({ ...record, ...values });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };

    const customPanelRender: ColorPickerProps['panelRender'] = (
        _,
        { components: { Picker, Presets } },
    ) => (
        <Row
            justify="space-between"
            wrap={false}
        >
            <Col span={12}>
                <Presets />
            </Col>
            <Divider
                type="vertical"
                style={{ height: 'auto' }}
            />
            <Col flex="auto">
                <Picker />
            </Col>
        </Row>
    );

    let childNode = children;

    if (editable) {
        childNode = editing ? (
            <Form.Item
                style={{ margin: 0 }}
                name={dataIndex}
                rules={[
                    {
                        required: true,
                        message: `${title} обязательное поле.`,
                    },
                ]}
            >
                {dataIndex == 'newText' && (
                    <Input
                        ref={inputRef}
                        maxLength={20}
                        onPressEnter={save}
                        onBlur={save}
                        placeholder="Введите название"
                        required
                    />
                )}
                {dataIndex == 'newColor' && (
                    <ColorPicker
                        disabledAlpha
                        format="hex"
                        onOpenChange={(o) => {
                            if (!o) save();
                        }}
                        panelRender={customPanelRender}
                        presets={prepareGrid().map((g) => {
                            return {
                                colors: g.colors.map((gc) => gc.hex),
                                defaultOpen: false,
                                key: g.group,
                                label: g.group,
                            };
                        })}
                        showText
                        styles={{ popupOverlayInner: { width: 480 } }}
                    />
                )}
            </Form.Item>
        ) : (
            <div
                className="editable-cell-value-wrap"
                style={{ paddingInlineEnd: 4 }}
                onClick={toggleEdit}
            >
                {children}
            </div>
        );
    }

    return <td {...restProps}>{childNode}</td>;
};

type ColumnTypes = Exclude<TableProps<TempTableStatus>['columns'], undefined>;

type TState = {
    tempTableStatuses: { [key: string]: TempTableStatus };
};

const TableStatusesTest = (): JSX.Element => {
    const state = useReactive<TState>({
        tempTableStatuses: {},
    });
    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };
    const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
        {
            title: 'Ключ',
            dataIndex: 'key',
            render: (value, record) => (
                <span className="p3">
                    {`${value}${
                        record.defaultColor != record.newColor ||
                        record.defaultText != record.newText
                            ? ' (изменено)'
                            : ''
                    }`}
                </span>
            ),
            width: 150,
        },
        {
            title: 'Оригинальный текст',
            dataIndex: 'defaultText',
            render: (value) => <span className="p3">{value}</span>,
            width: 200,
        },
        {
            title: 'Оригинальный цвет',
            dataIndex: 'defaultColor',
            render: (value, record) => (
                <FilterButton
                    emptyText="Текст"
                    hex={value}
                    text={record.defaultText}
                />
            ),
            width: 200,
        },
        {
            title: 'Новый текст',
            dataIndex: 'newText',
            editable: true,
            render: (value) =>
                Common.isNullOrEmptyString(value) ? (
                    <span className="p3 no-filter-name">Введите название</span>
                ) : (
                    <span className="p3">{value}</span>
                ),
            width: 200,
        },
        {
            title: 'Новый цвет',
            dataIndex: 'newColor',
            editable: true,
            render: (value, record) => {
                if (Common.isNullOrEmptyString(value)) {
                    return <span className="p3 no-filter-name">Выберите цвет</span>;
                } else {
                    const pureHex = typeof value == 'string' ? value.slice(1) : value.toHex();
                    return (
                        <FilterButton
                            emptyText="Текст"
                            hex={`#${pureHex}`}
                            text={record.newText}
                        />
                    );
                }
            },
            width: 200,
        },
    ];

    function updateTableStatus(record: TempTableStatus) {
        state.tempTableStatuses[record.key] = record;
    }

    const columns = defaultColumns.map((col) => {
        if (!col.editable) {
            return col;
        }
        return {
            ...col,
            onCell: (record: TempTableStatus) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: updateTableStatus,
            }),
        };
    });

    useEffect(() => {
        const originalTS = GlobalConstants.TableStatuses;
        const tempTS: typeof state.tempTableStatuses = {};
        for (let i = 0; i < Object.keys(originalTS).length; i++) {
            const tempKey = Object.keys(originalTS)[i];
            const tempValue = originalTS[tempKey];
            tempTS[tempKey] = {
                key: tempKey,
                defaultColor: tempValue.color,
                defaultText: tempValue.text,
                newColor: tempValue.color,
                newText: tempValue.text,
            };
        }
        state.tempTableStatuses = tempTS;
    }, []);

    function prepareCSVData() {
        const csvData: string[][] = [
            [
                '№',
                'Ключ',
                'Изменено?',
                'Оригинальный цвет',
                'Оригинальный текст',
                'Новый цвет',
                'Новый текст',
            ],
        ];
        for (let i = 0; i < Object.keys(state.tempTableStatuses).length; i++) {
            const tempKey = Object.keys(state.tempTableStatuses)[i];
            const tempValue = state.tempTableStatuses[tempKey];
            csvData.push([
                `${i + 1}`,
                tempValue.key,
                `${
                    tempValue.defaultColor != tempValue.newColor ||
                    tempValue.defaultText != tempValue.defaultText
                        ? 'Да'
                        : 'Нет'
                }`,
                tempValue.defaultColor,
                tempValue.defaultText,
                typeof tempValue.newColor == 'string'
                    ? tempValue.newColor
                    : `#${((tempValue.newColor as any).toHex() as string).toUpperCase()}`,
                tempValue.newText,
            ]);
        }
        return csvData;
    }

    function exportTableStatuses() {
        const rows = prepareCSVData();
        const csvContent = '\ufeff' + rows.map((e) => e.join(';')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        const a = document.createElement('a');
        a.style.display = 'none';
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        a.download = `Табличные статусы, ${dayjs.utc().tz(timezone).format('HH-mm-ss DD.MM.YYYY')}.csv`;
        a.href = URL.createObjectURL(blob);
        a.addEventListener('click', () => {
            setTimeout(() => URL.revokeObjectURL(a.href), 30 * 1000);
        });
        a.click();
        a.remove();
        URL.revokeObjectURL(a.href);
    }

    return (
        <UniLayout
            activeTab="table-statuses"
            tabSet="dev"
        >
            <div className="table-statuses-profile">
                <Row className="table-statuses-card">
                    <Col>
                        <Row className="header-row">
                            <Col>
                                <h4>
                                    Табличные статусы ({Object.keys(state.tempTableStatuses).length}{' '}
                                    шт.):
                                </h4>
                            </Col>
                        </Row>
                        <Row className="body-row">
                            <Col>
                                <Row className="table-actions">
                                    <Col>
                                        <Button
                                            className="export-btn"
                                            icon={<div className="table-icon" />}
                                            onClick={exportTableStatuses}
                                        >
                                            Экспорт
                                        </Button>
                                    </Col>
                                </Row>
                                <Row className="table-container">
                                    <Table<TempTableStatus>
                                        bordered
                                        columns={columns as ColumnTypes}
                                        components={components}
                                        dataSource={Object.values(state.tempTableStatuses)}
                                        locale={{
                                            emptyText: (
                                                <Col
                                                    className="empty-text p3"
                                                    flex={1}
                                                >
                                                    <Row>Табличных статусов нет :)</Row>
                                                </Col>
                                            ),
                                        }}
                                        pagination={false}
                                        rowClassName={() => 'editable-row'}
                                        scroll={{
                                            x: 'max-content',
                                            y: 77 * 7,
                                        }}
                                    />
                                </Row>
                            </Col>
                        </Row>
                    </Col>
                </Row>
            </div>
        </UniLayout>
    );
};

export default TableStatusesTest;
