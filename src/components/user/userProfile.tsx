import { CRMAPIManager } from '@api/crmApiManager';
import { UserResp } from '@api/responseModels/users/userResp';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TUser } from 'types/user/user';
import {
    Button,
    Col,
    Collapse,
    Divider,
    Dropdown,
    Input,
    message,
    Popconfirm,
    Row,
    Tooltip,
} from 'antd';
import { SettingsManager } from 'src/shared/settingsManager';
import { Loader } from '@components/ui/loader/loader';
import { Permissions } from 'src/shared/permissions';
import { rootStore } from 'src/store/instanse';
import { TFilter } from 'types/filter';
import { TRole } from 'types/user/role';
import _ from 'lodash';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { LoginResp } from '@api/responseModels/security/loginResp';
import { Common } from 'src/shared/common';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { FilterDrawer } from '@components/drawers/filterDrawer';
import { TMetadata } from 'types/api/metadata';
import { RoleListResp } from '@api/responseModels/roles/roleListResponse';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { FilterResp } from '@api/responseModels/filters/filterResponse';
import { TPermissionCategory } from 'types/user/permissions';
import { PermissionCategoryListResp } from '@api/responseModels/permissions/permissionCategoryListResponse';
import { PermissionModal } from '@components/permissionModal/permissionModal';
import TextArea from 'antd/es/input/TextArea';
import { ChatResp } from '@api/responseModels/chat/chatResponse';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';

import './userProfile.scss';

type TState = {
    filterPickerOpen: boolean;
    filters: TFilter[];
    filtersMeta: TMetadata | null;
    isLoading: boolean;
    mode: 'edit' | 'security' | 'view';
    originalUser: TUser | null;
    passwordConfirmInputValue: string;
    passwordInputValue: string;
    permissionCategories: TPermissionCategory[];
    permissionModalOpen: boolean;
    roles: TRole[];
    user: TUser | null;
};

const UserProfile = (): JSX.Element => {
    const state = useReactive<TState>({
        filterPickerOpen: false,
        filters: [],
        filtersMeta: null,
        isLoading: false,
        mode: 'view',
        originalUser: null,
        passwordConfirmInputValue: '',
        passwordInputValue: '',
        permissionCategories: [],
        permissionModalOpen: false,
        roles: [],
        user: null,
    });
    const navigate = useNavigate();
    const { userId } = useParams();
    const [messageApi, contextHolder] = message.useMessage();
    const creds = SettingsManager.getConnectionCredentials();
    const curUser = rootStore.currentUserStore.getUser;
    // https://stackoverflow.com/questions/28555114/regexp-for-login
    const loginMask = /^(?=.*[A-Za-z0-9]$)[A-Za-z][A-Za-z\d]{2,15}$/;
    // https://stackoverflow.com/questions/46155/how-can-i-validate-an-email-address-in-javascript
    const emailMask = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    // https://stackoverflow.com/questions/12090077/javascript-regular-expression-password-validation-having-special-characters
    const passwordMask = /^(?=.*[0-9])[a-zA-Z0-9]{8,24}$/;
    const nameMask = /^(?=.*[A-Za-zа-яА-ЯёЁ\d]$)[A-Za-zа-яА-ЯёЁ][A-Za-zа-яА-ЯёЁ\d\s-]{2,32}$/;
    const anyChanges = !_.isEqual(state.originalUser, state.user);
    const allowEdit =
        Permissions.checkPermission(Permissions.UserUpdate) &&
        (state.user?.role != 'Roles.Admin' || state.user?.id == creds?.user_id) &&
        (window.location.pathname == '/lk' ||
            curUser.role == 'Roles.Admin' ||
            curUser.role == 'Roles.Manager');
    const allowFilterChange =
        Permissions.checkPermission(Permissions.UserUpdate) &&
        (state.user?.role != 'Roles.Admin' || curUser.role == 'Roles.Admin') &&
        (curUser.role == 'Roles.Admin' || curUser.role == 'Roles.Manager');
    const allowChangeRole = () =>
        Permissions.checkPermission(Permissions.UserChangeRole) &&
        state.user?.role != 'Roles.Admin' &&
        state.user?.is_verified == true &&
        state.user?.id != rootStore.currentUserStore.getUser.id;
    const allowSecurity = window.location.pathname == '/lk';
    const allowIdLoginEmail = curUser.role == 'Roles.Admin' || curUser.role == 'Roles.Manager';
    const allowBan =
        Permissions.checkPermission(Permissions.UserBan) &&
        state.user?.role != 'Roles.Admin' &&
        state.user?.id != creds.user_id &&
        state.user?.is_banned == false;
    const allowUnban =
        Permissions.checkPermission(Permissions.UserUnban) &&
        state.user?.id != creds.user_id &&
        state.user?.is_banned == true;
    const allowDelete =
        Permissions.checkPermission(Permissions.UserDelete) &&
        state.user?.role != 'Roles.Admin' &&
        state.user?.id != creds.user_id &&
        state.user?.deleted_at == null;
    const allowRestore =
        Permissions.checkPermission(Permissions.UserRestore) &&
        state.user?.id != creds.user_id &&
        state.user?.deleted_at != null;
    const allowNewChat =
        Permissions.checkPermission(Permissions.ChatCreate) &&
        state.user?.deleted_at == null &&
        state.user?.id != creds.user_id;
    const allowChatWithManager =
        Permissions.checkPermission(Permissions.ChatWithManager) &&
        state.user?.id == creds.user_id &&
        state.user?.role == 'Roles.Client';
    const allowUserInvitations =
        Permissions.checkPermission(Permissions.InvitationList) &&
        Permissions.checkUserPermission(state.user, Permissions.InvitationList);
    const allowUserSimulations =
        Permissions.checkPermission(Permissions.SimulationList) &&
        Permissions.checkUserPermission(state.user, Permissions.SimulationCreate);
    const allowUserSessions =
        Permissions.checkPermission(Permissions.SessionList) &&
        Permissions.checkUserPermission(state.user, Permissions.SessionCreate);
    const allowUserAssignments =
        Permissions.checkPermission(Permissions.SessionAssignmentList) &&
        Permissions.checkUserPermission(state.user, Permissions.SessionAssignmentList);
    const allowViewPermissions =
        Permissions.checkPermission(Permissions.PermissionCategoryGet) &&
        Permissions.checkPermission(Permissions.PermissionList);

    async function loadUser() {
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<UserResp>(async (api) => {
                if (userId == undefined || userId == creds.user_id) {
                    return api.currentUser();
                } else {
                    return api.getUser(userId);
                }
            });
            if (result.errorCode) throw result;
            state.user = result.data.data;
            state.originalUser = { ...state.user };
        } catch (err) {
            if (err?.message?.includes('404')) {
                navigate('/lk');
                messageApi.error('Пользователя не существует или вам нельзя его просматривать');
            } else {
                messageApi.error('Ошибка загрузки пользователя');
            }
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadRoles() {
        state.isLoading = true;
        try {
            const roles = await CRMAPIManager.request<RoleListResp>(async (api) => {
                return await api.getRoleList();
            });
            if (roles.errorMessages) throw roles.errorMessages;
            state.roles = roles.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка ролей');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadFilters() {
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.request<FilterListResp>(async (api) => {
                return await api.getFilterList({
                    query: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data;
            state.filtersMeta = filters.data.meta;
            state.user = {
                ...state.user,
                filters: [...state.user.filters].map((uf) => {
                    const fullFilter = [...state.filters].find((f) => f.id == uf.id);
                    if (fullFilter != undefined) {
                        return fullFilter;
                    } else {
                        return { ...uf, target: 'users' };
                    }
                }),
            };
            state.originalUser = { ...state.user };
        } catch (errors) {
            messageApi.error('Ошибка при получении списка фильтров');
        }
        state.isLoading = false;
    }

    function updStateUser(user: TUser) {
        state.user = allowFilterChange
            ? {
                  ...user,
                  filters: [...user.filters].map((uf) => {
                      return { ...uf, target: 'users' };
                  }),
              }
            : { ...user };
        state.originalUser = { ...state.user };
    }

    async function loadPermissionCategories() {
        state.isLoading = true;
        try {
            const permCatList = await CRMAPIManager.request<PermissionCategoryListResp>(
                async (api) => {
                    return await api.getPermissionCategoryList({
                        category_name: null,
                        page: 1,
                        per_page: 100,
                        sort_by: null,
                        sort_direction: null,
                    });
                },
            );
            if (permCatList.errorMessages) throw permCatList.errorMessages;
            state.permissionCategories = permCatList.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка категорий прав');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadNecessary() {
        state.mode = 'view';
        if (!Permissions.checkPermission(Permissions.UserGet) && userId == undefined) {
            navigate('/lk');
            message.warning('Вам запрещено просматривать профили других пользователей');
            return;
        }
        state.originalUser = null;
        await loadUser();
        if (state.originalUser == null) {
            return;
        }
        if (allowChangeRole()) {
            await loadRoles();
        }
        if (allowFilterChange) {
            await loadFilters();
        }
        if (allowViewPermissions) {
            await loadPermissionCategories();
        }
    }

    useEffect(() => {
        if (
            window.location.pathname != '/lk' &&
            (userId == undefined || userId == creds?.user_id)
        ) {
            navigate('/lk');
        }
        loadNecessary();
    }, [userId]);

    function setEditing() {
        if (!allowEdit) {
            messageApi.warning('Редактирование запрещено');
            return;
        }
        state.mode = 'edit';
    }

    function setSecurity() {
        if (!allowSecurity) {
            messageApi.warning('Управление учётной записью запрещено');
            return;
        }
        state.mode = 'security';
    }

    function toUserInvitations() {
        if (!allowUserInvitations) {
            messageApi.warning('Переход к приглашениям пользователя запрещён');
            return;
        }
        navigate(`/management/invitations?sender_id=${state.originalUser?.id}`);
    }

    function toUserSimulations() {
        if (!allowUserSimulations) {
            messageApi.warning('Переход к симуляциям пользователя запрещён');
            return;
        }
        navigate(`/simulations/all?creator_id=${state.originalUser?.id}`);
    }

    function toUserSessions() {
        if (!allowUserSessions) {
            messageApi.warning('Переход к сессиям пользователя запрещён');
            return;
        }
        navigate(`/controls/sessions?manager_id=${state.originalUser?.id}`);
    }

    function toUserAssignments() {
        if (!allowUserAssignments) {
            messageApi.warning('Переход к назначениям пользователя запрещён');
            return;
        }
        navigate(`/controls/assignments?user_id=${state.originalUser?.id}`);
    }

    async function banUser() {
        if (!allowBan) {
            messageApi.warning('Бан пользователя запрещён');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.banUser(state.originalUser?.id);
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            if (state.user?.id == creds.user_id) {
                rootStore.currentUserStore.setUser({ ...state.user });
            }
            messageApi.success('Пользователь забанен');
        } catch (errors) {
            messageApi.error('Ошибка при блокировке пользователя');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function unbanUser() {
        if (!allowUnban) {
            messageApi.warning('Разбан пользователя запрещён');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.unbanUser(state.originalUser?.id);
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            if (state.user?.id == creds.user_id) {
                rootStore.currentUserStore.setUser({ ...state.user });
            }
            messageApi.success('Пользователь разблокирован');
        } catch (errors) {
            messageApi.error('Ошибка при разблокировке пользователя');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function deleteUser() {
        if (!allowDelete) {
            messageApi.warning('Удаление пользователя запрещено');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.deleteUser(state.originalUser?.id);
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            if (state.user?.id == creds.user_id) {
                rootStore.currentUserStore.setUser({ ...state.user });
            }
            messageApi.success('Пользователь удалён');
        } catch (errors) {
            messageApi.error('Ошибка при удалении пользователя');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function restoreUser() {
        if (!allowRestore) {
            messageApi.warning('Восстановление пользователя запрещено');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.restoreUser(state.originalUser?.id);
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            if (state.user?.id == creds.user_id) {
                rootStore.currentUserStore.setUser({ ...state.user });
            }
            messageApi.success('Пользователь восстановлен');
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении пользователя');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function updateUser(u: Partial<TUser>) {
        state.user = { ...state.user, ...u };
    }

    function validateChanges() {
        if (!nameMask.test(state.user?.name)) {
            messageApi.warning('Некорректное имя');
            return false;
        }

        return true;
    }

    async function saveChanges() {
        if (!allowEdit) {
            messageApi.warning('Редактирование пользователя запрещено');
            return false;
        }
        if (!validateChanges()) return false;
        state.isLoading = true;
        let returnValue = false;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.updateUser({
                    ...state.originalUser,
                    name: state.user?.name,
                    picture: state.user?.picture,
                    description: state.user?.description,
                    filters: [...state.user?.filters],
                });
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            if (state.user?.id == creds.user_id) {
                rootStore.currentUserStore.setUser({ ...state.user });
            }
            state.mode = 'view';
            message.success('Изменения сохранены');
            returnValue = true;
        } catch (errors) {
            messageApi.error('Ошибка при сохранении изменений');
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    async function saveFilterChanges() {
        if (!allowFilterChange) {
            messageApi.warning('Обновление фильтров пользователя запрещено');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.updateUser({
                    ...state.originalUser,
                    filters: [...state.user?.filters],
                });
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            if (state.user?.id == creds.user_id) {
                rootStore.currentUserStore.setUser({ ...state.user });
            }
            state.filterPickerOpen = false;
            messageApi.success('Фильтры пользователя обновлены');
        } catch (errors) {
            messageApi.error('Ошибка при обновлении фильтров пользователя');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function cancelEditing() {
        state.user = { ...state.originalUser };
        state.mode = 'view';
    }

    function cancelSecurity() {
        state.user = { ...state.originalUser };
        state.mode = 'view';
    }

    async function chatWithUser() {
        if (!allowNewChat) {
            messageApi.warning('Чат с пользователем не разрешён');
            return;
        }
        state.isLoading = true;
        try {
            const result = await rootStore.socketStore.findOrCreatePrivateChat(
                rootStore.currentUserStore.getUser,
                state.user,
            );
            if (result.success) {
                navigate(`/chats/${result.chat_id}`);
            }
        } catch (errors) {
            messageApi.error('Ошибка при поиске/создании чата с пользователем');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function changeLogin() {
        if (!allowSecurity) {
            messageApi.warning('Смена логина запрещена');
            return;
        }
        if (
            !(
                loginMask.test(state.user?.login) &&
                state.originalUser?.login.localeCompare(state.user?.login) != 0
            )
        ) {
            messageApi.warning('Введён некорректный логин');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.updateUser({
                    ...state.originalUser,
                    login: state.user?.login,
                });
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            rootStore.currentUserStore.setUser({ ...state.user });
            messageApi.success('Логин изменён');
        } catch (errors) {
            messageApi.error('Ошибка при смене логина');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function changeEmail() {
        if (!allowSecurity) {
            messageApi.warning('Смена email запрещена');
            return;
        }
        if (
            !(
                emailMask.test(state.user?.email) &&
                state.originalUser?.email.localeCompare(state.user?.email) != 0 &&
                state.user?.email.length >= 5 &&
                state.user?.email.length <= 40
            )
        ) {
            messageApi.warning('Введён некорректный email');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.updateUser({
                    ...state.originalUser,
                    email: state.user?.email,
                });
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            rootStore.currentUserStore.setUser({ ...state.user });
            messageApi.success('Email изменён');
        } catch (errors) {
            messageApi.error('Ошибка при смене email');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function changePassword() {
        if (!allowSecurity) {
            messageApi.warning('Смена пароля запрещена');
            return;
        }
        if (
            !(
                passwordMask.test(state.passwordInputValue) &&
                passwordMask.test(state.passwordConfirmInputValue) &&
                state.passwordInputValue.localeCompare(state.passwordConfirmInputValue) == 0
            )
        ) {
            messageApi.warning('Введены некорректные пароли');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.updateUser({
                    ...state.originalUser,
                    password: state.passwordInputValue,
                });
            });
            if (user.errorMessages) throw user.errorMessages;
            navigate('/logout');
            messageApi.success('Пароль обновлён. Пожалуйста, перезайдите');
        } catch (errors) {
            messageApi.error('Ошибка при смене пароля');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function refreshToken() {
        if (!allowSecurity) {
            messageApi.warning('Завершение других сессий запрещено');
            return;
        }
        state.isLoading = true;
        try {
            const refresh = await CRMAPIManager.request<any>(async (api) => {
                return await api.refresh(creds?.refreshToken);
            });
            if (refresh.errorMessages) throw refresh.errorMessages;
            SettingsManager.updateConnectionCredentials({
                accessToken: refresh.data.access_token,
                refreshToken: refresh.data.refresh_token,
            });
            messageApi.success('Другие сессии завершены');
        } catch (errors) {
            console.log(errors);
            if (errors?.message?.includes('400')) {
                navigate('/logout');
                messageApi.error('Ваш токен устарел. Пожалуйста, перезайдите');
            } else {
                messageApi.error('Ошибка при завершении других сессий');
            }
        }
        state.isLoading = false;
    }

    async function onFilterAdd(filter: TFilter) {
        if (!Permissions.checkPermission(Permissions.FilterCreate)) {
            messageApi.error('Запрещено создание фильтров');
            return;
        }
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<FilterResp>(async (api) => {
                return await api.createFilter(filter);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadFilters();
        } catch (errors) {
            messageApi.error('Ошибка при создании фильтра');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function updateUserTempFilters(filter: TFilter) {
        if (state.user?.filters.find((f) => f.id == filter.id)) {
            state.user.filters = [...state.user.filters].filter((f) => f.id != filter.id);
        } else {
            state.user.filters = [...state.user.filters, filter];
        }
    }

    function setFilterPickerOpen(isOpen: boolean) {
        if (isOpen) {
            state.filterPickerOpen = true;
        } else {
            state.user.filters = [...state.originalUser?.filters];
            state.filterPickerOpen = false;
        }
    }

    async function changeUserRole(role: TUser['role']) {
        if (!allowChangeRole()) {
            messageApi.error('Запрещена смена роли');
            return;
        }
        state.isLoading = true;
        try {
            const user = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.changeUserRole(state.originalUser?.id, role);
            });
            if (user.errorMessages) throw user.errorMessages;
            updStateUser(user.data.data);
            messageApi.success('Роль изменена');
        } catch (errors) {
            messageApi.error('Ошибка при смене роли');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function setPermissionModalOpen(isOpen: boolean) {
        state.permissionModalOpen = isOpen;
    }

    async function chatWithManager() {
        if (!allowChatWithManager) {
            messageApi.warning('Чат с менеджером не разрешён');
            return;
        }
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.chatWithManager();
            });
            if (result.errorMessages) throw result.errorMessages;
            if (Common.isNullOrUndefined(result.data.data.id)) {
                messageApi.error('Не удалось найти/создать чат с менеджером');
            } else {
                navigate(`/chats/${result.data.data.id}`);
                message.success('Чат с менеджером найден/создан');
            }
        } catch (errors) {
            messageApi.error('Ошибка при поиске/создании чата с менеджером');
            console.log(errors);
        }
        state.isLoading = false;
    }

    return (
        <UniLayout
            activeTab="users"
            additionalClass="profile-min-width"
            tabSet="management"
            topBarText={state.user?.id == creds?.user_id ? 'Ваш профиль' : null}
        >
            <div className="user-profile">
                {state.isLoading && <Loader />}
                {contextHolder}
                <PreventLeaving
                    anyChanges={anyChanges}
                    onSave={saveChanges}
                />
                {state.filtersMeta != null && (
                    <FilterDrawer
                        filterList={state.filters.filter((f) => !f.is_protected)}
                        filterListMeta={state.filtersMeta}
                        fixedTarget="users"
                        isOpen={state.filterPickerOpen}
                        onConfirm={saveFilterChanges}
                        onFilterAdd={
                            Permissions.checkPermission(Permissions.FilterCreate)
                                ? onFilterAdd
                                : undefined
                        }
                        onSelect={updateUserTempFilters}
                        selected={state.user?.filters.map((f) => f.id)}
                        setIsOpen={setFilterPickerOpen}
                    />
                )}
                <PermissionModal
                    isOpen={state.permissionModalOpen}
                    permissionCategories={allowViewPermissions ? state.permissionCategories : null}
                    permissions={state.user?.permissions}
                    role={state.user?.role}
                    setIsOpen={setPermissionModalOpen}
                />
                {state.mode == 'view' && (
                    <Col
                        flex={1}
                        className="user-card"
                    >
                        <Row className="body-row">
                            <Col>
                                <Row className="avatar-split-row">
                                    <Col className="avatar-col">
                                        <div
                                            className={
                                                state.user?.role == 'Roles.Client'
                                                    ? 'handsome-client'
                                                    : 'handsome-staff'
                                            }
                                        />
                                    </Col>
                                    <Col className="info-col">
                                        <Row className="name-row">
                                            <Col>
                                                <h3>{state.user?.name}</h3>
                                            </Col>
                                            <Col>
                                                <CopyButton
                                                    textToCopy={`Пользователь с именем ${state.user?.name}`}
                                                    textToShow="Имя пользователя скопирован"
                                                    size={36}
                                                />
                                            </Col>
                                        </Row>
                                        <Row className="role-row">
                                            <Col>
                                                <span className="p1">
                                                    Роль: {Common.makeRoleName(state.user?.role)}
                                                </span>
                                            </Col>
                                            {allowChangeRole() && (
                                                <Col>
                                                    <Dropdown
                                                        className={`role-select${state.isLoading ? ' disabled' : ''}`}
                                                        menu={{
                                                            items: state.roles
                                                                .filter(
                                                                    (f) => f.name != 'Roles.Admin',
                                                                )
                                                                .map((r) => {
                                                                    return {
                                                                        key: r.name,
                                                                        label: (
                                                                            <span className="p3">
                                                                                {Common.makeRoleName(
                                                                                    r.name,
                                                                                )}
                                                                            </span>
                                                                        ),
                                                                    };
                                                                }),
                                                            multiple: false,
                                                            onSelect: (info) =>
                                                                changeUserRole(info.key),
                                                            selectable: true,
                                                            selectedKeys: [state.user?.role],
                                                        }}
                                                        placement="bottomRight"
                                                    >
                                                        <div className="icon-container">
                                                            <div className="chevron-icon" />
                                                        </div>
                                                    </Dropdown>
                                                </Col>
                                            )}
                                            <Col>
                                                <Button
                                                    className="p3 view-permissions-btn"
                                                    disabled={state.isLoading}
                                                    onClick={() => {
                                                        setPermissionModalOpen(true);
                                                    }}
                                                >
                                                    Права
                                                </Button>
                                            </Col>
                                        </Row>
                                        <Row className="filter-row">
                                            {state.user?.filters.map((f) => (
                                                <FilterButton
                                                    key={`user-filter-${f.name}`}
                                                    hex={f.colorHEX}
                                                    text={f.name}
                                                />
                                            ))}
                                            {state.user?.filters.length == 0 && (
                                                <span className="p3">Нет фильтров</span>
                                            )}
                                            {allowFilterChange && (
                                                <Button
                                                    className="filter-change-btn"
                                                    disabled={state.isLoading}
                                                    icon={<div className="plus-icon" />}
                                                    onClick={() => {
                                                        if (!state.isLoading && allowFilterChange) {
                                                            setFilterPickerOpen(true);
                                                        }
                                                    }}
                                                />
                                            )}
                                        </Row>
                                        <Row className="description-row">
                                            <div className="desc-l">
                                                {state.user?.description?.length > 0
                                                    ? state.user?.description
                                                    : 'Без описания'}
                                            </div>
                                        </Row>
                                    </Col>
                                </Row>
                                {allowIdLoginEmail && (
                                    <Row className="login-email-row">
                                        {state.user?.id != creds.user_id && (
                                            <Col>
                                                <Row className="id-row">
                                                    <Col>
                                                        <span className="p2">
                                                            ID {state.user?.id}
                                                        </span>
                                                    </Col>
                                                    <Col>
                                                        <CopyButton
                                                            textToCopy={`Пользователь ${state.user?.id}`}
                                                            textToShow="ID пользователя скопирован"
                                                            size={24}
                                                        />
                                                    </Col>
                                                </Row>
                                            </Col>
                                        )}
                                        {state.user?.id != creds.user_id && (
                                            <Divider type="vertical" />
                                        )}
                                        <Col>
                                            <Row>
                                                <span className="p2">{state.user?.login}</span>
                                                <CopyButton
                                                    textToCopy={`Пользователь с логином ${state.user?.login}`}
                                                    textToShow="Логин пользователя скопирован"
                                                    size={24}
                                                />
                                            </Row>
                                        </Col>
                                        <Divider type="vertical" />
                                        <Col>
                                            <Row>
                                                <span className="p2">{state.user?.email}</span>
                                                <CopyButton
                                                    textToCopy={`Пользователь с email ${state.user?.email}`}
                                                    textToShow="Email пользователя скопирован"
                                                    size={24}
                                                />
                                            </Row>
                                        </Col>
                                        <Divider type="vertical" />
                                        <Col>
                                            <Row>
                                                <span className="p2">Пригласил(а):&nbsp;</span>
                                                {state.user?.invite_sender_id == null ||
                                                    (state.user?.invite_sender_id == 'None' && (
                                                        <Col className="p2 user-name">Система</Col>
                                                    ))}
                                                {state.user?.invite_sender_id != null &&
                                                    state.user?.invite_sender_id != 'None' && (
                                                        <Popconfirm
                                                            cancelText="Отмена"
                                                            disabled={
                                                                state.mode == 'view' || !anyChanges
                                                            }
                                                            okText="ОК"
                                                            onConfirm={() => {
                                                                navigate(
                                                                    `/management/users/${state.user?.invite_sender_id}`,
                                                                );
                                                            }}
                                                            title="Не сохранённые изменения будут потеряны"
                                                        >
                                                            <Col>
                                                                <Row
                                                                    className="invite-sender"
                                                                    onClick={() => {
                                                                        if (
                                                                            state.mode == 'view' ||
                                                                            !anyChanges
                                                                        ) {
                                                                            navigate(
                                                                                `/management/users/${state.user?.invite_sender_id}`,
                                                                            );
                                                                        }
                                                                    }}
                                                                >
                                                                    <Col className="user-avatar">
                                                                        {state.user
                                                                            ?.invite_sender_picture ==
                                                                            null && (
                                                                            <div className="p1-strong">
                                                                                {state.user
                                                                                    ?.invite_sender_name?.[0] ??
                                                                                    '-'}
                                                                            </div>
                                                                        )}
                                                                    </Col>
                                                                    <Col className="p2 user-name">
                                                                        <Button
                                                                            className="creator-btn"
                                                                            type="link"
                                                                        >
                                                                            {state.user
                                                                                ?.invite_sender_name ??
                                                                                'Неизвестный'}
                                                                        </Button>
                                                                    </Col>
                                                                </Row>
                                                            </Col>
                                                        </Popconfirm>
                                                    )}
                                            </Row>
                                        </Col>
                                    </Row>
                                )}
                            </Col>
                        </Row>
                        <Row className="p2 controls-row">
                            <Col>
                                <Row>
                                    {allowEdit && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={setEditing}
                                        >
                                            Редактировать
                                        </Button>
                                    )}
                                    {allowSecurity && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={setSecurity}
                                        >
                                            Безопасность
                                        </Button>
                                    )}
                                    {allowNewChat && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={chatWithUser}
                                        >
                                            Чат
                                        </Button>
                                    )}
                                    {allowChatWithManager && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={chatWithManager}
                                        >
                                            Чат с менеджером
                                        </Button>
                                    )}
                                    {allowUserInvitations && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={toUserInvitations}
                                        >
                                            Приглашения
                                        </Button>
                                    )}
                                    {allowUserSimulations && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={toUserSimulations}
                                        >
                                            Симуляции
                                        </Button>
                                    )}
                                    {allowUserSessions && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={toUserSessions}
                                        >
                                            Сессии
                                        </Button>
                                    )}
                                    {allowUserAssignments && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={toUserAssignments}
                                        >
                                            Назначения
                                        </Button>
                                    )}
                                    {state.user?.id == creds?.user_id && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={() => navigate('/logout')}
                                        >
                                            Выход
                                        </Button>
                                    )}
                                </Row>
                            </Col>
                            <Col>
                                <Row>
                                    {allowBan && (
                                        <Popconfirm
                                            cancelText="Отмена"
                                            okText="Подтвердить"
                                            onConfirm={banUser}
                                            title="Пользователь будет забанен"
                                        >
                                            <Button disabled={state.isLoading}>Бан</Button>
                                        </Popconfirm>
                                    )}
                                    {allowUnban && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={unbanUser}
                                        >
                                            Разбан
                                        </Button>
                                    )}
                                    {allowDelete && (
                                        <Popconfirm
                                            cancelText="Отмена"
                                            okText="Подтвердить"
                                            onConfirm={deleteUser}
                                            title="Пользователь будет удалён"
                                        >
                                            <Button disabled={state.isLoading}>Удалить</Button>
                                        </Popconfirm>
                                    )}
                                    {allowRestore && (
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={restoreUser}
                                        >
                                            Восстановить
                                        </Button>
                                    )}
                                </Row>
                            </Col>
                        </Row>
                    </Col>
                )}
                {state.mode == 'edit' && allowEdit && (
                    <Col
                        flex={1}
                        className="user-card"
                    >
                        <Row className="body-row">
                            <Col>
                                <Row className="avatar-split-row">
                                    <Col className="avatar-col">
                                        <div
                                            className={
                                                state.user?.role == 'Roles.Client'
                                                    ? 'handsome-client'
                                                    : 'handsome-staff'
                                            }
                                        />
                                    </Col>
                                    <Col className="info-col editing">
                                        <Row className="name-row">
                                            <Tooltip title="От 3 до 32 символов, кириллица, латиница, цифры, тире и пробелы">
                                                <Input
                                                    allowClear
                                                    autoComplete="first-name name username"
                                                    maxLength={32}
                                                    minLength={3}
                                                    onChange={(e) =>
                                                        updateUser({ name: e.target.value })
                                                    }
                                                    placeholder="Введите имя"
                                                    showCount
                                                    status={
                                                        nameMask.test(state.user?.name)
                                                            ? null
                                                            : 'error'
                                                    }
                                                    value={state.user?.name}
                                                />
                                            </Tooltip>
                                        </Row>
                                        <Row className="role-row">
                                            <Col>
                                                <span className="p1">
                                                    Роль: {Common.makeRoleName(state.user?.role)}
                                                </span>
                                            </Col>
                                            {allowChangeRole() && (
                                                <Col>
                                                    <Dropdown
                                                        className="role-select"
                                                        disabled={state.isLoading}
                                                        menu={{
                                                            items: state.roles.map((r) => {
                                                                return {
                                                                    key: r.name,
                                                                    label: (
                                                                        <span className="p3">
                                                                            {Common.makeRoleName(
                                                                                r.name,
                                                                            )}
                                                                        </span>
                                                                    ),
                                                                };
                                                            }),
                                                            multiple: false,
                                                            onSelect: (info) =>
                                                                changeUserRole(info.key),
                                                            selectable: true,
                                                            selectedKeys: [state.user?.role],
                                                        }}
                                                        placement="bottomRight"
                                                    >
                                                        <div className="icon-container">
                                                            <div className="chevron-icon" />
                                                        </div>
                                                    </Dropdown>
                                                </Col>
                                            )}
                                            <Col>
                                                <Button
                                                    className="p2 view-permissions-btn"
                                                    disabled={state.isLoading}
                                                    onClick={() => {
                                                        setPermissionModalOpen(true);
                                                    }}
                                                >
                                                    Права
                                                </Button>
                                            </Col>
                                        </Row>
                                        <Row className="filter-row">
                                            {state.user?.filters.map((f) => (
                                                <FilterButton
                                                    key={`user-filter-${f.name}`}
                                                    hex={f.colorHEX}
                                                    text={f.name}
                                                />
                                            ))}
                                            {state.user?.filters.length == 0 && (
                                                <span className="p3">Нет фильтров</span>
                                            )}
                                            {allowFilterChange && (
                                                <Button
                                                    className="filter-change-btn"
                                                    disabled={state.isLoading}
                                                    icon={<div className="plus-icon" />}
                                                    onClick={() => {
                                                        if (!state.isLoading && allowFilterChange) {
                                                            setFilterPickerOpen(true);
                                                        }
                                                    }}
                                                />
                                            )}
                                        </Row>
                                        <Row className="description-row">
                                            <TextArea
                                                allowClear
                                                autoSize={{
                                                    maxRows: 6,
                                                    minRows: 3,
                                                }}
                                                maxLength={390}
                                                onChange={(e) =>
                                                    updateUser({ description: e.target.value })
                                                }
                                                placeholder="Введите описание"
                                                showCount
                                                value={state.user?.description}
                                            />
                                        </Row>
                                    </Col>
                                </Row>
                                {allowIdLoginEmail && (
                                    <Row className="login-email-row">
                                        {state.user?.id != creds.user_id && (
                                            <Col>
                                                <Row className="id-row">
                                                    <Col>
                                                        <span className="p2">
                                                            ID {state.user?.id}
                                                        </span>
                                                    </Col>
                                                    <Col>
                                                        <CopyButton
                                                            textToCopy={`Пользователь ${state.user?.id}`}
                                                            textToShow="ID пользователя скопирован"
                                                            size={24}
                                                        />
                                                    </Col>
                                                </Row>
                                            </Col>
                                        )}
                                        {state.user?.id != creds.user_id && (
                                            <Divider type="vertical" />
                                        )}
                                        <Col>
                                            <Row>
                                                <span className="p2">{state.user?.login}</span>
                                                <CopyButton
                                                    textToCopy={`Пользователь с логином ${state.user?.login}`}
                                                    textToShow="Логин пользователя скопирован"
                                                    size={24}
                                                />
                                            </Row>
                                        </Col>
                                        <Divider type="vertical" />
                                        <Col>
                                            <Row>
                                                <span className="p2">{state.user?.email}</span>
                                                <CopyButton
                                                    textToCopy={`Пользователь с email ${state.user?.email}`}
                                                    textToShow="Email пользователя скопирован"
                                                    size={24}
                                                />
                                            </Row>
                                        </Col>
                                    </Row>
                                )}
                            </Col>
                        </Row>
                        <Row className="p2 controls-row">
                            {anyChanges && (
                                <Button
                                    disabled={state.isLoading}
                                    onClick={saveChanges}
                                >
                                    Сохранить
                                </Button>
                            )}
                            <Popconfirm
                                cancelText="Отмена"
                                disabled={!anyChanges}
                                okText="Подтвердить"
                                onConfirm={cancelEditing}
                                title="Не сохранённые изменения будут потеряны"
                            >
                                <Button
                                    disabled={state.isLoading}
                                    onClick={() => {
                                        if (!anyChanges) cancelEditing();
                                    }}
                                >
                                    Отмена
                                </Button>
                            </Popconfirm>
                        </Row>
                    </Col>
                )}
                {state.mode == 'security' && allowEdit && (
                    <Col
                        flex={1}
                        className="user-card"
                    >
                        <Row className="id-row">
                            <Col>
                                <h4>Управление учётной записью</h4>
                            </Col>
                        </Row>
                        <Row className="body-row dropdowns">
                            <Col>
                                <Collapse
                                    accordion
                                    expandIcon={() => <div className="expand-icon" />}
                                    expandIconPosition="end"
                                    items={[
                                        {
                                            key: 'login',
                                            label: <h6>Смена логина</h6>,
                                            children: (
                                                <Col className="collapse-item">
                                                    <Row>
                                                        <Col className="labeled-input">
                                                            <Row>
                                                                <span className="p3">
                                                                    Текущий логин:
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <span className="p3-strong">
                                                                    {state.originalUser?.login}
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <span className="p3">
                                                                    Новый логин:
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <Tooltip title="От 3 до 16 символов, только латиница и цифры">
                                                                    <Input
                                                                        allowClear
                                                                        autoComplete="login username"
                                                                        maxLength={16}
                                                                        minLength={3}
                                                                        onChange={(e) => {
                                                                            updateUser({
                                                                                login: e.target
                                                                                    .value,
                                                                            });
                                                                        }}
                                                                        placeholder="Введите логин"
                                                                        showCount
                                                                        status={
                                                                            loginMask.test(
                                                                                state.user?.login,
                                                                            ) &&
                                                                            state.user?.login !=
                                                                                state.originalUser
                                                                                    ?.login
                                                                                ? null
                                                                                : 'error'
                                                                        }
                                                                        value={state.user?.login}
                                                                    />
                                                                </Tooltip>
                                                            </Row>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Button
                                                            className="p2 collapse-confirm"
                                                            disabled={
                                                                state.isLoading ||
                                                                !loginMask.test(
                                                                    state.user?.login,
                                                                ) ||
                                                                state.user?.login ==
                                                                    state.originalUser?.login
                                                            }
                                                            onClick={changeLogin}
                                                        >
                                                            Сохранить
                                                        </Button>
                                                    </Row>
                                                </Col>
                                            ),
                                        },
                                        {
                                            key: 'email',
                                            label: <h6>Смена email</h6>,
                                            children: (
                                                <Col className="collapse-item">
                                                    <Row>
                                                        <Col className="labeled-input">
                                                            <Row>
                                                                <span className="p3">
                                                                    Текущий email:
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <span className="p3-strong">
                                                                    {state.originalUser?.email}
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <span className="p3">
                                                                    Новый email:
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <Tooltip title="От 5 до 40 символов, email-формат">
                                                                    <Input
                                                                        allowClear
                                                                        autoComplete="email"
                                                                        maxLength={40}
                                                                        minLength={5}
                                                                        onChange={(e) => {
                                                                            updateUser({
                                                                                email: e.target
                                                                                    .value,
                                                                            });
                                                                        }}
                                                                        placeholder="Введите email"
                                                                        showCount
                                                                        status={
                                                                            emailMask.test(
                                                                                state.user?.email,
                                                                            ) &&
                                                                            state.user?.email
                                                                                .length >= 5 &&
                                                                            state.user?.email
                                                                                .length <= 40 &&
                                                                            state.user?.email !=
                                                                                state.originalUser
                                                                                    ?.email
                                                                                ? null
                                                                                : 'error'
                                                                        }
                                                                        value={state.user?.email}
                                                                    />
                                                                </Tooltip>
                                                            </Row>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Button
                                                            className="p2 collapse-confirm"
                                                            disabled={
                                                                state.isLoading ||
                                                                !emailMask.test(
                                                                    state.user?.email,
                                                                ) ||
                                                                state.user?.email.length < 5 ||
                                                                state.user?.email.length > 40 ||
                                                                state.user?.email ==
                                                                    state.originalUser?.email
                                                            }
                                                            onClick={changeEmail}
                                                        >
                                                            Сохранить
                                                        </Button>
                                                    </Row>
                                                </Col>
                                            ),
                                        },
                                        {
                                            key: 'password',
                                            label: <h6>Смена пароля</h6>,
                                            children: (
                                                <Col className="collapse-item">
                                                    <Row>
                                                        <Col className="labeled-input">
                                                            <Row>
                                                                <span className="p3">
                                                                    Новый пароль:
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <Tooltip title="От 8 до 24 символов, латиница, минимум 1 цифра">
                                                                    <Input.Password
                                                                        allowClear
                                                                        autoComplete="off"
                                                                        maxLength={24}
                                                                        minLength={8}
                                                                        onChange={(e) => {
                                                                            state.passwordInputValue =
                                                                                e.target.value;
                                                                        }}
                                                                        placeholder="Введите пароль"
                                                                        showCount
                                                                        status={
                                                                            passwordMask.test(
                                                                                state.passwordInputValue,
                                                                            )
                                                                                ? null
                                                                                : 'error'
                                                                        }
                                                                        value={
                                                                            state.passwordInputValue
                                                                        }
                                                                    />
                                                                </Tooltip>
                                                            </Row>
                                                            <Row>
                                                                <span className="p3">
                                                                    Повторите пароль:
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <Tooltip title="От 8 до 24 символов, латиница, минимум 1 цифра">
                                                                    <Input.Password
                                                                        allowClear
                                                                        autoComplete="off"
                                                                        maxLength={24}
                                                                        minLength={8}
                                                                        onChange={(e) => {
                                                                            state.passwordConfirmInputValue =
                                                                                e.target.value;
                                                                        }}
                                                                        onPaste={(e) => {
                                                                            e.preventDefault();
                                                                        }}
                                                                        placeholder="Повторите пароль"
                                                                        showCount
                                                                        status={
                                                                            passwordMask.test(
                                                                                state.passwordConfirmInputValue,
                                                                            ) &&
                                                                            state.passwordConfirmInputValue.localeCompare(
                                                                                state.passwordInputValue,
                                                                            ) == 0
                                                                                ? null
                                                                                : 'error'
                                                                        }
                                                                        value={
                                                                            state.passwordConfirmInputValue
                                                                        }
                                                                    />
                                                                </Tooltip>
                                                            </Row>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Button
                                                            className="p2 collapse-confirm"
                                                            disabled={
                                                                state.isLoading ||
                                                                !passwordMask.test(
                                                                    state.passwordInputValue,
                                                                ) ||
                                                                !passwordMask.test(
                                                                    state.passwordConfirmInputValue,
                                                                ) ||
                                                                state.passwordInputValue.localeCompare(
                                                                    state.passwordConfirmInputValue,
                                                                ) != 0
                                                            }
                                                            onClick={changePassword}
                                                        >
                                                            Сохранить
                                                        </Button>
                                                    </Row>
                                                </Col>
                                            ),
                                        },
                                        {
                                            key: 'refresh',
                                            label: <h6>Выйти на всех устройствах</h6>,
                                            children: (
                                                <Col className="collapse-item">
                                                    <Row>
                                                        <Col className="labeled-input">
                                                            <Row>
                                                                <span className="p3">
                                                                    Все сессии, кроме этой, будут
                                                                    завершены
                                                                </span>
                                                            </Row>
                                                            <Row>
                                                                <Button
                                                                    className="p2 collapse-confirm"
                                                                    onClick={refreshToken}
                                                                >
                                                                    Подтвердить
                                                                </Button>
                                                            </Row>
                                                        </Col>
                                                    </Row>
                                                </Col>
                                            ),
                                        },
                                    ]}
                                />
                            </Col>
                        </Row>
                        <Row className="p2 controls-row">
                            <Button
                                disabled={state.isLoading}
                                onClick={cancelSecurity}
                            >
                                Назад
                            </Button>
                        </Row>
                    </Col>
                )}
            </div>
        </UniLayout>
    );
};

export default UserProfile;
