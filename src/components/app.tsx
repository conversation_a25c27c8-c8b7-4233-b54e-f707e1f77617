import { lazy, Suspense, useCallback, useEffect } from 'react';
import { Loader } from '@components/ui/loader/loader';
import {
    createBrowserRouter,
    createRoutesFromElements,
    Navigate,
    Route,
    RouterProvider,
} from 'react-router-dom';
import { SettingsManager, TCredentials } from 'src/shared/settingsManager';
import { InvitationProfileUseCases } from './invitations/invitationProfile';
import { SimulationListUseCases } from '@components/simulations/simulationList';
import { PusherProvider } from '@common/contexts/Pusher';
import { useReactive } from 'ahooks';
import { CRMAPI } from '@api/crmApi';
import { rootStore } from 'src/store/instanse';
import { Button, ConfigProvider, message, Row } from 'antd';
import ruRU from 'antd/locale/ru_RU';
import { observer } from 'mobx-react';
import { ConstructorScheduleUseCases } from './constructor/constructorSchedule/constructorSchedule';

const FontsTest = lazy(() => import('@components/tests/fonts'));
const ColorsTest = lazy(() => import('@components/tests/colors'));
const StoresTest = lazy(() => import('@components/tests/stores'));
const NetworkTest = lazy(() => import('@components/tests/network'));
const TableStatusesTest = lazy(() => import('@components/tests/tableStatuses'));

const Login = lazy(() => import('@components/login/login'));
const Logout = lazy(() => import('@components/user/logout'));
const InviteRegistration = lazy(() => import('@components/inviteRegistration/inviteRegistration'));

const SimulationList = lazy(() => import('@components/simulations/simulationList'));
const SimulationProfile = lazy(() => import('@components/simulations/simulationProfile'));

const UserList = lazy(() => import('@components/user/userList'));
const UserProfile = lazy(() => import('@components/user/userProfile'));
const InvitationList = lazy(() => import('@components/invitations/invitationList'));
const InvitationProfile = lazy(() => import('@components/invitations/invitationProfile'));
const RolesPage = lazy(() => import('@components/roles/roles'));
const FiltersPage = lazy(() => import('@components/filters/filters'));

const ConstructorSim = lazy(() => import('@components/constructor/constructorSim/constructorSim'));
const ConstructorGraph = lazy(
    () => import('@components/constructor/constructorGraph/constructorGraph'),
);
const ConstructorGantt = lazy(
    () => import('@components/constructor/constructorGantt/constructorGantt'),
);
const ConstructorNodeList = lazy(
    () => import('@components/constructor/constructorNode/constructorNodeList'),
);
const ConstructorNodeProfile = lazy(
    () => import('@components/constructor/constructorNode/constructorNodeProfile'),
);
const ConstructorWorkerList = lazy(
    () => import('@components/constructor/constructorWorker/constructorWorkerList'),
);
const ConstructorWorkerProfile = lazy(
    () => import('@components/constructor/constructorWorker/constructorWorkerProfile'),
);
const ConstructorEventList = lazy(
    () => import('@components/constructor/constructorEvent/constructorEventList'),
);
const ConsturctorEventProfile = lazy(
    () => import('@components/constructor/constructorEvent/constructorEventProfile'),
);
const ConstructorSchedule = lazy(
    () => import('@components/constructor/constructorSchedule/constructorSchedule'),
);

const SessionList = lazy(() => import('@components/sessions/sessionList'));
const SessionProfile = lazy(() => import('@components/sessions/sessionProfile'));
const SessionAssignmentList = lazy(
    () => import('@components/sessions/sessionAssignments/sessionAssignmentList'),
);
const SessionAssignmentProfile = lazy(
    () => import('@components/sessions/sessionAssignments/sessionAssignmentProfile'),
);

const SessionDesktop = lazy(() => import('@components/ingame/sessionDesktop/sessionDesktop'));
const SessionDashboard = lazy(() => import('@components/ingame/sessionDashboard/sessionDashboard'));
const SessionGantt = lazy(() => import('@components/ingame/sessionGantt/sessionGantt'));
const SessionChats = lazy(() => import('@components/ingame/sessionChats/sessionChats'));
const SessionCalendar = lazy(() => import('@components/ingame/sessionCalendar/sessionCalendar'));
const SessionBudget = lazy(() => import('@components/ingame/sessionBudget/sessionBudget'));
const SessionWorkerList = lazy(() => import('@components/ingame/sessionWorkers/sessionWorkerList'));
const SessionWorkerProfile = lazy(
    () => import('@components/ingame/sessionWorkers/sessionWorkerProfile'),
);
const SessionTask = lazy(() => import('@components/ingame/sessionTask/sessionTask'));
const SessionGraph = lazy(() => import('@components/ingame/ingameGraph/ingameGraph'));
const SessionCharts = lazy(() => import('@components/ingame/charts/chartList'));
const ComplexTaskChartPage = lazy(
    () => import('@components/ingame/charts/complexTaskChart/complexTaskChartPage'),
);

const NotificationHolder = lazy(
    () => import('@components/notifications/notificationChannelHolder'),
);
const ChatsPage = lazy(() => import('@components/chats/chatsPage'));
const NotificationList = lazy(() => import('@components/notifications/notificationList'));

type TState = {
    accessToken: TCredentials['accessToken'];
    assignmentNoticeShown: boolean;
    validated: boolean;
    user_id: TCredentials['user_id'];
};

const App = observer((): JSX.Element => {
    const state = useReactive<TState>({
        accessToken: null,
        assignmentNoticeShown: false,
        validated: false,
        user_id: null,
    });

    const validateToken = useCallback(async () => {
        try {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.accessToken == null) return;
            const api = new CRMAPI(creds?.accessToken);
            const result = await api.currentUser(true);
            if (result.errorMessages) throw result;
        } catch (errors) {
            if (rootStore.socketStore.verbose) {
                console.log(errors);
            }
            SettingsManager.clearConnectionCredentials();
            rootStore.socketStore.clearStore();
        }
    }, []);

    useEffect(() => {
        if (!state.validated) {
            validateToken().then(() => {
                state.validated = true;
            });
        }
    }, []);

    useEffect(() => {
        const creds = SettingsManager.getConnectionCredentials();
        if (state.accessToken != creds?.accessToken) {
            state.accessToken = creds?.accessToken;
        }
        if (state.user_id != creds?.user_id) {
            state.user_id = creds?.user_id;
        }
    }, [rootStore.currentUserStore.user]);

    function getContent(): JSX.Element {
        if (SettingsManager.getConnectionCredentials()?.accessToken == null) {
            if (
                window.location?.pathname != '/login' &&
                !window.location.pathname.includes('/invite/')
            ) {
                window.location.pathname = '/login';
            }
        } else {
            if (window.location?.pathname == '/login') {
                window.location.pathname = '/lk';
            }
            const creds = SettingsManager.getConnectionCredentials();
            if (
                creds.sessionAssignmentId != null &&
                window.location?.pathname.includes('/session') == false &&
                !state.assignmentNoticeShown
            ) {
                message.destroy('assignment-notice');
                state.assignmentNoticeShown = true;
                message.info({
                    content: (
                        <Row style={{ columnGap: '8px', justifyContent: 'space-between' }}>
                            <span className="p4">Найдено прохождение</span>
                            <Button
                                onClick={() => {
                                    message.destroy('assignment-notice');
                                    window.location.pathname = '/session';
                                }}
                                size="small"
                            >
                                Вернуться
                            </Button>
                            <Button
                                danger
                                onClick={() => {
                                    message.destroy('assignment-notice');
                                    SettingsManager.updateConnectionCredentials({
                                        sessionAssignmentId: null,
                                    });
                                }}
                                size="small"
                            >
                                Закрыть
                            </Button>
                        </Row>
                    ),
                    duration: 0,
                    key: 'assignment-notice',
                });
            }
            if (
                creds.sessionAssignmentId != null &&
                window.location?.pathname.includes('/session') &&
                state.assignmentNoticeShown
            ) {
                state.assignmentNoticeShown = false;
            }
            if (creds.sessionAssignmentId == null) {
                message.destroy('assignment-notice');
            }
        }
        if (!state.validated) return <Loader />;

        const router = createBrowserRouter(
            createRoutesFromElements(
                <Route
                    path="/"
                    element={null}
                >
                    <Route
                        path=""
                        element={<Navigate to="/lk" />}
                    />
                    <Route
                        path="login"
                        element={<Login />}
                    />
                    <Route
                        path="invite/:inviteUid"
                        element={<InviteRegistration />}
                    />

                    <Route
                        path="simulations"
                        element={<Navigate to="/simulations/finished" />}
                    />
                    <Route
                        path="simulations/finished"
                        element={<SimulationList useCase={SimulationListUseCases.Finished} />}
                    />
                    <Route
                        path="simulations/unfinished"
                        element={<SimulationList useCase={SimulationListUseCases.Unfinished} />}
                    />
                    <Route
                        path="simulations/archive"
                        element={<SimulationList useCase={SimulationListUseCases.Archive} />}
                    />
                    <Route
                        path="simulations/all"
                        element={<SimulationList useCase={SimulationListUseCases.All} />}
                    />
                    <Route
                        path="simulations/:simId"
                        element={<SimulationProfile />}
                    />

                    <Route
                        path="constructor"
                        element={<ConstructorSim />}
                    />
                    <Route
                        path="constructor/:simId"
                        element={<ConstructorSim />}
                    />
                    <Route
                        path="constructor/:simId/graph"
                        element={<ConstructorGraph />}
                    />
                    <Route
                        path="constructor/:simId/gantt"
                        element={<ConstructorGantt />}
                    />
                    <Route
                        path="constructor/:simId/nodes"
                        element={<ConstructorNodeList />}
                    />
                    <Route
                        path="constructor/:simId/nodes/new"
                        element={<ConstructorNodeProfile />}
                    />
                    <Route
                        path="constructor/:simId/nodes/:nodeId"
                        element={<ConstructorNodeProfile />}
                    />
                    <Route
                        path="constructor/:simId/events"
                        element={<ConstructorEventList />}
                    />
                    <Route
                        path="constructor/:simId/events/new"
                        element={<ConsturctorEventProfile />}
                    />
                    <Route
                        path="constructor/:simId/events/:eventId"
                        element={<ConsturctorEventProfile />}
                    />
                    <Route
                        path="constructor/:simId/workers"
                        element={<ConstructorWorkerList />}
                    />
                    <Route
                        path="constructor/:simId/workers/new"
                        element={<ConstructorWorkerProfile />}
                    />
                    <Route
                        path="constructor/:simId/workers/:workerId"
                        element={<ConstructorWorkerProfile />}
                    />
                    <Route
                        path="constructor/:simId/schedule-events"
                        element={
                            <ConstructorSchedule
                                useCase={ConstructorScheduleUseCases.SimScheduleEvents}
                            />
                        }
                    />
                    <Route
                        path="constructor/:simId/schedule-event-types"
                        element={
                            <ConstructorSchedule
                                useCase={ConstructorScheduleUseCases.ScheduleEventTypes}
                            />
                        }
                    />

                    <Route
                        path="management/users"
                        element={<UserList />}
                    />
                    <Route
                        path="management/users/:userId"
                        element={<UserProfile />}
                    />
                    <Route
                        path="management/invitations"
                        element={<InvitationList />}
                    />
                    <Route
                        path="management/invitations/:invitationId"
                        element={<InvitationProfile useCase={InvitationProfileUseCases.Profile} />}
                    />
                    <Route
                        path="management/invitations/new"
                        element={<InvitationProfile useCase={InvitationProfileUseCases.New} />}
                    />
                    <Route
                        path="management/roles"
                        element={<RolesPage />}
                    />
                    <Route
                        path="management/filters"
                        element={<FiltersPage />}
                    />

                    <Route
                        path="controls/sessions"
                        element={<SessionList />}
                    />
                    <Route
                        path="controls/sessions/:sessionId"
                        element={<SessionProfile />}
                    />
                    <Route
                        path="controls/assignments"
                        element={<SessionAssignmentList />}
                    />
                    <Route
                        path="controls/assignments/:sessionAssignmentId"
                        element={<SessionAssignmentProfile />}
                    />

                    <Route
                        path="session"
                        element={<Navigate to="/session/desktop" />}
                    />
                    <Route
                        path="session/desktop"
                        element={<SessionDesktop />}
                    />
                    <Route
                        path="session/dashboard"
                        element={<SessionDashboard />}
                    />
                    <Route
                        path="session/gantt"
                        element={<SessionGantt />}
                    />
                    <Route
                        path="session/chats"
                        element={<SessionChats />}
                    />
                    <Route
                        path="session/chats/:chatId"
                        element={<SessionChats />}
                    />
                    <Route
                        path="session/calendar"
                        element={<SessionCalendar />}
                    />
                    <Route
                        path="session/budget"
                        element={<SessionBudget />}
                    />
                    <Route
                        path="session/tasks/:sessionTaskId"
                        element={<SessionTask />}
                    />
                    <Route
                        path="session/workers"
                        element={<SessionWorkerList />}
                    />
                    <Route
                        path="session/workers/:sessionWorkerId"
                        element={<SessionWorkerProfile />}
                    />
                    <Route
                        path="session/graph"
                        element={<SessionGraph />}
                    />
                    <Route
                        path="session/charts"
                        element={<SessionCharts />}
                    />
                    <Route
                        path="sessions/charts/complex-task-chart"
                        element={<ComplexTaskChartPage />}
                    />

                    <Route
                        path="notifications"
                        element={<NotificationList />}
                    />
                    <Route
                        path="chats"
                        element={<ChatsPage />}
                    />
                    <Route
                        path="chats/:chatId"
                        element={<ChatsPage />}
                    />
                    <Route
                        path="lk"
                        element={<UserProfile />}
                    />
                    <Route
                        path="logout"
                        element={<Logout />}
                    />

                    <Route
                        path="tests/fonts"
                        element={<FontsTest />}
                    />
                    <Route
                        path="tests/colors"
                        element={<ColorsTest />}
                    />
                    <Route
                        path="tests/stores"
                        element={<StoresTest />}
                    />
                    <Route
                        path="tests/network"
                        element={<NetworkTest />}
                    />
                    <Route
                        path="tests/table-statuses"
                        element={<TableStatusesTest />}
                    />
                    <Route
                        path="*"
                        element={<Navigate to="lk" />}
                    />
                </Route>,
            ),
        );
        return (
            <Suspense fallback={<Loader />}>
                <RouterProvider router={router} />
            </Suspense>
        );
    }

    return (
        <ConfigProvider locale={ruRU}>
            <PusherProvider
                userId={state.validated ? state.user_id : null}
                token={state.validated ? state.accessToken : null}
            >
                <div id="simbios-app-content">
                    <NotificationHolder />
                    {getContent()}
                </div>
            </PusherProvider>
        </ConfigProvider>
    );
});

export { App };
