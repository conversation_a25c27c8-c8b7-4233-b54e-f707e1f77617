import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Handle, Position } from '@xyflow/react';
import { Button, Col, Progress, Row } from 'antd';
import { memo } from 'react';
import Colors from 'src/shared/colors';
import { GanttNodeInner } from './gantt';

export type GanttNodeProps = {
    data: GanttNodeInner & { onPlusClick: (id: number) => void };
};

export default memo(({ data }: GanttNodeProps) => {
    return (
        <div className="gantt-node-wrapper">
            {data.previous.length > 0 && (
                <Handle
                    id="in"
                    type="target"
                    position={Position.Left}
                />
            )}
            <div className="gantt-node-inner">
                <Col
                    className="main-col"
                    style={{
                        backgroundColor: data.milestone
                            ? Colors.Error.warm[400]
                            : Colors.Accent.warm[200],
                        backgroundImage: `linear-gradient(${data.milestone ? Colors.Error.cold[800] : Colors.Accent.warm[500]})`,
                        backgroundPosition: 'left top',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: `${Math.floor(data.progress * 100)}% 100%`,
                        borderStartEndRadius: data.slack > 0 ? '0' : '14px',
                        borderEndEndRadius: data.slack > 0 ? '0' : '14px',
                        color: data.milestone ? Colors.Neutral[0] : Colors.Neutral[950],
                        width: `${Math.floor((data.curDuration * 100) / (data.curDuration + data.slack))}%`,
                    }}
                >
                    <Row>
                        <Col className="gantt-node-workers">
                            <Row>
                                <Col>
                                    {/* Для 4 и более дней - 3 ПШЕ, остаток и (+) */}
                                    {data.curDuration >= 3.2 && (
                                        <Row>
                                            {data.workers.length > 0 && (
                                                <div className="worker-mini p3">
                                                    {data.workers[0].name.slice(0, 2)}
                                                </div>
                                            )}
                                            {data.workers.length > 1 && (
                                                <div className="worker-mini p3">
                                                    {data.workers[1].name.slice(0, 2)}
                                                </div>
                                            )}
                                            {data.workers.length > 2 && (
                                                <div className="worker-mini p3">
                                                    {data.workers[2].name.slice(0, 2)}
                                                </div>
                                            )}
                                            {data.workers.length > 3 && (
                                                <div
                                                    className="worker-counter p2"
                                                    onClick={(e) => {
                                                        if (!data.allowActions) return;
                                                        if (data.workers.length <= 3) return;
                                                        e.preventDefault();
                                                        data.onPlusClick(data.id);
                                                    }}
                                                >
                                                    +{data.workers.length - 3}
                                                </div>
                                            )}
                                            {data.workers.length <= 3 && (
                                                <Button
                                                    className="add-worker-btn nodrag"
                                                    disabled={!data.allowActions}
                                                    icon={<FontAwesomeIcon icon={faPlus} />}
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        data.onPlusClick(data.id);
                                                    }}
                                                />
                                            )}
                                        </Row>
                                    )}
                                    {/* Если ширина в 3 дня - 1-2 ПШЕ, остаток и/или (+) */}
                                    {data.curDuration < 3.2 && data.curDuration >= 2.5 && (
                                        <Row>
                                            {data.workers.length > 0 && (
                                                <div className="worker-mini p3">
                                                    {data.workers[0].name.slice(0, 2)}
                                                </div>
                                            )}
                                            {data.workers.length > 1 && (
                                                <div className="worker-mini p3">
                                                    {data.workers[1].name.slice(0, 2)}
                                                </div>
                                            )}
                                            {data.workers.length > 2 && (
                                                <div
                                                    className="worker-counter p2"
                                                    onClick={(e) => {
                                                        if (!data.allowActions) return;
                                                        if (data.workers.length <= 2) return;
                                                        e.preventDefault();
                                                        data.onPlusClick(data.id);
                                                    }}
                                                >
                                                    +{data.workers.length - 2}
                                                </div>
                                            )}
                                            {data.workers.length < 3 && (
                                                <Button
                                                    className="add-worker-btn nodrag"
                                                    disabled={!data.allowActions}
                                                    icon={<FontAwesomeIcon icon={faPlus} />}
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        data.onPlusClick(data.id);
                                                    }}
                                                />
                                            )}
                                        </Row>
                                    )}
                                    {/* Если ширина в 2 дня - 0-1 ПШЕ и остаток или (+) */}
                                    {data.curDuration < 2.5 && data.curDuration >= 1.8 && (
                                        <Row>
                                            {data.workers.length > 0 && (
                                                <div className="worker-mini p3">
                                                    {data.workers[0].name.slice(0, 2)}
                                                </div>
                                            )}
                                            {data.workers.length > 1 && (
                                                <div
                                                    className="worker-counter p2"
                                                    onClick={(e) => {
                                                        if (!data.allowActions) return;
                                                        if (data.workers.length <= 1) return;
                                                        e.preventDefault();
                                                        data.onPlusClick(data.id);
                                                    }}
                                                >
                                                    +{data.workers.length - 1}
                                                </div>
                                            )}
                                            {data.workers.length < 2 && (
                                                <Button
                                                    className="add-worker-btn nodrag"
                                                    disabled={!data.allowActions}
                                                    icon={<FontAwesomeIcon icon={faPlus} />}
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        data.onPlusClick(data.id);
                                                    }}
                                                />
                                            )}
                                        </Row>
                                    )}
                                    {/* Если ширина в 1 день - только счётчик, либо (+) */}
                                    {data.curDuration >= 0.75 && data.curDuration < 1.8 && (
                                        <Row>
                                            {data.workers.length > 0 ? (
                                                <div
                                                    className="worker-counter p3"
                                                    onClick={(e) => {
                                                        if (!data.allowActions) return;
                                                        e.preventDefault();
                                                        data.onPlusClick(data.id);
                                                    }}
                                                >
                                                    {data.workers.length}
                                                </div>
                                            ) : (
                                                <Button
                                                    className="add-worker-btn nodrag"
                                                    disabled={!data.allowActions}
                                                    icon={<FontAwesomeIcon icon={faPlus} />}
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        data.onPlusClick(data.id);
                                                    }}
                                                />
                                            )}
                                        </Row>
                                    )}
                                </Col>
                            </Row>
                        </Col>
                        {((data.curDuration > 4.07 && data.workers.length < 2) ||
                            data.curDuration >= 4.89) && (
                            <Col className="gantt-node-progress">
                                <Row>
                                    <Col className="desc-s">{Math.floor(data.progress * 100)}%</Col>
                                    <Col>
                                        <Progress
                                            percent={Math.floor(data.progress * 100)}
                                            showInfo={false}
                                            size={24}
                                            status="normal"
                                            strokeColor={Colors.Accent.warm[600]}
                                            trailColor={Colors.Accent.warm[0]}
                                            type="dashboard"
                                        />
                                    </Col>
                                </Row>
                            </Col>
                        )}
                    </Row>
                </Col>
                <Col
                    className="slack-col"
                    style={{
                        //border: `${data.slack > 0 ? `1px solid ${Colors.Neutral[50]}` : 'none'}`,
                        display: `${data.slack == 0 ? 'none' : 'block'}`,
                        width: `${Math.ceil((data.slack * 100) / (data.curDuration + data.slack))}%`,
                    }}
                ></Col>
            </div>
            {data.following.length > 0 && (
                <Handle
                    id="out"
                    type="source"
                    position={Position.Right}
                />
            )}
        </div>
    );
});
