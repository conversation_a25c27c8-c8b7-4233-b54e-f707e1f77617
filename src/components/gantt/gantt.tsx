import {
    ConnectionLineType,
    CoordinateExtent,
    MarkerType,
    Node,
    ReactFlow,
    ReactFlowProvider,
    useEdgesState,
    useNodesState,
    useReactFlow,
    useViewport,
} from '@xyflow/react';
import { TSimulation } from 'types/simulation/simulation';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import ganttNode from './ganttNode';
import { useReactive } from 'ahooks';
import { Button, Col, message, Row } from 'antd';
import { useCallback, useEffect, useRef } from 'react';
import { GanttTable, Item } from './ganttTable';
import { WorkerListDrawer } from '@components/workerListDrawer/workerListDrawer';
import { DefaultTimeSettings } from 'src/store/ingame/data';
import Colors from 'src/shared/colors';
import { IngameTimeSettings, IngameTotalSWAlinks } from 'types/ingame';
import _ from 'lodash';
import { TaskUtils } from '@shared/utils/taskUtitlty';
import CustomBackground from './background';
import { Permissions } from 'src/shared/permissions';
import ganttPlanNode from './ganttPlanNode';

import '@xyflow/react/dist/style.css';
import './gantt.scss';

export enum GanttUseCases {
    Constructor = 'constructor',
    Ingame = 'ingame',
    Mini = 'mini',
}

export type TSimTaskGanttExtend = TSimTask & {
    curDuration: number;
    workers: TSimWorker[];
};

type TProps = {
    className?: string;
    currentDay?: number;
    daysInAWeek: IngameTimeSettings['daysInAWeek'];
    onTableEdit?: (record: Item) => void;
    onTaskOpen: (task_uid: TSimTask['uid']) => void;
    onWorkerAssign?: (task_uid: TSimTask['uid'], worker_uid: TSimWorker['uid']) => void;
    onWorkerUnassign?: (task_uid: TSimTask['uid'], worker_uid: TSimWorker['uid']) => void;
    simulation: TSimulation | null;
    SWAlinks: IngameTotalSWAlinks | null;
    tasks: TSimTaskGanttExtend[] | TSessionTaskExtended[];
    useCase: GanttUseCases;
    workers: TSimWorker[] | TSessionWorkerExtended[];
};

// Константы для стилей
const EDGE_STYLE = {
    stroke: '#4a9eff',
    strokeWidth: 1.5,
    transition: 'all 0.3s ease',
};
const EDGE_CRIT_STYLE = {
    stroke: Colors.Error.warm[400],
    strokeWidth: 2.5,
    transition: 'all 0.3s ease',
};

const EDGE_MARKER = {
    type: MarkerType.ArrowClosed,
    width: 12,
    height: 12,
    color: '#4a9eff',
    strokeWidth: 1.5,
};
const EDGE_CRIT_MARKER = {
    type: MarkerType.ArrowClosed,
    width: 12,
    height: 12,
    color: Colors.Error.warm[400],
    strokeWidth: 1.5,
};

const defaultEdgeOptions = {
    type: 'smoothstep',
    animated: true,
    pathOptions: {
        offset: 5,
    },
    style: { stroke: '#00bfff', strokeWidth: 2 },
};

const nodeTypes = {
    ganttNode: ganttNode,
    ganttPlanNode: ganttPlanNode,
};

export type GanttNodeInner = {
    allowActions: boolean;
    column: number;
    // Текущая продолжительность в днях
    curDuration: TSimTaskGanttExtend['curDuration'];
    estBudget: TSimTask['est_budget'];
    estColumn: number;
    estDuration: TSimTask['est_duration'];
    estWorkers: TSimTask['est_workers'];
    following: TSimTask['following'];
    id: TSimTask['id'];
    milestone: TSimTask['milestone'];
    name: TSimTask['name'];
    previous: TSimTask['previous'];
    // В процентах, [0; 1]
    progress: number;
    row: number;
    slack?: number;
    statsReq: TSimTask['stats_req'];
    uid: TSimTask['uid'];
    workers: TSimWorker[] | TSessionWorkerExtended[];
};

type GanttNode = Node<
    GanttNodeInner & {
        onPlusClick: (id: number) => void;
    }
>;

type TState = {
    selectedTask: GanttNodeInner | null;
    tempTasks: GanttNodeInner[];
    workerPickerOpen: boolean;
};

const GanttContent = ({
    className,
    currentDay,
    daysInAWeek,
    onTableEdit,
    onTaskOpen,
    onWorkerAssign,
    onWorkerUnassign,
    simulation,
    SWAlinks,
    tasks,
    useCase,
    workers,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        selectedTask: null,
        tempTasks: [],
        workerPickerOpen: false,
    });
    const flowInstance = useReactFlow();
    const [
        nodes,
        setNodes,
    ] = useNodesState<GanttNode>([]);
    const [
        edges,
        setEdges,
    ] = useEdgesState([]);
    const [messageApi, contextHolder] = message.useMessage();
    const { x, y, zoom } = useViewport();
    const ganttColRef = useRef<HTMLDivElement>(null);
    const nodeHeight = 28;
    const rowPadding = 8;
    const widthPerDay = 32;
    const nodeStartX = widthPerDay * 3;
    const nodeStartY = nodeHeight + rowPadding;
    const showTable = useCase == GanttUseCases.Constructor && onTableEdit != null;
    const allowTaskWorkersChange = onWorkerAssign != null && onWorkerUnassign != null;

    function tasksToEdges() {
        const links = [];
        for (let i = 0; i < tasks.length; i++) {
            const t = state.tempTasks.find((tti) => tti.id == tasks[i].id);
            for (let j = 0; j < t.previous.length; j++) {
                const tprev = state.tempTasks.find((ti) => ti.id == t.previous[j]);
                const crit = t.milestone && tprev.milestone;
                links.push({
                    id: `e${tprev.id}-${t.id}`,
                    source: `${tprev.id}`,
                    target: `${t.id}`,
                    type: 'smoothstep',
                    animated: true,
                    className: crit ? 'critical' : '',
                    style: crit ? EDGE_CRIT_STYLE : EDGE_STYLE,
                    markerEnd: crit ? EDGE_CRIT_MARKER : EDGE_MARKER,
                });
            }
        }
        setEdges(links);
    }

    function tasksToNodes() {
        let nodes: GanttNode[] = [];
        if (simulation.first_task == null) {
            messageApi.error('Обязательно должен быть задан первый узел!');
            return;
        }

        const taskUtilsOut = TaskUtils.tasksToGantt({
            hoursInADay: DefaultTimeSettings.workDayHours - +DefaultTimeSettings.workDayLunchSkip,
            simulation: simulation,
            tasks: tasks,
            useCase: useCase,
            workers: workers,
        });
        state.tempTasks = taskUtilsOut.map((ti) => {
            return {
                ...ti,
                allowActions:
                    allowTaskWorkersChange &&
                    (useCase == GanttUseCases.Constructor ||
                        (tasks as TSessionTaskExtended[]).find((t) => t.id == ti.id)?.end_day ==
                            null),
            };
        });
        // Плановые узлы - положения и длина по архитектору
        nodes.push(
            ...state.tempTasks.map((tti) => {
                return {
                    id: 'plan-' + tti.id,
                    position: {
                        x: nodeStartX + tti.estColumn * widthPerDay,
                        y:
                            nodeStartY +
                            tti.row * nodeHeight +
                            (tti.row > 0 ? tti.row : 0) * rowPadding -
                            rowPadding * 0.5,
                    },
                    style: {
                        width: tti.estDuration * widthPerDay,
                    },
                    data: {
                        ...tti,
                        onPlusClick: null,
                    },
                    type: 'ganttPlanNode',
                };
            }),
        );
        // Обычные узлы - по текущим данным
        nodes.push(
            ...state.tempTasks.map((tti) => {
                return {
                    id: '' + tti.id,
                    position: {
                        x: nodeStartX + tti.column * widthPerDay,
                        y:
                            nodeStartY +
                            tti.row * nodeHeight +
                            (tti.row > 0 ? tti.row : 0) * rowPadding,
                    },
                    style: {
                        width: (tti.curDuration + tti.slack) * widthPerDay,
                    },
                    data: {
                        ...tti,
                        onPlusClick: (id) => selectTempTask(id),
                    },
                    type: 'ganttNode',
                };
            }),
        );
        setNodes(nodes);
    }

    function makeTableItems(): Item[] {
        return nodes.map((n) => {
            return { ...n.data, key: '' + n.data.id };
        });
    }

    function selectTempTask(id: number) {
        state.selectedTask = state.tempTasks.find((tti) => tti.id == id);
        state.workerPickerOpen = true;
    }

    useEffect(() => {
        if (simulation == null) return;
        tasksToNodes();
        tasksToEdges();
        if (state.selectedTask) {
            updateSelectedTask();
        }
    }, [
        simulation,
        tasks,
        workers,
        useCase,
    ]);

    function updateSelectedTask() {
        const foundTask = tasks.find((ti) => ti.id == state.selectedTask.id);
        if (foundTask == undefined) {
            state.selectedTask = null;
            return;
        }
        const foundTT = state.tempTasks.find((tti) => tti.id == state.selectedTask.id);
        state.selectedTask = foundTT;
    }

    function assignWorkerToSelTask(id: number) {
        if (!state.selectedTask) return;
        const worker = workers.find((wi) => wi.id == id);
        if (worker == undefined) return;
        if (useCase == GanttUseCases.Constructor) {
            onWorkerAssign(state.selectedTask.uid, (worker as TSimWorker).uid);
        } else {
            onWorkerAssign(state.selectedTask.uid, (worker as TSessionWorkerExtended).worker_uid);
        }
    }

    function unassignWorkerFromSelTask(id: number) {
        if (!state.selectedTask) return;
        const worker = workers.find((wi) => wi.id == id);
        if (worker == undefined) return;
        if (useCase == GanttUseCases.Constructor) {
            onWorkerUnassign(state.selectedTask.uid, (worker as TSimWorker).uid);
        } else {
            onWorkerUnassign(state.selectedTask.uid, (worker as TSessionWorkerExtended).worker_uid);
        }
    }

    const makeGanttBoundaries = useCallback(() => {
        const yStart = 0;
        const rowNum = tasks.length == 0 ? 1 : tasks.length;
        const yEnd = yStart + nodeStartY + rowNum * nodeHeight + (rowNum - 1) * rowPadding;
        const xStart = 0;
        const days = simulation.weeks * DefaultTimeSettings.daysInAWeek;
        const xEnd = xStart + nodeStartX + days * widthPerDay;
        if (ganttColRef == null) {
            return [
                [xStart, yEnd],
                [xEnd, yStart],
            ] as CoordinateExtent;
        } else {
            const gb = [
                [
                    xStart,
                    yEnd < ganttColRef?.current?.clientHeight
                        ? ganttColRef?.current?.clientHeight
                        : yEnd,
                ],
                [
                    xEnd < ganttColRef?.current?.clientWidth
                        ? ganttColRef?.current?.clientWidth
                        : xEnd,
                    yStart,
                ],
            ] as CoordinateExtent;
            return gb;
        }
    }, [tasks, simulation]);

    useEffect(() => {
        if (y != 0) {
            const vp = flowInstance.getViewport();
            flowInstance.setViewport({ ...vp, y: 0 });
        }
    }, [y]);

    return (
        <div className={`gantt-container ${className}`}>
            {contextHolder}
            <Row>
                {showTable && (
                    <Col className="gantt-table-col">
                        <GanttTable
                            dataSource={makeTableItems()}
                            updateTableTask={onTableEdit}
                        />
                    </Col>
                )}
                <Col
                    className={`gantt-flow-col${showTable ? '' : ' full-width'}`}
                    ref={ganttColRef}
                >
                    {simulation == null ||
                        (tasks.length == 0 && (
                            <div className="no-sim-or-tasks">
                                <span className="p3">Нет симуляции или задач</span>
                            </div>
                        ))}
                    {simulation != null && nodes.length > 0 && (
                        <ReactFlow
                            className="gantt-flow"
                            nodes={nodes}
                            edges={edges}
                            nodeTypes={nodeTypes}
                            defaultEdgeOptions={defaultEdgeOptions}
                            connectionLineType={ConnectionLineType.SmoothStep}
                            connectionLineStyle={defaultEdgeOptions.style}
                            proOptions={{ hideAttribution: true }}
                            // Запрет удаления
                            deleteKeyCode={null}
                            // Не фокусить связи
                            edgesFocusable={false}
                            // Отруб подсветки
                            elevateEdgesOnSelect={false}
                            // Отключение автозума
                            fitView={false}
                            // Ограничение зума
                            maxZoom={1}
                            minZoom={1}
                            // Запрет мультивыбора
                            multiSelectionKeyCode={null}
                            // Не соединять узлы
                            nodesConnectable={false}
                            // Не перемещать узлы
                            nodesDraggable={false}
                            // Не фокусить узлы
                            nodesFocusable={false}
                            // Рендер только видимых элементов
                            onlyRenderVisibleElements={true}
                            // Перемещение скроллом
                            panOnScroll={true}
                            // Снятие запрета на скролл
                            preventScrolling={false}
                            // Запрет выбора
                            selectionKeyCode={null}
                            // Задание границ вью-порта
                            translateExtent={makeGanttBoundaries()}
                            // Отключение зума двойным кликом
                            zoomOnDoubleClick={false}
                            // Отключение зума на тач-девайсе
                            zoomOnPinch={false}
                            // Отключение зума скроллом
                            zoomOnScroll={false}
                        >
                            <CustomBackground
                                baseColor={Colors.Neutral[0]}
                                daysInAWeek={daysInAWeek}
                                splitColor={Colors.Neutral[10]}
                                weeksInSimulation={simulation.weeks}
                                widthPerDay={widthPerDay}
                            />
                            <div
                                id="task-numbering"
                                style={{
                                    paddingTop: `${nodeStartY - rowPadding * 0.5}px`,
                                    rowGap: rowPadding * 0.5,
                                    width: widthPerDay * 3,
                                }}
                            >
                                {tasks.map((task, i) => (
                                    <Row
                                        key={`task-num-${i}`}
                                        className="p3-strong"
                                        style={{
                                            maxHeight: `${nodeHeight + rowPadding * 0.5}px`,
                                            minHeight: `${nodeHeight + rowPadding * 0.5}px`,
                                        }}
                                    >
                                        {onTaskOpen != null &&
                                        ((useCase == GanttUseCases.Constructor &&
                                            Permissions.checkPermission(
                                                Permissions.SimulationTaskUpdate,
                                            )) ||
                                            (useCase != GanttUseCases.Constructor &&
                                                Permissions.checkPermission(
                                                    Permissions.SessionAssignmentInfoTasks,
                                                ))) ? (
                                            <Button
                                                onClick={() => {
                                                    if (useCase == GanttUseCases.Constructor) {
                                                        onTaskOpen(
                                                            (task as TSimTaskGanttExtend).uid,
                                                        );
                                                    } else {
                                                        onTaskOpen(
                                                            (task as TSessionTaskExtended).task_uid,
                                                        );
                                                    }
                                                }}
                                                type="link"
                                            >
                                                {`Задача ${i + 1}`}
                                            </Button>
                                        ) : (
                                            <span>{`Задача ${i + 1}`}</span>
                                        )}
                                    </Row>
                                ))}
                            </div>
                            {useCase != GanttUseCases.Constructor && currentDay != null && (
                                <div
                                    id="today-marker"
                                    style={{
                                        transform: `translate(${nodeStartX + (currentDay - 1) * widthPerDay + x}px, ${'calc(-100% - 28px)'})`,
                                        width: `${widthPerDay}px`,
                                    }}
                                >
                                    <div className="p2-strong today-text">Сегодня</div>
                                </div>
                            )}
                            <div
                                id="week-numbering"
                                style={{
                                    transform: `translate(${nodeStartX + x}px, -${
                                        currentDay != null
                                            ? ganttColRef?.current.clientHeight
                                            : className == 'sim-profile'
                                              ? 0
                                              : 28
                                    }px) scale(1)`,
                                    width: `${simulation.weeks * daysInAWeek * widthPerDay}px`,
                                }}
                            >
                                {Array.from({ length: simulation.weeks }).map((_, i) => (
                                    <Col
                                        key={`week-num-${i}`}
                                        style={{
                                            width: `${daysInAWeek * widthPerDay}px`,
                                        }}
                                    >
                                        <Row>
                                            <Col className="left-thingie" />
                                            <Col className="p1">{i + 1}</Col>
                                            <Col className="right-thingie" />
                                        </Row>
                                    </Col>
                                ))}
                            </div>
                        </ReactFlow>
                    )}
                </Col>
            </Row>
            {useCase != GanttUseCases.Mini && (
                <WorkerListDrawer
                    isOpen={state.workerPickerOpen}
                    onClose={() => {
                        state.workerPickerOpen = false;
                        state.selectedTask = null;
                    }}
                    onDeselect={unassignWorkerFromSelTask}
                    onTaskOpen={onTaskOpen}
                    onSelect={assignWorkerToSelTask}
                    simulation={simulation}
                    SWAlinks={
                        state.selectedTask == null || SWAlinks == null
                            ? null
                            : SWAlinks[state.selectedTask.id]
                    }
                    task={state.selectedTask}
                    workers={workers}
                    useCase={useCase}
                />
            )}
        </div>
    );
};

const Gantt = ({
    className = '',
    currentDay = null,
    daysInAWeek,
    onTableEdit = null,
    onTaskOpen,
    onWorkerAssign = null,
    onWorkerUnassign = null,
    simulation,
    SWAlinks,
    tasks,
    useCase,
    workers,
}: TProps): JSX.Element => {
    return (
        <ReactFlowProvider>
            <GanttContent
                className={className}
                currentDay={currentDay}
                daysInAWeek={daysInAWeek}
                onTableEdit={onTableEdit}
                onTaskOpen={onTaskOpen}
                onWorkerAssign={onWorkerAssign}
                onWorkerUnassign={onWorkerUnassign}
                simulation={simulation}
                SWAlinks={SWAlinks}
                tasks={tasks}
                useCase={useCase}
                workers={workers}
            />
        </ReactFlowProvider>
    );
};

export { Gantt };
