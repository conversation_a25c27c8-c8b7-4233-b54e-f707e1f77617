import { Common } from 'src/shared/common';
import { useReactive } from 'ahooks';
import { Checkbox, Col, Modal, Row } from 'antd';
import { useEffect } from 'react';
import { TPermission, TPermissionCategory } from 'types/user/permissions';
import { TUser } from 'types/user/user';

import './permissionModal.scss';

type TProps = {
    isOpen: boolean;
    permissionCategories: TPermissionCategory[] | null;
    permissions: TUser['permissions'];
    role: TUser['role'];
    setIsOpen: (isOpen: boolean) => void;
};

type TState = {
    flatSet: TPermission['id'][];
};

const PermissionModal = ({
    isOpen,
    permissionCategories,
    permissions,
    role,
    setIsOpen,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        flatSet: [],
    });

    useEffect(() => {
        if (permissions == null) return;
        const rolePermSet = [];
        for (let i = 0; i < Object.keys(permissions).length; i++) {
            const permCatKey = Object.keys(permissions)[i];
            const permCat = permissions[permCatKey];
            for (let j = 0; j < permCat.entity_permissions.length; j++) {
                rolePermSet.push(permCat.entity_permissions[j].id);
            }
        }
        state.flatSet = rolePermSet.sort();
    }, [permissions]);

    return (
        <Modal
            footer={null}
            onCancel={() => setIsOpen(false)}
            open={isOpen}
            title={
                <Row className="modal-header">
                    <Col>
                        <h6>Права роли {Common.makeRoleName(role)}</h6>
                    </Col>
                </Row>
            }
            wrapClassName="permission-modal-wrapper"
        >
            <Col>
                <Row className="permissions-card-list">
                    {permissionCategories != null &&
                        permissionCategories.map((pc) => {
                            return (
                                <div
                                    key={pc.name}
                                    className="permission-card"
                                >
                                    <Row className="p3 permission-category-name">
                                        {pc.name_locale}
                                    </Row>
                                    <Row className="p4 permission-category-body">
                                        {pc.entity_permissions?.map((ep) => (
                                            <Row
                                                key={ep.id}
                                                className="entity-permission"
                                            >
                                                <Checkbox
                                                    checked={state.flatSet.includes(ep.id)}
                                                    disabled={true}
                                                />
                                                {ep.name_locale}
                                            </Row>
                                        ))}
                                    </Row>
                                </div>
                            );
                        })}
                    {permissionCategories == null &&
                        permissions != null &&
                        Object.keys(permissions).map((pckey) => {
                            const pc = permissions[pckey];
                            return (
                                <div
                                    key={pckey}
                                    className="permission-card"
                                >
                                    <Row className="p3 permission-category-name">
                                        {Object.keys(pc).includes('locale') && (pc as any).locale}
                                        {Object.keys(pc).includes('name_locale') && pc.name_locale}
                                    </Row>
                                    <Row className="p4 permission-category-body">
                                        {pc.entity_permissions != null &&
                                            pc.entity_permissions.map((ep) => (
                                                <Row
                                                    key={ep.id}
                                                    className="entity-permission"
                                                >
                                                    <Checkbox
                                                        checked={state.flatSet.includes(ep.id)}
                                                        disabled={true}
                                                    />
                                                    {Object.keys(ep).includes('name_locale') &&
                                                        ep.name_locale}
                                                    {Object.keys(ep).includes('locale_name') &&
                                                        (ep as any).locale_name}
                                                </Row>
                                            ))}
                                    </Row>
                                </div>
                            );
                        })}
                </Row>
            </Col>
        </Modal>
    );
};

export { PermissionModal };
