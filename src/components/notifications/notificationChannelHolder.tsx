import { Permissions } from 'src/shared/permissions';
import { SettingsManager } from 'src/shared/settingsManager';
import { usePusherContext } from '@common/contexts/Pusher';
import { useNotificationsPusher } from '@hooks/useNotificationsPusher';
import { rootStore } from 'src/store/instanse';
import { observer } from 'mobx-react';
import { useEffect } from 'react';

const NotificationChannelConnector = (): JSX.Element => {
    const creds = SettingsManager.getConnectionCredentials();
    useNotificationsPusher({ userId: creds?.user_id });

    return null;
};

const NotificationChannelHolder = observer((): JSX.Element => {
    const creds = SettingsManager.getConnectionCredentials();
    const { socketRegistered } = usePusherContext();

    useEffect(() => {
        if (rootStore.socketStore.verbose) {
            console.log(
                `NotificationHolder: user_id ${creds?.user_id != null}, socketRegistered ${socketRegistered}`,
            );
        }
    }, [creds?.user_id, socketRegistered]);

    return (
        <>
            {creds?.user_id != null &&
                socketRegistered &&
                Permissions.checkPermission(Permissions.NotificationList) &&
                Permissions.checkPermission(Permissions.NotificationGet) && (
                    <NotificationChannelConnector />
                )}
        </>
    );
});

export default NotificationChannelHolder;
