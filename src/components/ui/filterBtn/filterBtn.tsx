import { TFilter } from 'types/filter';
import { Button } from 'antd';
import Colors from 'src/shared/colors';
import { Common } from 'src/shared/common';

import './filterBtn.scss';

type TProps = {
    emptyText?: string;
    hex: TFilter['colorHEX'];
    text: TFilter['name'];
};

const FilterButton = ({ emptyText = 'Пусто', hex, text }: TProps): JSX.Element => (
    <Button
        className="desc-l-strong filter-btn"
        style={{
            backgroundColor: hex,
            color: Common.shouldTextBeDark(hex) ? Colors.Black[950] : Colors.Black[25],
        }}
    >
        {Common.isNullOrEmptyString(text) ? emptyText : text}
    </Button>
);

export { FilterButton };
