import { Common } from 'src/shared/common';
import { faLink } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Tooltip } from 'antd';

type TProps = {
    textToCopy: string;
    textToShow: string;
    size?: number;
};

const LinkButton = ({ textToCopy, textToShow, size = 32 }: TProps): JSX.Element => {
    return (
        <Tooltip title="Копировать ссылку">
            <Button
                className="link-btn"
                icon={<FontAwesomeIcon icon={faLink} />}
                //icon={<div className="link-icon" />}
                onClick={(e) => {
                    e.stopPropagation();
                    Common.clipboardCopy(textToCopy, textToShow);
                }}
                style={{
                    height: `${size}px`,
                    width: `${size}px`,
                }}
                type="text"
            />
        </Tooltip>
    );
};

export { LinkButton };
