import { Common } from 'src/shared/common';
import { faCopy } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Tooltip } from 'antd';

type TProps = {
    textToCopy: string;
    textToShow: string;
    size?: number;
};

const CopyButton = ({ textToCopy, textToShow, size = 32 }: TProps): JSX.Element => {
    return (
        <Tooltip title="Копировать">
            <Button
                className="copy-btn"
                icon={<FontAwesomeIcon icon={faCopy} />}
                //icon={<div className="copy-icon" />}
                onClick={(e) => {
                    e.stopPropagation();
                    Common.clipboardCopy(textToCopy, textToShow);
                }}
                style={{
                    height: `${size}px`,
                    width: `${size}px`,
                }}
                type="text"
            />
        </Tooltip>
    );
};

export { CopyButton };
