import { useReactive } from 'ahooks';
import { rootStore } from 'src/store/instanse';
import { observer } from 'mobx-react';
import { Col, Row, Button, Input } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { Permissions } from 'src/shared/permissions';
import { useEffect, useRef } from 'react';
import useDraggableScroll from 'use-draggable-scroll';

import './topBar.scss';

const { Search } = Input;

type TopBarProps = {
    activeTab: string | null;
    tabSet:
        | 'simulations'
        | 'constructor-new'
        | 'constructor'
        | 'management'
        | 'controls'
        | 'dev'
        | null;
    showSearch: boolean;
    topBarText: string | null;
};

type TState = {
    searchValue: string;
};

const TopBar: React.FC<TopBarProps> = observer(
    ({ activeTab, tabSet, showSearch = true, topBarText }): JSX.Element => {
        const state = useReactive<TState>({
            searchValue: '',
        });
        const navigate = useNavigate();
        const { simId } = useParams();
        const buttonGroupRef = useRef<HTMLDivElement>(null);
        const { onMouseDown } = useDraggableScroll(buttonGroupRef, {
            direction: 'horizontal',
        });

        const tabs = {
            simulations: [
                {
                    show: Permissions.checkPermission(Permissions.SimulationList),
                    tabKey: 'finished',
                    title: 'Готовые',
                    getRoute: () => {
                        return '/simulations/finished';
                    },
                },
                {
                    show: Permissions.checkPermission(Permissions.SimulationList),
                    tabKey: 'unfinished',
                    title: 'Разработка',
                    getRoute: () => {
                        return '/simulations/unfinished';
                    },
                },
                {
                    show: Permissions.checkPermission(Permissions.SimulationList),
                    tabKey: 'archive',
                    title: 'Архив',
                    getRoute: () => {
                        return '/simulations/archive';
                    },
                },
                {
                    show: Permissions.checkPermission(Permissions.SimulationList),
                    tabKey: 'all',
                    title: 'Все',
                    getRoute: () => {
                        return '/simulations/all';
                    },
                },
            ],
            'constructor-new': [
                {
                    show: Permissions.checkPermission(Permissions.SimulationCreate),
                    tabKey: 'new-simulation',
                    title: 'Новая симуляция',
                    getRoute: () => {
                        return '/constructor';
                    },
                },
            ],
            constructor: [
                {
                    show:
                        Permissions.checkPermission(Permissions.SimulationGet) &&
                        Permissions.checkPermission(Permissions.SimulationCreate) &&
                        Permissions.checkPermission(Permissions.SimulationUpdate) &&
                        Permissions.checkPermission(Permissions.SimulationRestore) &&
                        Permissions.checkPermission(Permissions.SimulationDelete) &&
                        Permissions.checkPermission(Permissions.SimulationTest),
                    tabKey: 'simulation',
                    title: 'Описание симуляции',
                    getRoute: () => {
                        return `/constructor/${simId}`;
                    },
                },
                {
                    show:
                        Permissions.checkPermission(Permissions.SimulationUpdate) &&
                        Permissions.checkPermission(Permissions.SimulationTaskList) &&
                        Permissions.checkPermission(Permissions.SimulationTaskGet) &&
                        Permissions.checkPermission(Permissions.SimulationTaskCreate) &&
                        Permissions.checkPermission(Permissions.SimulationTaskUpdate) &&
                        Permissions.checkPermission(Permissions.SimulationTaskDelete) &&
                        Permissions.checkPermission(Permissions.SimulationTaskBulkAdd) &&
                        Permissions.checkPermission(Permissions.SimulationTaskBulkResult),
                    tabKey: 'graph',
                    title: 'Сеть',
                    getRoute: () => {
                        return `/constructor/${simId}/graph`;
                    },
                },
                {
                    show:
                        Permissions.checkPermission(Permissions.SimulationGet) &&
                        Permissions.checkPermission(Permissions.SimulationTaskList) &&
                        Permissions.checkPermission(Permissions.SimulationTaskGet) &&
                        Permissions.checkPermission(Permissions.SimulationWorkerList) &&
                        Permissions.checkPermission(Permissions.SimulationWorkerGet),
                    tabKey: 'gantt',
                    title: 'Гант',
                    getRoute: () => {
                        return `/constructor/${simId}/gantt`;
                    },
                },
                {
                    show:
                        Permissions.checkPermission(Permissions.SimulationTaskCreate) &&
                        Permissions.checkPermission(Permissions.SimulationTaskList) &&
                        Permissions.checkPermission(Permissions.SimulationTaskGet) &&
                        Permissions.checkPermission(Permissions.SimulationTaskRestore),
                    tabKey: 'nodes',
                    title: 'Узлы',
                    getRoute: () => {
                        return `/constructor/${simId}/nodes`;
                    },
                },
                {
                    show:
                        Permissions.checkPermission(Permissions.SimulationEventCreate) &&
                        Permissions.checkPermission(Permissions.SimulationEventList) &&
                        Permissions.checkPermission(Permissions.SimulationEventGet) &&
                        Permissions.checkPermission(Permissions.SimulationEventRestore),
                    tabKey: 'events',
                    title: 'События',
                    getRoute: () => {
                        return `/constructor/${simId}/events`;
                    },
                },
                {
                    show:
                        Permissions.checkPermission(Permissions.SimulationWorkerCreate) &&
                        Permissions.checkPermission(Permissions.SimulationWorkerList) &&
                        Permissions.checkPermission(Permissions.SimulationWorkerGet) &&
                        Permissions.checkPermission(Permissions.SimulationWorkerRestore),
                    tabKey: 'workers',
                    title: 'ПШЕ',
                    getRoute: () => {
                        return `/constructor/${simId}/workers`;
                    },
                },
                {
                    show: true,
                    tabKey: 'schedule',
                    title: 'Календарь',
                    getRoute: () => {
                        return `/constructor/${simId}/schedule-events`;
                    },
                },
            ],
            management: [
                {
                    show: Permissions.checkPermission(Permissions.UserList),
                    tabKey: 'users',
                    title: 'Пользователи',
                    getRoute: () => {
                        return '/management/users';
                    },
                },
                {
                    show: Permissions.checkPermission(Permissions.InvitationList),
                    tabKey: 'invitations',
                    title: 'Приглашения',
                    getRoute: () => {
                        return '/management/invitations';
                    },
                },
                {
                    show:
                        Permissions.checkPermission(Permissions.RoleList) &&
                        Permissions.checkPermission(Permissions.PermissionCategoryList),
                    tabKey: 'roles',
                    title: 'Роли',
                    getRoute: () => {
                        return '/management/roles';
                    },
                },
                {
                    show: Permissions.checkPermission(Permissions.FilterList),
                    tabKey: 'filters',
                    title: 'Фильтры',
                    getRoute: () => {
                        return '/management/filters';
                    },
                },
            ],
            controls: [
                {
                    show: Permissions.checkPermission(Permissions.SessionList),
                    tabKey: 'sessions',
                    title: 'Сессии',
                    getRoute: () => {
                        return '/controls/sessions';
                    },
                },
                {
                    show: Permissions.checkPermission(Permissions.SessionAssignmentList),
                    tabKey: 'assignments',
                    title: 'Назначения',
                    getRoute: () => {
                        return '/controls/assignments';
                    },
                },
            ],
            dev: [
                {
                    show: true,
                    tabKey: 'fonts',
                    title: 'Шрифты',
                    getRoute: () => {
                        return '/tests/fonts';
                    },
                },
                {
                    show: true,
                    tabKey: 'colors',
                    title: 'Палитра',
                    getRoute: () => {
                        return '/tests/colors';
                    },
                },
                {
                    show: true,
                    tabKey: 'table-statuses',
                    title: 'Табличные статусы',
                    getRoute: () => {
                        return '/tests/table-statuses';
                    },
                },
                {
                    show: true,
                    tabKey: 'stores',
                    title: 'Хранилище',
                    getRoute: () => {
                        return '/tests/stores';
                    },
                },
                {
                    show: true,
                    tabKey: 'network',
                    title: 'API',
                    getRoute: () => {
                        return '/tests/network';
                    },
                },
            ],
        };

        useEffect(() => {
            if (tabSet != rootStore.ingameStore.prevTabSet) {
                rootStore.ingameStore.topMenuScrollPosition = 0;
                rootStore.ingameStore.prevTabSet = tabSet;
            }
        }, []);

        useEffect(() => {
            if (buttonGroupRef?.current != null && buttonGroupRef?.current != undefined) {
                buttonGroupRef.current.scrollLeft = rootStore.ingameStore.topMenuScrollPosition;
            }
        }, [window.location.href, buttonGroupRef.current]);

        return topBarText == null ? (
            <Row
                className={`top-bar spaced${rootStore.ingameStore.sideBarMinimized ? ' sbm' : ''}`}
            >
                <Col className="button-group">
                    {tabSet !== null && (
                        <Row
                            className="p3"
                            ref={buttonGroupRef}
                            onMouseDown={onMouseDown}
                            onScroll={(e) => {
                                rootStore.ingameStore.topMenuScrollPosition = (
                                    e.nativeEvent.target as HTMLDivElement
                                ).scrollLeft;
                            }}
                        >
                            {tabs[tabSet]
                                .filter((t) => t.show)
                                .map((tabItem) => {
                                    return (
                                        <Button
                                            key={tabItem.tabKey}
                                            className={
                                                activeTab == tabItem.tabKey ? 'active' : 'default'
                                            }
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                if (tabItem.show) {
                                                    navigate(tabItem.getRoute());
                                                }
                                            }}
                                        >
                                            {tabItem.title}
                                        </Button>
                                    );
                                })}
                        </Row>
                    )}
                    {tabSet == null && activeTab != null && <h3>{activeTab}</h3>}
                </Col>
                <Col className={showSearch ? 'search-group' : 'search-group-disabled'}>
                    <Row>
                        <Col className="search">
                            {/*<TextField
                            label="Поиск"
                            id="search-input"
                            className="p2"
                            value={state.searchValue}
                            onChange={(e) => state.searchValue = e.target.value}
                            slotProps={{
                                input: {
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <div className="search-icon" />
                                        </InputAdornment>
                                    ),
                                },
                            }}
                        />*/}
                            <Search
                                allowClear
                                className="p2 search-input"
                                disabled
                                onChange={(e) => (state.searchValue = e.target.value)}
                                placeholder="Поиск"
                                value={state.searchValue}
                            />
                        </Col>
                        <Col className="filters">
                            {/*<Button 
                            className='filter-button desc-s'
                            onClick={() => console.log("filter drawer should open")}
                            startIcon={<div className='filter-button-icon' />}
                            variant="contained" 
                        >
                            Фильтры
                        </Button>*/}
                            <Button
                                className="filter-button desc-s"
                                disabled
                                icon={<div className="filter-button-icon" />}
                                onClick={() => console.log('filter drawer should open')}
                            >
                                Фильтры
                            </Button>
                        </Col>
                        <Col className="view-settings"></Col>
                    </Row>
                </Col>
                {!showSearch && (
                    <Col
                        className="user-name p2"
                        style={{ padding: '0 12px' }}
                    >
                        {rootStore.currentUserStore.getUser?.name}
                    </Col>
                )}
            </Row>
        ) : (
            <Row
                className={`top-bar spaced${rootStore.ingameStore.sideBarMinimized ? ' sbm' : ''}`}
            >
                <Col>
                    <h4>{topBarText}</h4>
                </Col>
                {!showSearch && (
                    <Col
                        className="user-name p2"
                        style={{ padding: '0 12px' }}
                    >
                        {rootStore.currentUserStore.getUser?.name}
                    </Col>
                )}
            </Row>
        );
    },
);

export { TopBar };
