import { CRMAPIManager } from '@api/crmApiManager';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimulation } from 'types/simulation/simulation';
import { Col, Row, Button, message, Collapse } from 'antd';
import { Permissions } from 'src/shared/permissions';
import { TUser } from 'types/user/user';
import { SettingsManager } from 'src/shared/settingsManager';
import { rootStore } from 'src/store/instanse';
import { UserResp } from '@api/responseModels/users/userResp';
import { Common } from 'src/shared/common';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { SessionResp } from '@api/responseModels/sessions/sessionResponse';
import { observer } from 'mobx-react';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSimEvent } from 'types/simulation/simulationEvent';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { Loader } from '@components/ui/loader/loader';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { SimEventListResp } from '@api/responseModels/simulations/simulationEvents/simulationEventListResponse';
import { Gantt, GanttUseCases, TSimTaskGanttExtend } from '@components/gantt/gantt';
import { DefaultTimeSettings } from 'src/store/ingame/data';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';

import './simulationProfile.scss';

type TState = {
    collapseItems: Array<{
        key: string;
        label: React.ReactNode;
        children: React.ReactNode;
    }>;
    creator: TUser | null;
    events: TSimEvent[];
    isLoading: boolean;
    sim: TSimulation;
    tasks: TSimTaskGanttExtend[];
    workers: TSimWorker[];
};

const SimulationProfile = observer((): JSX.Element => {
    const state = useReactive<TState>({
        collapseItems: [],
        creator: null,
        events: [],
        isLoading: false,
        sim: null,
        tasks: [],
        workers: [],
    });
    const { simId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();

    async function loadSim() {
        if (!Permissions.checkPermission(Permissions.SimulationGet)) {
            navigate('/lk');
            message.error('Недостаточно прав для просмотра списка симуляций');
            return;
        }
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(Number(simId));
            });
            if (sim.errorMessages) throw sim.errorMessages;
            state.sim = sim.data.data;
            if (sim.data.data?.creator == SettingsManager.getConnectionCredentials()?.user_id) {
                state.creator = rootStore.currentUserStore.getUser;
            } else {
                const loadCreator = await CRMAPIManager.request<UserResp>(async (api) => {
                    return await api.getUser(sim.data.data?.creator);
                });
                if (loadCreator.errorMessages) throw loadCreator.errorMessages;
                state.creator = loadCreator.data.data;
            }
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            message.error('Ошибка при загрузке симуляции или её создателя');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadTaskList() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.request<SimTaskListResp>(async (api) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.tasks = tl.data.data
                .sort((a, b) => a.id - b.id)
                .map((ti) => {
                    return { ...ti, curDuration: ti.est_duration, workers: [] };
                });
        } catch (errors) {
            messageApi.error('Ошибка при загрузке списка узлов');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadWorkerList() {
        state.isLoading = true;
        try {
            const wl = await CRMAPIManager.request<SimWorkerListResp>(async (api) => {
                return await api.getSimWorkerList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (wl.errorMessages) throw wl.errorMessages;
            state.workers = wl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке списка ПШЕ');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadEventList() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.request<SimEventListResp>(async (api) => {
                return await api.getSimEventList({
                    simulation_id: +simId,
                    query: '',
                    page: 1,
                    per_page: 100,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.events = tl.data.data;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка событий');
            console.log(err);
        }
        state.isLoading = false;
    }

    function onTaskOpen(task_uid: TSimTask['uid']) {
        const task = state.tasks.find((ti) => ti.uid == task_uid);
        if (task == undefined) {
            messageApi.error('Не удалось найти задачу для перехода');
            return;
        }
        navigate(`/constructor/${simId}/nodes/${task.id}`);
    }

    async function initSimProfile() {
        if (simId == null || simId == undefined || Number.isNaN(+simId)) {
            navigate('/simulations');
            return;
        }
        await loadSim();
        if (state.sim == null) return;
        const tempCollapseItems: (typeof state)['collapseItems'] = [];
        if (
            Permissions.checkPermission(Permissions.SimulationTaskList) &&
            state.sim?.deleted_at == null
        ) {
            await loadTaskList();
            if (
                state.tasks.length > 0 &&
                state.sim?.first_task != null &&
                state.sim?.last_task != null
            ) {
                tempCollapseItems.push({
                    key: 'gantt',
                    label: <h6>Гант</h6>,
                    children: (
                        <div className="collapse-gantt-container">
                            <Gantt
                                className="sim-profile"
                                daysInAWeek={DefaultTimeSettings.daysInAWeek}
                                onTableEdit={null}
                                onTaskOpen={onTaskOpen}
                                onWorkerAssign={null}
                                onWorkerUnassign={null}
                                simulation={state.sim}
                                tasks={state.tasks}
                                useCase={GanttUseCases.Constructor}
                                workers={[]}
                            />
                        </div>
                    ),
                });
            }
            tempCollapseItems.push({
                key: 'tasks',
                label: <h6>Узлы</h6>,
                children: (
                    <Row
                        style={{ gap: '8px' }}
                        wrap
                    >
                        {state.tasks.map((t) => {
                            return (
                                <Col
                                    key={t.id}
                                    className="link-task-card"
                                    onClick={() => {
                                        if (
                                            Permissions.checkPermission(
                                                Permissions.SimulationTaskUpdate,
                                            )
                                        ) {
                                            navigate(`/constructor/${simId}/nodes/${t.id}`);
                                        }
                                    }}
                                >
                                    <Row style={{ justifyContent: 'space-between' }}>
                                        <Col className="desc-s-strong grayish">{`ID ${t.id}`}</Col>
                                        <Col className="desc-s">{`${t.est_duration} дн`}</Col>
                                    </Row>
                                    <Row
                                        className="desc-s"
                                        style={{ justifyContent: 'space-between' }}
                                    >
                                        <Col>{`${t.est_workers} чел`}</Col>
                                        <Col>{`${Math.ceil(t.est_budget / 1000)} тыс`}</Col>
                                    </Row>
                                </Col>
                            );
                        })}
                        {state.tasks.length == 0 && <span className="p3 gray">Отсутствуют</span>}
                    </Row>
                ),
            });
        }
        if (
            Permissions.checkPermission(Permissions.SimulationWorkerList) &&
            state.sim?.deleted_at == null
        ) {
            await loadWorkerList();
            tempCollapseItems.push({
                key: 'workers',
                label: <h6>ПШЕ</h6>,
                children: (
                    <Row
                        style={{ gap: '8px' }}
                        wrap
                    >
                        {state.workers.map((w) => {
                            return (
                                <Col
                                    key={w.id}
                                    className="link-worker-card"
                                    onClick={() => {
                                        if (
                                            Permissions.checkPermission(
                                                Permissions.SimulationWorkerUpdate,
                                            )
                                        ) {
                                            navigate(`/constructor/${simId}/workers/${w.uid}`);
                                        }
                                    }}
                                >
                                    <Row style={{ justifyContent: 'space-between' }}>
                                        <Col className="desc-s-strong grayish">{w.name}</Col>
                                        <Col className="desc-s">{`${w.project_percentage}%`}</Col>
                                    </Row>
                                    <Row
                                        className="desc-s"
                                        style={{ justifyContent: 'space-between' }}
                                    >
                                        <Col>{`${w.hourly_rate}/ч`}</Col>
                                        <Col>{w.base_stats.join('/')}</Col>
                                    </Row>
                                </Col>
                            );
                        })}
                        {state.tasks.length == 0 && <span className="p3 gray">Отсутствуют</span>}
                    </Row>
                ),
            });
        }
        if (
            Permissions.checkPermission(Permissions.SimulationEventList) &&
            state.sim?.deleted_at == null
        ) {
            await loadEventList();
            tempCollapseItems.push({
                key: 'events',
                label: <h6>События</h6>,
                children: (
                    <Row
                        style={{ gap: '8px' }}
                        wrap
                    >
                        {state.events.map((e) => {
                            return (
                                <Col
                                    key={e.id}
                                    className="link-event-card"
                                    onClick={() => {
                                        if (
                                            Permissions.checkPermission(
                                                Permissions.SimulationEventUpdate,
                                            )
                                        ) {
                                            navigate(`/constructor/${simId}/events/${e.uid}`);
                                        }
                                    }}
                                >
                                    <Row style={{ justifyContent: 'space-between' }}>
                                        <Col className="desc-s-strong grayish">{`ID ${e.id}`}</Col>
                                        <Col className="desc-s">
                                            {`${e.trigger_type == 'day' ? 'день' : 'узел'} ${e.trigger_arg}`}
                                        </Col>
                                    </Row>
                                    <span className="desc-s">{`Опций: ${e.options.length}`}</span>
                                </Col>
                            );
                        })}
                        {state.events.length == 0 && <span className="p3 gray">Отсутствуют</span>}
                    </Row>
                ),
            });
        }
        if (Permissions.checkPermission(Permissions.SimulationTest)) {
            tempCollapseItems.push({
                key: 'test',
                label: <h6>Тест</h6>,
                children: <span className="p3 gray">Отсутствует</span>,
            });
        }
        state.collapseItems = tempCollapseItems;
    }

    useEffect(() => {
        initSimProfile();
    }, []);

    async function createSession() {
        state.isLoading = true;
        try {
            const session = await CRMAPIManager.request<SessionResp>(async (api) => {
                return await api.createSession(state.sim?.id);
            });
            if (session.errorMessages) throw session.errorMessages;
            navigate(`/controls/sessions/${session.data.data?.id}`);
        } catch (errors) {
            messageApi.error('Ошибка при создании сессии');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function getCreatorName() {
        return state.creator?.name == null || state.creator?.name.length == 0
            ? '-'
            : state.creator?.name;
    }

    async function restoreSim() {
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.restoreSimulation(state.sim.id);
            });
            if (result.errorMessages) throw result.errorMessages;
            await initSimProfile();
            messageApi.success('Симуляция восстановлена');
        } catch (errors) {
            messageApi.destroy();
            messageApi.error('Ошибка при восстановлении');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function calcActiveTab() {
        if (state.sim?.archived || state.sim?.deleted_at != null) {
            return 'archive';
        }
        if (state.sim?.finished && !state.sim?.archived && state.sim?.deleted_at == null) {
            return 'finished';
        }
        if (!state.sim?.finished && !state.sim?.archived && state.sim?.deleted_at == null) {
            return 'unfinished';
        }
        return 'all';
    }

    return (
        <UniLayout
            activeTab={calcActiveTab()}
            additionalClass="profile-min-width"
            tabSet="simulations"
        >
            <div className="sim-profile">
                {state.isLoading && <Loader />}
                {contextHolder}
                <div className="sim-card">
                    <Row
                        className="id-row"
                        justify={'space-between'}
                        style={{ columnGap: '20px' }}
                        wrap={false}
                    >
                        <Col style={{ minWidth: '100px' }}>
                            <Row>
                                <h3>ID {state.sim?.id}</h3>
                                <CopyButton
                                    textToCopy={`ID симуляции: ${state.sim?.id}`}
                                    textToShow="ID симуляции скопирован"
                                    size={36}
                                />
                            </Row>
                        </Col>
                        <Col
                            className="filters desc-l"
                            style={{
                                maxWidth: '768px',
                            }}
                        >
                            <Row style={{ gap: '8px 16px' }}>
                                {state.sim?.filters.map((f) => {
                                    return (
                                        <FilterButton
                                            key={`filter-${state.sim?.id}-${f.id}`}
                                            hex={f.colorHEX}
                                            text={f.name}
                                        />
                                    );
                                })}
                                {state.sim?.filters.length == 0 && (
                                    <span className="p2">Нет фильтров</span>
                                )}
                            </Row>
                        </Col>
                    </Row>
                    <Row>
                        <div className="p1">{state.sim?.name}</div>
                        <CopyButton
                            textToCopy={`Симуляция \"${state.sim?.name}\"`}
                            textToShow="Название симуляции скопировано"
                            size={26}
                        />
                    </Row>
                    <Row>
                        <div className="description desc-l">{state.sim?.description}</div>
                    </Row>
                    <Row className="info-row">
                        <Col>
                            <Row>
                                <Col className="labeled-input">
                                    <Row>
                                        <span className="p3">Создатель:</span>
                                    </Row>
                                    <Row
                                        className="creator-row"
                                        onClick={() => {
                                            navigate(`/management/users/${state.creator?.id}`);
                                        }}
                                    >
                                        <Col className="creator-avatar">
                                            <div className="p1-strong">{getCreatorName()[0]}</div>
                                        </Col>
                                        <Col className="p3 creator-name">
                                            <Button
                                                className="creator-btn"
                                                type="link"
                                            >
                                                {getCreatorName()}
                                            </Button>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                        <Col>
                            <Row>
                                <Col className="labeled-input">
                                    <Row>
                                        <span className="p3">Последнее изменение:</span>
                                    </Row>
                                    <Row>
                                        <span className="p3 lighter-tone">
                                            {Common.formatDateString(state.sim?.updated_at)}
                                        </span>
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    {state.collapseItems.length > 0 && (
                        <Row className="dropdowns">
                            <Col>
                                <Collapse
                                    expandIcon={() => <div className="expand-icon" />}
                                    expandIconPosition="end"
                                    items={state.collapseItems}
                                />
                            </Col>
                        </Row>
                    )}
                    <Row className="controls p2">
                        {Permissions.checkPermission(Permissions.SimulationUpdate) &&
                            state.sim?.deleted_at == null && (
                                <Button
                                    onClick={() => {
                                        navigate(`/constructor/${state.sim?.id}`);
                                    }}
                                >
                                    Открыть в конструкторе
                                </Button>
                            )}
                        {Permissions.checkPermission(Permissions.SessionList) &&
                            state.sim?.deleted_at == null &&
                            !state.sim?.archived &&
                            state.sim?.published && (
                                <Button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        navigate(`/controls/sessions?sim_id=${state.sim?.id}`);
                                    }}
                                >
                                    Сессии
                                </Button>
                            )}
                        {Permissions.checkPermission(Permissions.SessionCreate) &&
                            state.sim?.deleted_at == null &&
                            !state.sim?.archived &&
                            state.sim?.published && (
                                <Button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        createSession();
                                    }}
                                >
                                    Создать сессию
                                </Button>
                            )}

                        {Permissions.checkPermission(Permissions.SimulationRestore) &&
                            state.sim?.deleted_at != null && (
                                <Button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        restoreSim();
                                    }}
                                >
                                    Восстановить
                                </Button>
                            )}
                    </Row>
                </div>
            </div>
        </UniLayout>
    );
});

export default SimulationProfile;
