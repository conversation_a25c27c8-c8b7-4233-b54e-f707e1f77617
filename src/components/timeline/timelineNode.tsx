import Colors from 'src/shared/colors';
import { GanttNodeInner } from '@components/gantt/gantt';
import { Col, Row } from 'antd';
import { memo } from 'react';
import { TSimTask } from 'types/simulation/simulationTask';

type TimelineNodeProps = {
    data: GanttNodeInner & {
        onTaskClick: (task_uid: TSimTask['uid']) => void | null;
        type: 'task' | 'free';
    };
};

export default memo(({ data }: TimelineNodeProps) => {
    return (
        <div
            className="timeline-node-wrapper"
            style={{
                backgroundColor:
                    data.type == 'task'
                        ? data.milestone
                            ? Colors.Error.warm[400]
                            : Colors.Accent.warm[200]
                        : Colors.Neutral[100],
                backgroundImage:
                    data.type == 'task'
                        ? `linear-gradient(${data.milestone ? Colors.Error.cold[800] : Colors.Accent.warm[500]})`
                        : 'none',
                backgroundPosition: data.type == 'task' ? 'left top' : '0% 0%',
                backgroundRepeat: 'no-repeat',
                backgroundSize:
                    data.type == 'task' ? `${Math.floor(data.progress * 100)}% 100%` : 'auto auto',
                color: data.milestone ? Colors.Neutral[0] : Colors.Neutral[950],
            }}
        >
            <div className={`timeline-node-inner ${data.type}`}>
                {data.type == 'task' && (
                    <Row
                        className="p2-strong task-number"
                        onClick={() => data.onTaskClick(data.uid)}
                    >
                        <Col>{data.name}</Col>
                    </Row>
                )}
            </div>
        </div>
    );
});
