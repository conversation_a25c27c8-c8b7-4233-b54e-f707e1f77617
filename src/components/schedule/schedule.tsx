import { useReactive } from 'ahooks';
import { Col, Row } from 'antd';
import { useCallback, useRef } from 'react';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { TScheduleEventType, TSimScheduleEvent } from 'types/simulation/simulationScheduleEvent';
import { TSimWorker } from 'types/simulation/simulationWorker';

import './schedule.scss';
import { useParams } from 'react-router-dom';
import { Common } from 'src/shared/common';
import { SimScheduleEventModal } from './simScheduleEventModal';
import { WeekdayStringsFull } from 'src/store/ingame/data';

type TScheduleChange = TSimScheduleEvent & {
    change_type: 'add' | 'update' | 'remove';
};

type TProps = {
    changes?: { [key: TSimScheduleEvent['id']]: TScheduleChange };
    currentDay?: number;
    dayEndHour: number;
    daysInAWeek: number;
    dayStartHour: number;
    disableEdit: boolean;
    onSSETadd?: (item: TSimScheduleEvent) => Promise<boolean>;
    onSSETchange?: (item: TSimScheduleEvent) => Promise<boolean>;
    onSSETdelete: (id: TSimScheduleEvent['id']) => Promise<void>;
    scheduleEventTypeList: TScheduleEventType[];
    simScheduleEventList: TSimScheduleEvent[];
    totalWeeks: number;
    useCase: 'Default' | 'Ingame';
    workerList: TSimWorker[] | TSessionWorkerExtended[];
};

type TState = {
    simEventModalOpen: boolean;
    simEventModalUseCase: 'new' | 'edit';
    selectedSimEvent: TSimScheduleEvent | null;
    selectedWeek: number;
};

const Schedule = ({
    changes,
    currentDay,
    dayEndHour,
    daysInAWeek,
    dayStartHour,
    disableEdit,
    onSSETadd,
    onSSETchange,
    onSSETdelete,
    scheduleEventTypeList,
    simScheduleEventList,
    totalWeeks,
    useCase,
    workerList,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        simEventModalOpen: false,
        simEventModalUseCase: 'new',
        selectedSimEvent: null,
        selectedWeek: 1,
    });
    const { simId } = useParams();
    const daysRowRef = useRef<HTMLDivElement>(null);

    const makeChangeStats = useCallback(() => {
        const weeklyStats = [];
        for (let i = 0; i < totalWeeks; i++) {
            weeklyStats.push({ added: 0, updated: 0, removed: 0 });
        }
        if (changes == undefined) return weeklyStats;
        for (let i = 0; i < Object.keys(changes).length; i++) {
            const tChange = changes[Object.keys(changes)[i]];
            switch (tChange.change_type) {
                case 'add': {
                    weeklyStats[tChange.week - 1].added += 1;
                    break;
                }
                case 'update': {
                    weeklyStats[tChange.week - 1].updated += 1;
                    break;
                }
                case 'remove': {
                    weeklyStats[tChange.week - 1].removed += 1;
                    break;
                }
            }
        }
        return weeklyStats;
    }, [changes, totalWeeks]);

    const makeHourNotations = useCallback(() => {
        const hourNotations = [];
        for (let i = dayStartHour; i <= dayEndHour; i++) {
            if (i == dayStartHour || i == dayEndHour || i % 2 == 0) {
                hourNotations.push(`${i < 10 ? '0' : ''}${i}:00`);
            }
        }
        return hourNotations;
    }, [dayStartHour, dayEndHour]);

    const makeEventList = useCallback(() => {
        const tempList: TSimScheduleEvent[] = [];
        if (useCase == 'Ingame') {
            return simScheduleEventList.filter((li) => li.week == state.selectedWeek);
        } else {
            for (let i = 0; i < simScheduleEventList.length; i++) {
                const SSETi = simScheduleEventList[i];
                if (SSETi.week != state.selectedWeek) continue;
                const SSETiChange = Object.values(changes).find((chi) => chi.id == SSETi.id);
                if (SSETiChange == undefined) {
                    tempList.push(SSETi);
                } else {
                    if (SSETiChange.change_type == 'remove') continue;
                    else {
                        tempList.push({
                            ...SSETi,
                            ...(SSETiChange as TSimScheduleEvent),
                        });
                    }
                }
            }
            for (let i = 0; i < Object.values(changes).length; i++) {
                const tChange = Object.values(changes)[i];
                if (tChange.week != state.selectedWeek || tChange.change_type != 'add') {
                    continue;
                } else {
                    tempList.push(tChange as TSimScheduleEvent);
                }
            }
            return tempList;
        }
    }, [
        simScheduleEventList,
        state.selectedWeek,
        changes,
    ]);

    const makeDaySlot = useCallback(
        (dayIndex: number, hourIndex: number) => {
            const tempList = makeEventList();
            const targetEvent = tempList.find(
                (tli) =>
                    tli.week == state.selectedWeek &&
                    tli.day == dayIndex + 1 &&
                    tli.start == hourIndex + dayStartHour,
            );
            if (targetEvent != undefined) {
                const targetEventType =
                    targetEvent.event_type_id != null
                        ? scheduleEventTypeList.find((seti) => seti.id == targetEvent.event_type_id)
                        : null;
                const targetEventWorker =
                    targetEvent.event_worker_uid != null
                        ? workerList.find(
                              (wli) =>
                                  (useCase == 'Default' &&
                                      (wli as TSimWorker).uid == targetEvent.event_worker_uid) ||
                                  (useCase == 'Ingame' &&
                                      (wli as TSessionWorkerExtended).worker_uid ==
                                          targetEvent.event_worker_uid),
                          )
                        : null;
                let eventName = '';
                if (targetEvent.name_override != null) {
                    eventName = targetEvent.name_override;
                } else {
                    if (targetEventType != null) {
                        eventName = targetEventType.name;
                    }
                    if (targetEventWorker != null) {
                        eventName += ' ' + targetEventWorker.name;
                    }
                }
                return (
                    <div
                        className={`filled-slot-inner${targetEvent.locked ? ' locked-slot' : ''}`}
                        onClick={(e) => {
                            e.stopPropagation();
                            state.selectedSimEvent = targetEvent;
                            state.simEventModalUseCase = 'edit';
                            state.simEventModalOpen = true;
                        }}
                        style={{
                            height: `${
                                targetEvent.duration == null && targetEventType != null
                                    ? targetEventType.duration * 48
                                    : targetEvent.duration * 48
                            }px`,
                        }}
                    >
                        <Row className="colored-divider" />
                        <Row className="desc-l event-time">
                            {`${targetEvent.start < 10 ? '0' : ''}${targetEvent.start}:00 - ${
                                targetEvent.end < 10 ? '0' : ''
                            }${targetEvent.end}:00`}
                        </Row>
                        <Row className="p3 event-name">{eventName}</Row>
                    </div>
                );
            } else {
                return (
                    <div
                        className="empty-slot-inner"
                        onClick={(e) => {
                            e.stopPropagation();
                            if (disableEdit) return;
                            state.selectedSimEvent = makeNewSimEvent(dayIndex, hourIndex);
                            state.simEventModalUseCase = 'new';
                            state.simEventModalOpen = true;
                        }}
                    ></div>
                );
            }
        },
        [
            simScheduleEventList,
            state.selectedWeek,
            changes,
        ],
    );

    function makeNewSimEvent(dayIndex: number, hourIndex: number): TSimScheduleEvent {
        let maxId = 0;
        for (let i = 0; i < simScheduleEventList.length; i++) {
            if (simScheduleEventList[i].id > maxId) {
                maxId = simScheduleEventList[i].id;
            }
        }
        for (let i = 0; i < Object.values(changes).length; i++) {
            if (Object.values(changes)[i].id > maxId) {
                maxId = Object.values(changes)[i].id;
            }
        }
        return {
            simulation_id: +simId,
            id: maxId + 1,
            event_type_id: null,
            event_worker_uid: null,
            week: state.selectedWeek,
            day: dayIndex + 1,
            start: hourIndex + dayStartHour,
            duration: 1,
            end: hourIndex + dayStartHour + 1,
            locked: true,
            name_override: null,
            created_at: Common.dateNowString(),
            updated_at: null,
        };
    }

    async function onSimScheduleEventDelete() {
        await onSSETdelete(state.selectedSimEvent.id);
        state.simEventModalOpen = false;
        state.selectedSimEvent = null;
    }

    async function onSimScheduleEventSave(changedSSE: TSimScheduleEvent) {
        let result = false;
        if (state.simEventModalUseCase == 'new') {
            result = await onSSETadd(changedSSE);
        } else {
            result = await onSSETchange(changedSSE);
        }
        if (result) {
            state.simEventModalOpen = false;
            state.selectedSimEvent = null;
        }
    }

    return (
        <Row className={`custom-schedule${useCase == 'Ingame' ? '-ingame' : ''}`}>
            {state.simEventModalOpen && state.selectedSimEvent != null && (
                <SimScheduleEventModal
                    disableEdit={disableEdit}
                    isOpen={state.simEventModalOpen}
                    onDelete={onSimScheduleEventDelete}
                    onSave={onSimScheduleEventSave}
                    scheduleEventTypeList={scheduleEventTypeList}
                    selectedSimEvent={state.selectedSimEvent}
                    setIsOpen={(isOpen: boolean) => {
                        if (!isOpen) {
                            state.simEventModalOpen = false;
                            state.selectedSimEvent = null;
                        }
                    }}
                    useCase={state.simEventModalUseCase}
                    workers={workerList as TSimWorker[]}
                />
            )}
            <Col className="hours-col">
                {makeHourNotations().map((v, i) => (
                    <Row
                        key={`hour-notation-${i}`}
                        className="p3 hour-notation"
                    >
                        {v}
                    </Row>
                ))}
            </Col>
            <Col
                className="main-col"
                flex={1}
            >
                <Row
                    className="days-row"
                    ref={daysRowRef}
                >
                    {Array.from({ length: daysInAWeek }).map((_, i) => (
                        <Col
                            key={`day-col-${i}`}
                            className="day-col"
                            style={{
                                width: `${Math.floor(100 / daysInAWeek)}%`,
                            }}
                        >
                            <Row className="p2 day-name">{WeekdayStringsFull[i]}</Row>
                            {Array.from({ length: dayEndHour - dayStartHour }).map((_, j) => (
                                <Row
                                    key={`time-slot-${i}-${j}`}
                                    className="day-slot"
                                >
                                    {makeDaySlot(i, j)}
                                </Row>
                            ))}
                        </Col>
                    ))}
                </Row>
                <Row className="week-selector-row">
                    {totalWeeks != null &&
                        Array.from({ length: totalWeeks }).map((_, i) => (
                            <Col
                                key={`week-col-${i}`}
                                className={`week-col${
                                    state.selectedWeek == i + 1 ? '-selected' : ''
                                }`}
                                flex={1}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    if (state.selectedWeek != i + 1) {
                                        state.selectedWeek = i + 1;
                                    }
                                }}
                            >
                                <Row className="p2 week-number">{i + 1}</Row>
                                {useCase == 'Default' && (
                                    <Row className="desc-l week-stats">
                                        <Col className="week-added">
                                            {makeChangeStats()[i].added > 0
                                                ? `+${makeChangeStats()[i].added}`
                                                : ''}
                                        </Col>
                                        <Col className="week-updated">
                                            {makeChangeStats()[i].updated > 0
                                                ? `~${makeChangeStats()[i].updated}`
                                                : ''}
                                        </Col>
                                        <Col className="week-removed">
                                            {makeChangeStats()[i].removed > 0
                                                ? `-${makeChangeStats()[i].removed}`
                                                : ''}
                                        </Col>
                                    </Row>
                                )}
                                {useCase == 'Ingame' &&
                                    Math.floor(currentDay / daysInAWeek) == currentDay && (
                                        <Row className="p3 week-current">Текущая</Row>
                                    )}
                            </Col>
                        ))}
                </Row>
            </Col>
        </Row>
    );
};

export { Schedule };
