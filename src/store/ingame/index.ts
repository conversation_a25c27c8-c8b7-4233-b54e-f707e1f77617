import { makeAutoObservable } from 'mobx';
import {
    IngamePermissions,
    IngameTaskSWAlinks,
    IngameTimeSettings,
    IngameTotalSWAlinks,
    IngameWorkerSWAlinks,
    SWAlinkStatus,
    TransportStatus,
} from 'types/ingame';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import {
    AllSWAPermissions,
    DefaultNoPermissions,
    DefaultSessionEvents,
    DefaultTimeSettings,
    DefaultTransportStatus,
    NoStatePermissions,
    NoSWAPermissions,
    WeekdayStringsFull,
    WeekdayStringsShort,
} from './data';
import { TSessionAssignmentInfo } from 'types/session/sessionAssignmentInfo';
import { TSessionTask, TSessionTaskExtended } from 'types/session/sessionTask';
import { TSessionWorker, TSessionWorkerExtended } from 'types/session/sessionWorker';
import { message } from 'antd';
import { CRMAPIManager } from '@api/crmApiManager';
import { SessionAssignmentResp } from '@api/responseModels/sessionAssignments/sessionAssignmentResponse';
import { SWExtListResp } from '@api/responseModels/sessionAssignmentInfo/sessionWorkerListExtendedResponse';
import { STExtListResp } from '@api/responseModels/sessionAssignmentInfo/sessionTaskListExtendedResponse';
import { SettingsManager } from 'src/shared/settingsManager';
import { SAIResp } from '@api/responseModels/sessionAssignmentInfo/sessionAssignmentInfoResponse';
import { CWTResp } from '@api/responseModels/sessionAssignmentInfo/currentWorkerTasksResponse';
import { CTWResp } from '@api/responseModels/sessionAssignmentInfo/currentTaskWorkersResponse';
import { SessionWorkerAssignmentResp } from '@api/responseModels/sessionWorkerAssignments/sessionWorkerAssignmentResponse';
import { TSessionWorkerAssignment } from 'types/session/sessionWorkerAssignment';
import { SessionWorkerAssignmentListResp } from '@api/responseModels/sessionWorkerAssignments/sessionWorkerAssignmentListResponse';
import { TSession } from 'types/session/session';
import { TSimulation } from 'types/simulation/simulation';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { TSessionBudget, TSessionBudgetRecord } from 'types/session/sessionBudget';
import { rootStore } from 'src/store/instanse';
import { Permissions } from 'src/shared/permissions';
import { TSessionEvent } from 'types/session/sessionEvent';
import { STPResp } from '@api/responseModels/sessionAssignmentInfo/sesssionTaskProgressResponse';
import { TSessionTaskProgress } from 'types/session/sessionTaskProgress';

class IngameStore {
    public sideBarMinimized = true;
    public topMenuScrollPosition = 0;
    public leftMenuScrollPosition = 0;
    public prevTabSet: string = null;
    /** Состояние HTTP-транспорта */
    public httpState: 'error' | 'loading' | 'done' = 'done';
    /** Verbose-режим для дебага */
    public verbose = false;
    /** Несущий объект назначения */
    public sessionAssignment: TSessionAssignment = null;
    /** Состояние сетевой компоненты */
    private transportStatus: TransportStatus = DefaultTransportStatus;
    /** Имеет ли менеджер полный функционал */
    private managerManipulationAllowed = false;
    /** Права на возможности интерфейса */
    private ingamePermissions: IngamePermissions = DefaultNoPermissions;
    /** Настройки счёта игрового времени */
    private ingameTimeSettings: IngameTimeSettings = DefaultTimeSettings;
    /** Прикреплённая симуляция */
    private simulation: TSimulation = null;
    /** Общая информация прохождения */
    public SAI: TSessionAssignmentInfo = null;
    private lastSAIPull: number = null;
    /** Набор назначений ПШЕ на задачу */
    private SWA: TSessionWorkerAssignment[] = [];
    /** Набор задач */
    public tasks: TSessionTaskExtended[] = [];
    /** Набор ПШЕ */
    public workers: TSessionWorkerExtended[] = [];
    /** Набор "всплывших" сценарных событий */
    public events: TSessionEvent[] = [];
    /** Записи Session Task Progress */
    public STP: TSessionTaskProgress[] = [];

    /// TODELETE
    private session: TSession = null;

    constructor() {
        makeAutoObservable(this);
    }

    /** Очистка хранилища */
    clearStore() {
        this.httpState = 'done';
        this.verbose = true;
        this.sessionAssignment = null;
        this.session = null;
        this.simulation = null;
        this.transportStatus = DefaultTransportStatus;
        this.managerManipulationAllowed = true;
        this.ingamePermissions = DefaultNoPermissions;
        this.ingameTimeSettings = DefaultTimeSettings;
        this.SAI = null;
        this.SWA = [];
        this.tasks = [];
        this.workers = [];
        this.events = [];
        this.STP = [];
    }

    /** Получение дампа */
    dump() {
        return {
            sideBarMinimized: this.sideBarMinimized,
            state: this.httpState,
            verbose: this.verbose,
            sessionAssignment: this.sessionAssignment,
            session: this.session,
            simulation: this.simulation,
            transportStatus: this.transportStatus,
            managerManipulationAllowed: this.managerManipulationAllowed,
            ingamePermissions: this.ingamePermissions,
            ingameTimeSettings: this.ingameTimeSettings,
            SAI: this.SAI,
            SWA: this.SWA,
            tasks: this.tasks,
            workers: this.workers,
            events: this.events,
            STP: this.STP,
        };
    }

    /// Методы инициализации

    /**
     * Инициализация прохождения
     * @param session_assignment_id ID назначения
     */
    async initSA(session_assignment_id: TSessionAssignment['id']) {
        this.httpState = 'loading';
        this.leftMenuScrollPosition = 0;
        try {
            const resultSA = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.getSessionAssignment(session_assignment_id);
            });
            if (resultSA.errorMessages) {
                message.error('Инициализация: ошибка получения назначения');
                throw resultSA.errorMessages;
            }
            this.sessionAssignment = resultSA.data.data;
            this.initPermissions();

            const resultSim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.SAIgetSimulation(session_assignment_id);
            });
            if (resultSim.errorMessages) {
                message.error('Инициализация: ошибка получения симуляции');
                throw resultSim.errorMessages;
            }
            this.simulation = resultSim.data.data;

            const probeSWA = await CRMAPIManager.request<SessionWorkerAssignmentListResp>(
                async (api) => {
                    return await api.getSessionWorkerAssignmentList({
                        session_assignment_id: this.sessionAssignment.id,
                        day: null,
                        page: 1,
                        per_page: 1,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            created_at: null,
                        },
                    });
                },
            );
            if (probeSWA.errorMessages) {
                message.error('Инициализация: ошибка получения назначений');
                throw probeSWA.errorMessages;
            }
            const resultSWA = await CRMAPIManager.request<SessionWorkerAssignmentListResp>(
                async (api) => {
                    return await api.getSessionWorkerAssignmentList({
                        session_assignment_id: this.sessionAssignment.id,
                        day: null,
                        page: 1,
                        per_page: probeSWA.data.meta.total,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            created_at: null,
                        },
                    });
                },
            );
            if (resultSWA.errorMessages) {
                message.error('Инициализация: ошибка получения назначений');
                throw resultSWA.errorMessages;
            }
            this.SWA = resultSWA.data.data;

            const resultWLE = await CRMAPIManager.request<SWExtListResp>(async (api) => {
                return await api.SAIgetWorkersExtended(session_assignment_id);
            });
            if (resultWLE.errorMessages) {
                message.error('Инициализация: ошибка получения ПШЕ');
                throw resultWLE.errorMessages;
            }
            this.workers = resultWLE.data.data;

            const resultTLE = await CRMAPIManager.request<STExtListResp>(async (api) => {
                return await api.SAIgetTasksExtended(session_assignment_id);
            });
            if (resultTLE.errorMessages) {
                message.error('Инициализация: ошибка получения задач');
                throw resultTLE.errorMessages;
            }
            this.tasks = resultTLE.data.data;

            const resultSAI = await CRMAPIManager.request<SAIResp>(async (api) => {
                return await api.SAIgetInfo(session_assignment_id);
            });
            if (resultSAI.errorMessages) {
                message.error('Инициализация: ошибка получения состояния симуляции');
                throw resultSAI.errorMessages;
            }
            this.SAI = resultSAI.data.data;

            this.events = DefaultSessionEvents(session_assignment_id);

            SettingsManager.updateConnectionCredentials({
                sessionAssignmentId: session_assignment_id,
            });
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Прохождение инициализировано');
            }
        } catch (errors) {
            message.error('Инициализация проведена с ошибками; попробуйте ещё раз');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    /** Выставление соответствующих прав */
    initPermissions() {
        const creds = SettingsManager.getConnectionCredentials();
        // Разрешаем CRUD сущностей только проходящему, либо по флагу - менеджеру
        const extendPerms =
            this.sessionAssignment.user_id == creds?.user_id ||
            (this.managerManipulationAllowed &&
                this.sessionAssignment.manager_id == creds?.user_id) ||
            rootStore.currentUserStore.getUser?.role == 'Roles.Admin';
        const stateActionPerms =
            this.sessionAssignment.manager_id == creds?.user_id ||
            rootStore.currentUserStore.getUser?.role == 'Roles.Admin';
        switch (this.sessionAssignment.state) {
            case 'prestarted': {
                this.ingamePermissions = {
                    ...this.ingamePermissions,
                    ...NoStatePermissions,
                    ...(extendPerms ? AllSWAPermissions : NoSWAPermissions),
                    allowStart:
                        Permissions.checkPermission(Permissions.SessionAssignmentStart) &&
                        stateActionPerms,
                };
                break;
            }
            case 'started': {
                this.ingamePermissions = {
                    ...this.ingamePermissions,
                    ...NoStatePermissions,
                    ...(extendPerms ? AllSWAPermissions : NoSWAPermissions),
                    allowPause:
                        Permissions.checkPermission(Permissions.SessionAssignmentPause) &&
                        stateActionPerms,
                    allowStop:
                        Permissions.checkPermission(Permissions.SessionAssignmentStop) &&
                        stateActionPerms,
                };
                break;
            }
            case 'paused': {
                this.ingamePermissions = {
                    ...this.ingamePermissions,
                    ...NoStatePermissions,
                    ...(extendPerms ? AllSWAPermissions : NoSWAPermissions),
                    allowResume:
                        Permissions.checkPermission(Permissions.SessionAssignmentResume) &&
                        stateActionPerms,
                    allowStop:
                        Permissions.checkPermission(Permissions.SessionAssignmentStop) &&
                        stateActionPerms,
                };
                break;
            }
            case 'resumed': {
                this.ingamePermissions = {
                    ...this.ingamePermissions,
                    ...NoStatePermissions,
                    ...(extendPerms ? AllSWAPermissions : NoSWAPermissions),
                    allowPause:
                        Permissions.checkPermission(Permissions.SessionAssignmentPause) &&
                        stateActionPerms,
                    allowStop:
                        Permissions.checkPermission(Permissions.SessionAssignmentStop) &&
                        stateActionPerms,
                };
                break;
            }
            case 'stopped': {
                this.ingamePermissions = {
                    ...this.ingamePermissions,
                    ...NoStatePermissions,
                    ...(extendPerms ? AllSWAPermissions : NoSWAPermissions),
                    allowResume:
                        Permissions.checkPermission(Permissions.SessionAssignmentResume) &&
                        stateActionPerms,
                };
                break;
            }
            case 'finished': {
                this.ingamePermissions = {
                    ...this.ingamePermissions,
                    ...NoStatePermissions,
                    ...NoSWAPermissions,
                };
                break;
            }
        }
    }

    /// Управление состоянием
    async stateStart() {
        if (!this.ingamePermissions.allowStart) {
            message.warning('Нет разрешения запускать прохождение');
            return;
        }
        this.httpState = 'loading';
        try {
            const stateResult = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SAstart(this.sessionAssignment.id);
            });
            if (stateResult.errorMessages) throw stateResult.errorMessages;
            this.sessionAssignment = stateResult.data.data;
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Прохождение запущено');
            }
        } catch (errors) {
            message.error('Ошибка при пуске прохождения');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async statePause() {
        if (!this.ingamePermissions.allowPause) {
            message.warning('Нет разрешения ставить прохождение на паузу');
            return;
        }
        this.httpState = 'loading';
        try {
            const stateResult = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SApause(this.sessionAssignment.id);
            });
            if (stateResult.errorMessages) throw stateResult.errorMessages;
            this.sessionAssignment = stateResult.data.data;
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Прохождение поставлено на паузу');
            }
        } catch (errors) {
            message.error('Ошибка при постановке прохождения на паузу');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async stateResume() {
        if (!this.ingamePermissions.allowResume) {
            message.warning('Нет разрешения возобновлять прохождение');
            return;
        }
        this.httpState = 'loading';
        try {
            const stateResult = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SAresume(this.sessionAssignment.id);
            });
            if (stateResult.errorMessages) throw stateResult.errorMessages;
            this.sessionAssignment = stateResult.data.data;
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Прохождение возобновлено');
            }
        } catch (errors) {
            message.error('Ошибка при возобновлении прохождения');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async stateStop() {
        if (!this.ingamePermissions.allowStop) {
            message.warning('Нет разрешения останавливать прохождение');
            return;
        }
        this.httpState = 'loading';
        try {
            const stateResult = await CRMAPIManager.request<SessionAssignmentResp>(async (api) => {
                return await api.SAstop(this.sessionAssignment.id);
            });
            if (stateResult.errorMessages) throw stateResult.errorMessages;
            this.sessionAssignment = stateResult.data.data;
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Прохождение остановлено');
            }
        } catch (errors) {
            message.error('Ошибка при остановке прохождения');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    // Fetch-функции
    async fetchSWAlist() {
        this.httpState = 'loading';
        try {
            const probeSWA = await CRMAPIManager.request<SessionWorkerAssignmentListResp>(
                async (api) => {
                    return await api.getSessionWorkerAssignmentList({
                        session_assignment_id: this.sessionAssignment.id,
                        day: null,
                        page: 1,
                        per_page: 1,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            created_at: null,
                        },
                    });
                },
            );
            if (probeSWA.errorMessages) {
                message.error('Инициализация: ошибка получения назначений');
                throw probeSWA.errorMessages;
            }
            const resultSWA = await CRMAPIManager.request<SessionWorkerAssignmentListResp>(
                async (api) => {
                    return await api.getSessionWorkerAssignmentList({
                        session_assignment_id: this.sessionAssignment.id,
                        day: null,
                        page: 1,
                        per_page: probeSWA.data.meta.total,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            created_at: null,
                        },
                    });
                },
            );
            if (resultSWA.errorMessages) {
                message.error('Инициализация: ошибка получения назначений');
                throw resultSWA.errorMessages;
            }
            this.SWA = resultSWA.data.data;
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Список назначений подгружен');
            }
        } catch (errors) {
            message.error('Ошибка при подгрузке списка назначений');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async fetchSWA(session_worker_assignment_id: TSessionWorkerAssignment['id']) {
        this.httpState = 'loading';
        try {
            const fetchResult = await CRMAPIManager.request<SessionWorkerAssignmentResp>(
                async (api) => {
                    return await api.getSessionWorkerAssignment(session_worker_assignment_id);
                },
            );
            if (fetchResult.errorMessages) throw fetchResult.errorMessages;
            this.SWA = this.SWA.map((swai) => {
                if (swai.id != session_worker_assignment_id) {
                    return swai;
                } else {
                    return { ...swai, ...fetchResult.data.data };
                }
            });
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Назначение подгружено');
            }
        } catch (errors) {
            message.error('Ошибка при подгрузке назначения');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async fetchCWT(worker_uid: TSessionWorker['worker_uid']) {
        this.httpState = 'loading';
        try {
            const fetchResult = await CRMAPIManager.request<CWTResp>(async (api) => {
                return await api.SAIgetCWT(this.sessionAssignment.id, worker_uid, null);
            });
            if (fetchResult.errorMessages) throw fetchResult.errorMessages;
            this.workers = this.workers.map((wi) => {
                if (wi.worker_uid != worker_uid) {
                    return wi;
                } else {
                    return { ...wi, ...fetchResult.data.data };
                }
            });
            this.httpState = 'done';
            if (this.verbose) {
                message.info('ПШЕ подгружен');
            }
        } catch (errors) {
            message.error('Ошибка при подгрузке ПШЕ');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async fetchCTW(task_uid: TSessionTask['task_uid']) {
        this.httpState = 'loading';
        try {
            const tempTask = this.tasks.find((tti) => tti.task_uid == task_uid);
            if (tempTask == undefined) {
                this.httpState = 'error';
                message.error('Задача не найдена');
                return;
            }
            const fetchResult = await CRMAPIManager.request<CTWResp>(async (api) => {
                return await api.SAIgetCTW(this.sessionAssignment.id, tempTask.order_id);
            });
            if (fetchResult.errorMessages) throw fetchResult.errorMessages;
            this.tasks = this.tasks.map((ti) => {
                if (ti.task_uid != task_uid) {
                    return ti;
                } else {
                    return { ...ti, ...fetchResult.data.data };
                }
            });
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Задача подгружена');
            }
        } catch (errors) {
            message.error('Ошибка при подгрузке задачи');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async fetchInfo(force = false) {
        if (this.lastSAIPull != null && Date.now() - this.lastSAIPull < 30 * 1000 && !force) {
            this.httpState = 'done';
            return;
        }
        this.httpState = 'loading';
        try {
            const fetchResult = await CRMAPIManager.request<SAIResp>(async (api) => {
                return await api.SAIgetInfo(this.sessionAssignment.id);
            });
            if (fetchResult.errorMessages) throw fetchResult.errorMessages;
            this.SAI = fetchResult.data.data;
            this.lastSAIPull = Date.now();
            this.initPermissions();
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Состояние подгружено');
            }
        } catch (errors) {
            message.error('Ошибка при подгрузке состояния');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async fetchWorkers() {
        this.httpState = 'loading';
        try {
            const fetchResult = await CRMAPIManager.request<SWExtListResp>(async (api) => {
                return await api.SAIgetWorkersExtended(this.sessionAssignment.id);
            });
            if (fetchResult.errorMessages) throw fetchResult.errorMessages;
            this.workers = fetchResult.data.data;
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Список ПШЕ подгружен');
            }
        } catch (errors) {
            message.error('Ошибка при подгрузке списка ПШЕ');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async fetchTasks() {
        this.httpState = 'loading';
        try {
            const fetchResult = await CRMAPIManager.request<STExtListResp>(async (api) => {
                return await api.SAIgetTasksExtended(this.sessionAssignment.id);
            });
            if (fetchResult.errorMessages) throw fetchResult.errorMessages;
            this.tasks = fetchResult.data.data.map((ti) => {
                return { ...ti, id: ti.order_id };
            });
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Список задач подгружен');
            }
        } catch (errors) {
            message.error('Ошибка при подгрузке списка задач');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async fetchSTP() {
        this.httpState = 'loading';
        try {
            const fetchResult = await CRMAPIManager.request<STPResp>(async (api) => {
                return await api.SAIgetSTP(this.sessionAssignment.id);
            });
            if (fetchResult.errorMessages) throw fetchResult.errorMessages;
            this.STP = fetchResult.data.data;
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Хронология изменения задач подгружена');
            }
        } catch (errors) {
            if (errors?.includes('Объект не найден') || errors?.includes('Not Found')) {
                this.STP = [];
                this.httpState = 'done';
                if (this.verbose) {
                    message.info('Хронология изменения задач подгружена');
                }
            } else {
                message.error('Ошибка при подгрузке хронологии изменения задач');
                console.log(errors);
                this.httpState = 'error';
            }
        }
    }

    // GET-функции (синхронные)
    getInitStatus() {
        return this.sessionAssignment != null && this.httpState != 'error';
    }

    getSessionAssignment() {
        return this.sessionAssignment;
    }

    getSession() {
        return this.session;
    }

    getSimulation() {
        return this.simulation;
    }

    getTimeSettings() {
        return this.ingameTimeSettings;
    }

    getPermissions() {
        return this.ingamePermissions;
    }

    getDayTick() {
        return { day: this.SAI?.config?.day, tick: this.SAI?.config?.tick };
    }

    getPreviousTaskByUid(
        task_uid: TSessionTaskExtended['task_uid'],
    ): TSessionTaskExtended['task_uid'] | null {
        const target = this.tasks.find((t) => t.task_uid == task_uid);
        if (target == undefined || target.order_id == 1) {
            return null;
        }
        const targetOrderId = target.order_id;
        let closestPreviousOrderId = 0;
        let closestPreviousTaskUid = null;
        const tempTasks = [...this.tasks].sort((a, b) => b.order_id - a.order_id);
        for (let i = 0; i < tempTasks.length; i++) {
            if (
                tempTasks[i].order_id > closestPreviousOrderId &&
                tempTasks[i].order_id < targetOrderId
            ) {
                closestPreviousOrderId = tempTasks[i].order_id;
                closestPreviousTaskUid = tempTasks[i].task_uid;
            }
        }
        if (closestPreviousOrderId == targetOrderId) {
            return null;
        } else {
            return closestPreviousTaskUid;
        }
    }

    getFollowingTaskByUid(
        task_uid: TSessionTaskExtended['task_uid'],
    ): TSessionTaskExtended['task_uid'] | null {
        const target = this.tasks.find((t) => t.task_uid == task_uid);
        if (target == undefined || target.order_id == this.tasks.length) {
            return null;
        }
        const targetOrderId = target.order_id;
        const tempTasks = [...this.tasks].sort((a, b) => a.order_id - b.order_id);
        let closestFollowingOrderId = tempTasks.length + 1;
        let closestFollowingTaskUid = null;
        for (let i = 0; i < tempTasks.length; i++) {
            if (
                tempTasks[i].order_id < closestFollowingOrderId &&
                tempTasks[i].order_id > targetOrderId
            ) {
                closestFollowingOrderId = tempTasks[i].order_id;
                closestFollowingTaskUid = tempTasks[i].task_uid;
            }
        }
        if (closestFollowingOrderId == targetOrderId) {
            return null;
        } else {
            return closestFollowingTaskUid;
        }
    }

    getPreviousWorkerByUid(
        worker_uid: TSessionWorkerExtended['worker_uid'],
    ): TSessionWorkerExtended['worker_uid'] | null {
        const target = this.workers.find((w) => w.worker_uid == worker_uid);
        if (target == undefined || target.order_id == 1) {
            return null;
        }
        const targetOrderId = target.order_id;
        let closestPreviousOrderId = 0;
        let closestPreviousTaskUid = null;
        const tempWorkers = [...this.workers].sort((a, b) => b.order_id - a.order_id);
        for (let i = 0; i < tempWorkers.length; i++) {
            if (
                tempWorkers[i].order_id > closestPreviousOrderId &&
                tempWorkers[i].order_id < targetOrderId
            ) {
                closestPreviousOrderId = tempWorkers[i].order_id;
                closestPreviousTaskUid = tempWorkers[i].worker_uid;
            }
        }
        if (closestPreviousOrderId == targetOrderId) {
            return null;
        } else {
            return closestPreviousTaskUid;
        }
    }

    getFollowingWorkerByUid(
        worker_uid: TSessionWorkerExtended['worker_uid'],
    ): TSessionWorkerExtended['worker_uid'] | null {
        const target = this.workers.find((w) => w.worker_uid == worker_uid);
        if (target == undefined || target.order_id == this.workers.length) {
            return null;
        }
        const targetOrderId = target.order_id;
        const tempWorkers = [...this.workers].sort((a, b) => a.order_id - b.order_id);
        let closestFollowingOrderId = tempWorkers.length + 1;
        let closestFollowingTaskUid = null;
        for (let i = 0; i < tempWorkers.length; i++) {
            if (
                tempWorkers[i].order_id < closestFollowingOrderId &&
                tempWorkers[i].order_id > targetOrderId
            ) {
                closestFollowingOrderId = tempWorkers[i].order_id;
                closestFollowingTaskUid = tempWorkers[i].worker_uid;
            }
        }
        if (closestFollowingOrderId == targetOrderId) {
            return null;
        } else {
            return closestFollowingTaskUid;
        }
    }

    /// GET-функции (асинхронные)
    async getTaskList() {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            await this.fetchTasks();
            await this.fetchInfo();
        }
        return this.tasks;
    }

    async getWorkerList() {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            await this.fetchWorkers();
            await this.fetchInfo();
        }
        return this.workers;
    }

    async getInfo(force = false) {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            await this.fetchInfo(force);
        }
        return this.SAI;
    }

    async getSWA() {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            await this.fetchSWAlist();
        }
        return this.SWA;
    }

    async getTask(task_uid: TSessionTask['task_uid']) {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            await this.fetchCTW(task_uid);
        }
        return this.tasks.find((ti) => ti.task_uid == task_uid);
    }

    async getWorker(worker_uid: TSessionWorker['worker_uid']) {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            await this.fetchCWT(worker_uid);
        }
        return this.workers.find((wi) => wi.worker_uid == worker_uid);
    }

    async getBudgetData(): Promise<TSessionBudget> {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            await this.fetchTasks();
        }
        if (this.httpState == 'error') {
            return [];
        }
        const budget: TSessionBudget = [];
        let totalProgress = 0;
        let totalBudgetCurrent = 0;
        let totalBudgetPrediction = 0;
        const tempSTE = [...this.tasks];
        for (let i = 0; i < tempSTE.length; i++) {
            const taskSWAlinks = await this.getTaskSWAlinks(tempSTE[i].order_id);
            const budgetRecord: TSessionBudgetRecord = {
                budget_current: tempSTE[i].budget_current,
                budget_prediction:
                    tempSTE[i].predicted_end_budget == null
                        ? tempSTE[i].est_budget
                        : tempSTE[i].predicted_end_budget,
                budget_plan: tempSTE[i].est_budget,
                current_weight: tempSTE[i].current_weight,
                progress: tempSTE[i].progress,
                task_uid: tempSTE[i].task_uid,
                title: `${i + 1}. ${tempSTE[i].name}`,
                type: 'task',
                workers: Object.values(taskSWAlinks)
                    .filter(
                        (swali) =>
                            swali != null &&
                            (tempSTE[i].end_day != null || swali.status != SWAlinkStatus.Previous),
                    )
                    .map((swali) => {
                        const worker = this.workers.find((wi) => wi.worker_uid == swali.worker_uid);
                        if (worker == undefined) {
                            return { name: null, worker_uid: null };
                        }
                        return {
                            name: `${worker.name}${
                                swali.status == SWAlinkStatus.AwaitingAssign ? ' (завтра)' : ''
                            }${swali.status == SWAlinkStatus.AwaitingCancel ? ' (сегодня)' : ''}`,
                            worker_uid: worker.worker_uid,
                        };
                    }),
            };
            if (budgetRecord.progress != 0 && budgetRecord.budget_current != 0) {
                const estTotalHours =
                    tempSTE[i].est_duration *
                    (this.ingameTimeSettings.workDayHours -
                        +this.ingameTimeSettings.workDayLunchSkip);
                const spentHours = Math.ceil(
                    estTotalHours * (budgetRecord.progress / budgetRecord.current_weight),
                );
                const leftoverHours = estTotalHours - spentHours;
                const spentBudgetPerHour = budgetRecord.budget_current / spentHours;
                const leftoverSpendingPrediction = Math.ceil(leftoverHours * spentBudgetPerHour);
                budgetRecord.budget_prediction =
                    budgetRecord.budget_current + leftoverSpendingPrediction;
            }
            totalProgress +=
                budgetRecord.progress == 0
                    ? 0
                    : Math.floor((budgetRecord.progress / budgetRecord.current_weight) * 100) / 100;
            totalBudgetCurrent += budgetRecord.budget_current;
            totalBudgetPrediction += budgetRecord.budget_prediction;
            budget.push(budgetRecord);
        }

        const tempSim = this.simulation;
        budget.push({
            budget_current: 0,
            budget_prediction: tempSim.hardware_budget,
            budget_plan: tempSim.hardware_budget,
            current_weight: 0,
            progress: 0,
            task_uid: null,
            title: 'Оборудование',
            type: 'hardware',
            workers: [],
        });
        totalBudgetCurrent += 0;
        totalBudgetPrediction += tempSim.hardware_budget;

        budget.push({
            budget_current: 0,
            budget_prediction: tempSim.other_budget,
            budget_plan: tempSim.other_budget,
            current_weight: 0,
            progress: 0,
            task_uid: null,
            title: 'Прочее',
            type: 'other',
            workers: [],
        });
        totalBudgetCurrent += 0;
        totalBudgetPrediction += tempSim.other_budget;

        budget.push({
            budget_current: totalBudgetCurrent,
            budget_prediction: totalBudgetPrediction,
            budget_plan: this.simulation.total_budget,
            current_weight: 0,
            progress: totalProgress / tempSTE.length,
            task_uid: null,
            title: 'Итого',
            type: 'total',
            workers: [],
        });
        return budget;
    }

    async getEventList() {
        return this.events;
    }

    async getSTP() {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            await this.fetchSTP();
        }
        return this.STP;
    }

    async getTotalSWAlinks() {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            //await this.fetchSWAlist();
        }
        const tTasks = this.tasks;
        const outputLinks: IngameTotalSWAlinks = {};
        for (let i = 0; i < tTasks.length; i++) {
            const curTask = tTasks[i];
            const curTaskLinks = await this.getTaskSWAlinks(curTask.order_id, true);
            outputLinks[curTask.order_id] = curTaskLinks;
        }
        return outputLinks;
    }

    async getWorkerSWAlinks(worker_uid: TSessionWorker['worker_uid']) {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload) {
            //await this.fetchSWAlist();
        }
        const SAIstatus = this.SAI.config.state;
        const tTasks = this.tasks;
        const tWorkerSWA = this.SWA.filter((swai) => swai.worker_uid == worker_uid);
        const curDay = this.SAI.config.day;
        const outputLinks: IngameWorkerSWAlinks = {};
        // Собираем статусы по всем задачам
        for (let i = 0; i < tTasks.length; i++) {
            const curTask = tTasks[i];
            const curTaskWorkerSWA = tWorkerSWA.filter(
                (twswai) => twswai.task_id == curTask.order_id,
            );
            if (curTaskWorkerSWA.length == 0) {
                // Если на задаче не было этого ПШЕ
                outputLinks[curTask.order_id] = null;
            } else {
                if (SAIstatus == 'prestarted') {
                    // На предстарте нет задержки - назначение/снятие сразу
                    const lastCurTaskWorkerSWA = curTaskWorkerSWA[curTaskWorkerSWA.length - 1];
                    if (
                        lastCurTaskWorkerSWA.action == 'assign' ||
                        lastCurTaskWorkerSWA.action == 'prioritize'
                    ) {
                        outputLinks[curTask.order_id] = {
                            task_id: curTask.order_id,
                            worker_uid,
                            status: SWAlinkStatus.Current,
                            week: 1,
                            day: 1,
                            tick: 0,
                            priority: lastCurTaskWorkerSWA.priority,
                        };
                    } else {
                        outputLinks[curTask.order_id] = null;
                    }
                } else {
                    // Смотрим действие последнего SWA этой задачи и этого ПШЕ
                    const curRecord = curTaskWorkerSWA[curTaskWorkerSWA.length - 1];
                    if (curRecord.action == 'task-end') {
                        outputLinks[curTask.order_id] = {
                            task_id: curTask.order_id,
                            worker_uid,
                            status: SWAlinkStatus.Previous,
                            week: curRecord.week,
                            day: curRecord.day,
                            tick: curRecord.tick,
                            priority: curRecord.priority,
                        };
                    } else if (curRecord.action == 'assign' || curRecord.action == 'prioritize') {
                        // Если числится в current_workers - уже работает, иначе подлкючится позже
                        outputLinks[curTask.order_id] = {
                            task_id: curTask.order_id,
                            worker_uid,
                            status:
                                curRecord.day < curDay
                                    ? SWAlinkStatus.Current
                                    : curTask.current_workers.includes(worker_uid)
                                      ? SWAlinkStatus.Current
                                      : SWAlinkStatus.AwaitingAssign,
                            week: curRecord.week,
                            day: curRecord.day,
                            tick: curRecord.tick,
                            priority: curRecord.priority,
                        };
                    } else {
                        const foundAssign = Object.values(curTaskWorkerSWA).find(
                            (swali) =>
                                swali != null && swali.action == 'assign' && swali.day == curDay,
                        );
                        // Если числится в current_workers - пока работает, иначе отключится позже
                        outputLinks[curTask.order_id] = {
                            task_id: curTask.order_id,
                            worker_uid,
                            status:
                                curRecord.day == curDay &&
                                (foundAssign == undefined ||
                                    (foundAssign.day == 1 && foundAssign.tick == 0)) &&
                                curTask.start_day != null
                                    ? SWAlinkStatus.AwaitingCancel
                                    : SWAlinkStatus.Previous,
                            week: curRecord.week,
                            day: curRecord.day,
                            tick: curRecord.tick,
                            priority: curRecord.priority,
                        };
                    }
                }
            }
        }

        return outputLinks;
    }

    async getTaskSWAlinks(task_id: TSessionTask['order_id'], noReload = false) {
        const shouldReload = this.transportStatus.primaryTransport == 'HTTP';
        if (shouldReload && !noReload) {
            //await this.fetchSWAlist();
        }
        const SAIstatus = this.SAI.config.state;
        const tWorkers = this.workers;
        const tTaskSWA = this.SWA.filter((swai) => swai.task_id == task_id);
        const tTask = this.tasks.find((ti) => ti.order_id == task_id);
        const curDay = this.SAI.config.day;
        const outputLinks: IngameTaskSWAlinks = {};
        // Собираем статусы по всем ПШЕ
        for (let i = 0; i < tWorkers.length; i++) {
            const curWorker = tWorkers[i];
            const curWorkerTaskSWA = tTaskSWA.filter(
                (ttswai) => ttswai.worker_uid == curWorker.worker_uid,
            );
            if (curWorkerTaskSWA.length == 0) {
                // Если ПШЕ не был назначен на эту задачу
                outputLinks[curWorker.worker_uid] = null;
            } else {
                if (SAIstatus == 'prestarted') {
                    // На предстарте нет задержки - назначение/снятие сразу
                    const lastCurWorkerTaskSWA = curWorkerTaskSWA[curWorkerTaskSWA.length - 1];
                    if (
                        lastCurWorkerTaskSWA.action == 'assign' ||
                        lastCurWorkerTaskSWA.action == 'prioritize'
                    ) {
                        outputLinks[curWorker.worker_uid] = {
                            task_id,
                            worker_uid: curWorker.worker_uid,
                            status: SWAlinkStatus.Current,
                            week: 1,
                            day: 1,
                            tick: 0,
                            priority: lastCurWorkerTaskSWA.priority,
                        };
                    } else {
                        outputLinks[curWorker.worker_uid] = null;
                    }
                } else {
                    // Смотрим действие последнего SWA этой задачи и этого ПШЕ
                    const curRecord = curWorkerTaskSWA[curWorkerTaskSWA.length - 1];
                    if (curRecord.action == 'task-end') {
                        outputLinks[curWorker.worker_uid] = {
                            task_id,
                            worker_uid: curWorker.worker_uid,
                            status: SWAlinkStatus.Previous,
                            week: curRecord.week,
                            day: curRecord.day,
                            tick: curRecord.tick,
                            priority: curRecord.priority,
                        };
                    } else if (curRecord.action == 'assign' || curRecord.action == 'prioritize') {
                        // Если числится в current_workers - уже работает, иначе подлкючится позже
                        outputLinks[curWorker.worker_uid] = {
                            task_id,
                            worker_uid: curWorker.worker_uid,
                            status:
                                curRecord.day < curDay
                                    ? SWAlinkStatus.Current
                                    : curWorker.current_tasks.includes(task_id)
                                      ? SWAlinkStatus.Current
                                      : SWAlinkStatus.AwaitingAssign,
                            week: curRecord.week,
                            day: curRecord.day,
                            tick: curRecord.tick,
                            priority: curRecord.priority,
                        };
                    } else {
                        const foundAssign = Object.values(curWorkerTaskSWA).find(
                            (swali) =>
                                swali != null && swali.action == 'assign' && swali.day == curDay,
                        );
                        // Если числится в current_workers - пока работает, иначе отключится позже
                        outputLinks[curWorker.worker_uid] = {
                            task_id,
                            worker_uid: curWorker.worker_uid,
                            status:
                                curRecord.day == curDay &&
                                (foundAssign == undefined ||
                                    (foundAssign.day == 1 && foundAssign.tick == 0)) &&
                                tTask.start_day != null
                                    ? SWAlinkStatus.AwaitingCancel
                                    : SWAlinkStatus.Previous,
                            week: curRecord.week,
                            day: curRecord.day,
                            tick: curRecord.tick,
                            priority: curRecord.priority,
                        };
                    }
                }
            }
        }

        return outputLinks;
    }

    /// SWA-проверки
    allowSWAactions(task_uid: TSessionTask['task_uid']) {
        const task = this.tasks.find((ti) => ti.task_uid == task_uid);
        if (task.end_day != null || task.end_tick != null) {
            return false;
        }
        return true;
    }

    allowSWAassign(task_uid: TSessionTask['task_uid']) {
        if (!this.allowSWAactions(task_uid)) {
            return false;
        }
        return this.ingamePermissions.workerAssignTask;
    }

    allowSWAcancel(task_uid: TSessionTask['task_uid']) {
        if (!this.allowSWAactions(task_uid)) {
            return false;
        }
        return this.ingamePermissions.workerCancelTask;
    }

    allowSWAprioritize(task_uid: TSessionTask['task_uid']) {
        if (!this.allowSWAactions(task_uid)) {
            return false;
        }
        return this.ingamePermissions.workerPrioritizeTask;
    }

    /// SWA-запросы
    async SWAassign(task_uid: TSessionTask['task_uid'], worker_uid: TSessionWorker['worker_uid']) {
        // Права есть?
        if (!this.allowSWAassign(task_uid)) {
            message.error('Нельзя назначить на эту задачу');
            return;
        }
        // CTW числится?
        const task = this.tasks.find((ti) => ti.task_uid == task_uid);
        /* Временно отключено, т.к. CTW/CWT не отражают отложенные операции
        if (task.current_workers.includes(worker_uid)) {
            message.error("ПШЕ уже назначен");
            return;
        }*/
        try {
            this.httpState = 'loading';
            const result = await CRMAPIManager.request<SessionWorkerAssignmentResp>(async (api) => {
                return await api.SWAassign(this.sessionAssignment.id, worker_uid, task.order_id);
            });
            if (result.errorMessages) throw result.errorMessages;
            this.SWA = [...this.SWA, result.data.data];
            await this.fetchCTW(task_uid);
            await this.fetchCWT(worker_uid);
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Добавлено назначение ПШЕ');
            }
        } catch (errors) {
            message.error('Ошибка при назначении ПШЕ');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async SWAcancel(task_uid: TSessionTask['task_uid'], worker_uid: TSessionWorker['worker_uid']) {
        // Права есть?
        if (!this.allowSWAcancel(task_uid)) {
            message.error('Нельзя отменить назначение на эту задачу');
            return;
        }
        // СTW числится?
        const task = this.tasks.find((ti) => ti.task_uid == task_uid);
        /* Временно отключено, т.к. CTW/CWT не отражают отложенные операции
        if (!task.current_workers.includes(worker_uid)) {
            message.error("Нет назначения ПШЕ на эту задачу");
            return;
        }*/
        // SWA присутствует?
        const taskSWA = this.SWA.filter(
            (swai) => swai.task_id == task.order_id && swai.worker_uid == worker_uid,
        );
        if (
            taskSWA.length == 0 ||
            (taskSWA.length > 0 && taskSWA[taskSWA.length - 1].action == 'cancel')
        ) {
            message.error('Нет назначения ПШЕ на эту задачу');
            return;
        }
        try {
            this.httpState = 'loading';
            const result = await CRMAPIManager.request<SessionWorkerAssignmentResp>(async (api) => {
                return await api.SWAcancel(
                    taskSWA[taskSWA.length - 1].id,
                    this.sessionAssignment.id,
                );
            });
            if (result.errorMessages) throw result.errorMessages;
            this.SWA = [...this.SWA, result.data.data];
            await this.fetchCTW(task_uid);
            await this.fetchCWT(worker_uid);
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Отменено назначение ПШЕ');
            }
        } catch (errors) {
            message.error('Ошибка при отмене назначения ПШЕ');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    async SWAprioritize(
        task_uid: TSessionTask['task_uid'],
        worker_uid: TSessionWorker['worker_uid'],
    ) {
        // Права есть?
        if (!this.allowSWAprioritize(task_uid)) {
            message.error('Нельзя повысить приоритет этого назначения');
            return;
        }
        // СTW числится?
        const task = this.tasks.find((ti) => ti.task_uid == task_uid);
        /* Временно отключено, т.к. CTW/CWT не отражают отложенные операции
        if (!task.current_workers.includes(worker_uid)) {
            message.error("Нет назначения ПШЕ на эту задачу");
            return;
        }*/
        // SWA присутствует?
        const taskSWA = this.SWA.filter(
            (swai) => swai.task_id == task.id && swai.worker_uid == worker_uid,
        );
        if (
            taskSWA.length == 0 ||
            (taskSWA.length > 0 && taskSWA[taskSWA.length - 1].action == 'cancel')
        ) {
            message.error('Нет назначения ПШЕ на эту задачу');
            return;
        }
        try {
            this.httpState = 'loading';
            const result = await CRMAPIManager.request<SessionWorkerAssignmentResp>(async (api) => {
                return await api.SWAprioritize(
                    taskSWA[taskSWA.length - 1].id,
                    this.sessionAssignment.id,
                );
            });
            if (result.errorMessages) throw result.errorMessages;
            this.SWA = [...this.SWA, result.data.data];
            await this.fetchCTW(task_uid);
            await this.fetchCWT(worker_uid);
            this.httpState = 'done';
            if (this.verbose) {
                message.info('Повышен приоритет назначения ПШЕ');
            }
        } catch (errors) {
            message.error('Ошибка при повышении приоритета назначения ПШЕ');
            console.log(errors);
            this.httpState = 'error';
        }
    }

    // Интерфейсные функции
    dayTickToString(
        sessionAssignment: TSessionAssignment,
        format: 'hour-weekday-week' = 'hour-weekday-week',
    ) {
        if (sessionAssignment == null) {
            return '-';
        }
        if (this.ingameTimeSettings == null) {
            this.ingameTimeSettings = DefaultTimeSettings;
        }
        const day = sessionAssignment.day - 1;
        const tick = sessionAssignment.tick;
        const weekPure = Math.ceil((day + 1) / this.ingameTimeSettings.daysInAWeek);
        const weekdayPure = day % this.ingameTimeSettings.daysInAWeek;
        const weekdayFull = WeekdayStringsFull[weekdayPure];
        //const weekdayShort = WeekdayStringsShort[weekdayPure-1];
        const hourPure = Math.ceil(tick / this.ingameTimeSettings.ticksInAnHour);
        const middleHour =
            this.ingameTimeSettings.workDayStart +
            Math.ceil(this.ingameTimeSettings.workDayHours / 2);
        const hourIsAfterLunch = hourPure + this.ingameTimeSettings.workDayStart > middleHour;
        const hourFinal = this.ingameTimeSettings.workDayStart + hourPure + +hourIsAfterLunch;
        let result = '';
        switch (format) {
            case 'hour-weekday-week': {
                result = `${hourFinal}:${tick % 2 == 1 ? '30' : '00'} | ${weekdayFull} | Неделя ${weekPure}`;
                break;
            }
        }
        return result;
    }

    infoDayTickToString(format: 'hour-weekday-week' = 'hour-weekday-week') {
        if (this.SAI == null) {
            return '-';
        }
        if (this.ingameTimeSettings == null) {
            this.ingameTimeSettings = DefaultTimeSettings;
        }
        const day = this.SAI.config.day - 1;
        const tick = this.SAI.config.tick;
        const weekPure = Math.ceil((day + 1) / this.ingameTimeSettings.daysInAWeek);
        const weekdayPure = day % this.ingameTimeSettings.daysInAWeek;
        const weekdayFull = WeekdayStringsFull[weekdayPure];
        //const weekdayShort = WeekdayStringsShort[weekdayPure-1];
        const hourPure = Math.ceil(tick / this.ingameTimeSettings.ticksInAnHour);
        const middleHour =
            this.ingameTimeSettings.workDayStart +
            Math.ceil(this.ingameTimeSettings.workDayHours / 2);
        const hourIsAfterLunch = hourPure + this.ingameTimeSettings.workDayStart > middleHour;
        const hourFinal = this.ingameTimeSettings.workDayStart + hourPure + +hourIsAfterLunch;
        let result = '';
        switch (format) {
            case 'hour-weekday-week': {
                result = `${hourFinal}:${tick % 2 == 1 ? '30' : '00'} | ${weekdayFull} | Неделя ${weekPure}`;
                break;
            }
        }
        return result;
    }

    taskStartToEnd(task: TSessionTaskExtended): string {
        const { start_day, start_tick, end_day, end_tick } = task;
        if (start_day == null && start_tick == null) {
            return 'Ещё не началась';
        }
        const startWeekPure = Math.ceil(start_day / this.ingameTimeSettings.daysInAWeek);
        const startWeekdayPure = start_day % this.ingameTimeSettings.daysInAWeek;
        const startWeekdayShort = WeekdayStringsShort[startWeekdayPure - 1];
        const startHourPure = Math.ceil(start_tick / this.ingameTimeSettings.ticksInAnHour);
        const middleHour =
            this.ingameTimeSettings.workDayStart +
            Math.ceil(this.ingameTimeSettings.workDayHours / 2);
        const startHourIsAfterLunch =
            startHourPure + this.ingameTimeSettings.workDayStart > middleHour;
        const startHourFinal =
            this.ingameTimeSettings.workDayStart + startHourPure + +startHourIsAfterLunch;
        let result = `Начата: ${startWeekPure} нед ${startWeekdayShort} ${startHourFinal}:00`;
        if (end_day == null && end_tick == null) {
            result += '; и идёт.';
            return result;
        } else {
            const endWeekPure = Math.ceil(end_day / this.ingameTimeSettings.daysInAWeek);
            const endWeekdayPure = end_day % this.ingameTimeSettings.daysInAWeek;
            const endWeekdayShort = WeekdayStringsShort[endWeekdayPure - 1];
            const endHourPure = Math.ceil(end_tick / this.ingameTimeSettings.ticksInAnHour);
            const endHourIsAfterLunch =
                endHourPure + this.ingameTimeSettings.workDayStart > middleHour;
            const endHourFinal =
                this.ingameTimeSettings.workDayStart + endHourPure + +endHourIsAfterLunch;
            result += `; завершилась ${endWeekPure} нед ${endWeekdayShort} ${endHourFinal}:00.`;
            return result;
        }
    }
}

export { IngameStore };
