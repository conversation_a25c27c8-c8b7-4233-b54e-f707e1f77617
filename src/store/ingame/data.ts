import { Common } from 'src/shared/common';
import { IngamePermissions, IngameTimeSettings, TransportStatus } from 'types/ingame';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import { TSessionEvent } from 'types/session/sessionEvent';

export const DefaultTransportStatus: TransportStatus = {
    primaryTransport: 'HTTP',
};

export const NoStatePermissions: Pick<
    IngamePermissions,
    'allowStart' | 'allowPause' | 'allowResume' | 'allowStop' | 'allowFinish'
> = {
    allowStart: true,
    allowPause: true,
    allowResume: true,
    allowStop: true,
    allowFinish: true,
};

export const AllSWAPermissions: Pick<
    IngamePermissions,
    'workerAssignTask' | 'workerCancelTask' | 'workerPrioritizeTask'
> = {
    workerAssignTask: true,
    workerCancelTask: true,
    workerPrioritizeTask: true,
};

export const NoSWAPermissions: Pick<
    IngamePermissions,
    'workerAssignTask' | 'workerCancelTask' | 'workerPrioritizeTask'
> = {
    workerAssignTask: false,
    workerCancelTask: false,
    workerPrioritizeTask: false,
};

export const DefaultNoPermissions: IngamePermissions = {
    ...NoStatePermissions,
    ...NoSWAPermissions,
    globalReadonly: true,
    eventTopMost: false,
};

export const DefaultAllPermissions: IngamePermissions = {
    ...NoStatePermissions,
    ...AllSWAPermissions,
    globalReadonly: false,
    eventTopMost: false,
};

export const DefaultClientPermissions: IngamePermissions = DefaultAllPermissions;

export const DefaultManagerPermissions: IngamePermissions = DefaultNoPermissions;

export const WeekdayStringsFull = [
    'Понедельник',
    'Вторник',
    'Среда',
    'Четверг',
    'Пятница',
    'Суббота',
    'Воскресенье',
];

export const WeekdayStringsShort = [
    'Пн',
    'Вт',
    'Ср',
    'Чт',
    'Пт',
    'Сб',
    'Вс',
];

export const DefaultTimeSettings: IngameTimeSettings = {
    daysInAWeek: 5,
    workDayHours: 9,
    workDayStart: 8,
    workDayEnd: 17,
    workDayLunchSkip: true,
    ticksInAnHour: 2,
    calendarDayHours: 5,
    calendarDayStart: 8,
    calendarDayEnd: 20,
};

export const DefaultSessionEvents = (session_assignment_id: TSessionAssignment['id']) => {
    const tempSElist: TSessionEvent[] = [
        {
            session_assignment_id: session_assignment_id,
            id: '1',
            simulation_event_uid: '090e6002-5170-4b0c-a822-8a6cc50f1625',
            status: 'activated',
            simulation_event_sender_id: 'team',
            simulation_event_title: 'Электронные письма вместо собраний',
            simulation_event_description:
                'Ваши сотрудники предлагают заменить собрания электронными сообщениями.',
            day: 1,
            tick: 0,
            option_picked_day: null,
            option_picked_tick: null,
            option_picked_uid: null,
            options: [
                {
                    session_assignment_id: session_assignment_id,
                    id: '1',
                    session_event_uid: '1',
                    simulation_event_uid: '090e6002-5170-4b0c-a822-8a6cc50f1625',
                    simulation_event_option_uid: '0d2fbd24-6c0d-4a29-ae69-5a9f3fc42e22',
                    session_event_text: 'Я согласен, неплохая идея.',
                    session_event_reply:
                        'Нет, это не очень удачная мысль. Непосредственное общение между руководителем проекта и его командой нельзя заменить электронной перепиской. С другой стороны электронные сообщения можно использовать для подготовки собраний, например, давая знать заранее о повестке дня.',
                    session_event_reactions: [],
                    created_at: Common.dateNowString(),
                    updated_at: null,
                    deleted_at: null,
                },
                {
                    session_assignment_id: session_assignment_id,
                    id: '2',
                    session_event_uid: '1',
                    simulation_event_uid: '090e6002-5170-4b0c-a822-8a6cc50f1625',
                    simulation_event_option_uid: 'a8703306-8fc1-4100-a38c-7618b4a0daba',
                    session_event_text:
                        'Нет. Однако мы могли бы подготавливать собрания с помощью электронных сообщений, например, давая знать заранее о повестке дня.',
                    session_event_reply:
                        'Хорошая идея. Непосредственное общение между руководителем проекта и его командой нельзя заменить электронной перепиской. На многих предприятиях рассылают меморандум, чтобы подготовить собрание. Это пользуется успехом.',
                    session_event_reactions: [],
                    created_at: Common.dateNowString(),
                    updated_at: null,
                    deleted_at: null,
                },
                {
                    session_assignment_id: session_assignment_id,
                    id: '3',
                    session_event_uid: '1',
                    simulation_event_uid: '090e6002-5170-4b0c-a822-8a6cc50f1625',
                    simulation_event_option_uid: '455085a1-0041-494c-a17b-266e6b29e7a6',
                    session_event_text:
                        'Нет, я настаиваю на том, чтобы проводить собрания хотя бы раз в неделю.',
                    session_event_reply:
                        'Хорошая идея. Живое общение между руководителем проекта и его командой нельзя заменить электронной перепиской.',
                    session_event_reactions: [],
                    created_at: Common.dateNowString(),
                    updated_at: null,
                    deleted_at: null,
                },
            ],
            created_at: Common.dateNowString(),
            updated_at: null,
            deleted_at: null,
        },
        {
            session_assignment_id: session_assignment_id,
            id: '2',
            simulation_event_uid: 'd8b33f1b-8e40-4539-8f51-e7a262ff0b14',
            status: 'activated',
            simulation_event_sender_id: 'team',
            simulation_event_title: 'Роман: выпуск продукта задерживается',
            simulation_event_description:
                'Специалист, отвечающий за отношения с застройщиками, сообщил, что старт кампании на вашей целевой территории придется отложить на две недели из-за того, что местный основной застройщик решил объединить Вашу кампанию выпуска с его собственной маркетинговой кампанией.',
            day: 5,
            tick: 0,
            option_picked_day: null,
            option_picked_tick: null,
            option_picked_uid: null,
            options: [
                {
                    session_assignment_id: session_assignment_id,
                    id: '1',
                    session_event_uid: '2',
                    simulation_event_uid: 'd8b33f1b-8e40-4539-8f51-e7a262ff0b14',
                    simulation_event_option_uid: '11135207-07fc-407d-9c1e-172c4255f1ea',
                    session_event_text:
                        'Я предпочитаю сохранить эту информацию в секрете - я знаю, что имеющееся время будет так или иначе использовано для выпуска продукта на другие территории.',
                    session_event_reply:
                        'Рискованно. Вы ведёте опасную игру. Подумали ли вы о том, что может произойти, когда после всех дополнительных часов работы ваши сотрудники узнают о том, что на самом деле у них было 2 недели в резерве. Купите себе на всякий случай бронежилет, он может вам очень пригодиться в данной ситуации...',
                    session_event_reactions: [
                        {
                            reaction_sender_id: 'team',
                            reaction_text:
                                'Отлично! Всем известно, что срок выпуска был отложен на  неделю, а ты нам об этом ни слова. Тебе что, на нас наплевать? За дураков нас держишь?',
                        },
                    ],
                    created_at: Common.dateNowString(),
                    updated_at: null,
                    deleted_at: null,
                },
                {
                    session_assignment_id: session_assignment_id,
                    id: '2',
                    session_event_uid: '2',
                    simulation_event_uid: 'd8b33f1b-8e40-4539-8f51-e7a262ff0b14',
                    simulation_event_option_uid: 'a3a6289a-65a7-4843-afc9-5307e7e7271b',
                    session_event_text:
                        'Я поставлю в известность только основных членов команды, так как не считаю, что распространяться об этой информации всем сотрудникам - хорошая идея.',
                    session_event_reply:
                        'Рискованно. Вы ведёте опасную игру. Подумали ли вы о том, что может произойти, когда после всех дополнительных часов работы ваши сотрудники узнают о том, что на самом деле у них было 2 недели в резерве. Купите себе на всякий случай бронежилет, он может вам очень пригодиться в данной ситуации...',
                    session_event_reactions: [
                        {
                            reaction_sender_id: 'team',
                            reaction_text:
                                'Отлично! Всем известно, что срок выпуска был отложен на  неделю, а ты нам об этом ни слова. Тебе что, на нас наплевать? За дураков нас держишь?',
                        },
                    ],
                    created_at: Common.dateNowString(),
                    updated_at: null,
                    deleted_at: null,
                },
                {
                    session_assignment_id: session_assignment_id,
                    id: '3',
                    session_event_uid: '2',
                    simulation_event_uid: 'd8b33f1b-8e40-4539-8f51-e7a262ff0b14',
                    simulation_event_option_uid: '77f03c38-4662-46ef-8e96-510183e11f49',
                    session_event_text:
                        'Я скажу своей команде, что у нас есть еще 1 неделя, чтобы подготовить запуск продукта "Ключ".',
                    session_event_reply:
                        'Замечательное решение. Информация должна быть доступна всем. С сотрудниками нужно обращаться, как со взрослыми, ничего от них не скрывать. Тогда вы окажитесь в более выгодной для вас ситуации.',
                    session_event_reactions: [],
                    created_at: Common.dateNowString(),
                    updated_at: null,
                    deleted_at: null,
                },
            ],
            created_at: Common.dateNowString(),
            updated_at: null,
            deleted_at: null,
        },
    ];

    return tempSElist;
};
