import { createRoot } from 'react-dom/client';
import { App } from '@components/app';
import { GlobalConstants } from 'src/shared/constants';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import '@styles/style.scss';

export const render = () => {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    const container = document.getElementById(GlobalConstants.MainRoot);
    const root = createRoot(container);
    root.render(<App />);
};

render();
